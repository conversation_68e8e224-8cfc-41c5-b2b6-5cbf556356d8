import { createTheme, MantineColorsTuple } from '@mantine/core';

/**
 * BULLETPROOF MANTINE THEME FOR WORDPRESS PLUGIN
 *
 * This theme is designed to work seamlessly with our WordPress plugin CSS architecture.
 * It uses CSS custom properties that are defined in our bulletproof CSS files.
 *
 * Key principles:
 * 1. All colors reference CSS custom properties from our plugin namespace
 * 2. No hardcoded colors - everything is themeable
 * 3. Consistent with our bulletproof CSS architecture
 * 4. WordPress theme compatibility through CSS variables
 */

// Define our primary color tuple using CSS custom properties
const primaryColor: MantineColorsTuple = [
  'var(--limo-primary-light)',
  'var(--limo-primary-light)',
  'var(--limo-primary-light)',
  'var(--limo-primary-light)',
  'var(--limo-primary-light)',
  'var(--limo-primary)',
  'var(--limo-primary)',
  'var(--limo-primary-dark)',
  'var(--limo-primary-dark)',
  'var(--limo-primary-dark)',
];

export const wordPressMantineTheme = createTheme({
  /** Primary color configuration */
  primaryColor: 'primary',
  colors: {
    primary: primaryColor,
  },

  /** Typography */
  fontFamily: 'Raleway, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif',
  fontFamilyMonospace: 'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
  headings: {
    fontFamily: 'Raleway, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif',
    fontWeight: '600',
  },

  /** Default radius */
  defaultRadius: 'md',

  /** Component-specific styling */
  components: {
    TextInput: {
      styles: {
        input: {
          backgroundColor: 'var(--limo-surface)',
          borderColor: 'var(--limo-border)',
          color: 'var(--limo-text-primary)',
          '&:focus': {
            borderColor: 'var(--limo-primary)',
            boxShadow: '0 0 0 0.125rem var(--limo-primary-transparent)',
          },
          '&::placeholder': {
            color: 'var(--limo-text-muted)',
          }
        },
        label: {
          color: 'var(--limo-text-primary)',
          fontWeight: 500,
        },
      }
    },

    Textarea: {
      styles: {
        input: {
          backgroundColor: 'var(--limo-surface)',
          borderColor: 'var(--limo-border)',
          color: 'var(--limo-text-primary)',
          '&:focus': {
            borderColor: 'var(--limo-primary)',
            boxShadow: '0 0 0 0.125rem var(--limo-primary-transparent)',
          },
          '&::placeholder': {
            color: 'var(--limo-text-muted)',
          }
        },
        label: {
          color: 'var(--limo-text-primary)',
          fontWeight: 500,
        },
      }
    },

    Button: {
      styles: {
        root: {
          backgroundColor: 'var(--limo-primary)',
          color: '#ffffff',
          borderColor: 'var(--limo-primary)',
          '&:hover': {
            backgroundColor: 'var(--limo-primary-light)',
            borderColor: 'var(--limo-primary-light)',
          },
          '&:active': {
            backgroundColor: 'var(--limo-primary-dark)',
            borderColor: 'var(--limo-primary-dark)',
          }
        }
      }
    },

    Paper: {
      styles: {
        root: {
          backgroundColor: 'var(--limo-surface)',
          color: 'var(--limo-text-primary)',
          borderColor: 'var(--limo-border)',
        }
      }
    },

    Card: {
      styles: {
        root: {
          backgroundColor: 'var(--limo-surface)',
          borderColor: 'var(--limo-border)',
          color: 'var(--limo-text-primary)',
        }
      }
    },

    Switch: {
      styles: {
        track: {
          backgroundColor: 'var(--limo-text-disabled)',
          '&[data-checked]': {
            backgroundColor: 'var(--limo-primary)',
          }
        },
        thumb: {
          backgroundColor: '#ffffff',
        }
      }
    },

    Select: {
      styles: {
        input: {
          backgroundColor: 'var(--limo-surface)',
          borderColor: 'var(--limo-border)',
          color: 'var(--limo-text-primary)',
          '&:focus': {
            borderColor: 'var(--limo-primary)',
            boxShadow: '0 0 0 0.125rem var(--limo-primary-transparent)',
          }
        },
        label: {
          color: 'var(--limo-text-primary)',
          fontWeight: 500,
        },
        dropdown: {
          backgroundColor: 'var(--limo-surface)',
          borderColor: 'var(--limo-border)',
        },
        option: {
          color: 'var(--limo-text-primary)',
          '&[data-selected]': {
            backgroundColor: 'var(--limo-primary)',
            color: '#ffffff',
          },
          '&:hover': {
            backgroundColor: 'var(--limo-surface-light)',
          }
        }
      }
    },

    NumberInput: {
      styles: {
        input: {
          backgroundColor: 'var(--limo-surface)',
          borderColor: 'var(--limo-border)',
          color: 'var(--limo-text-primary)',
          '&:focus': {
            borderColor: 'var(--limo-primary)',
            boxShadow: '0 0 0 0.125rem var(--limo-primary-transparent)',
          }
        },
        label: {
          color: 'var(--limo-text-primary)',
          fontWeight: 500,
        },
        control: {
          backgroundColor: 'var(--limo-surface)',
          borderColor: 'var(--limo-border)',
          color: 'var(--limo-text-primary)',
          '&:hover': {
            backgroundColor: 'var(--limo-surface-light)',
            borderColor: 'var(--limo-primary)',
          }
        }
      }
    },

    DateInput: {
      styles: {
        input: {
          backgroundColor: 'var(--limo-surface)',
          borderColor: 'var(--limo-border)',
          color: 'var(--limo-text-primary)',
          '&:focus': {
            borderColor: 'var(--limo-primary)',
            boxShadow: '0 0 0 0.125rem var(--limo-primary-transparent)',
          }
        },
        label: {
          color: 'var(--limo-text-primary)',
          fontWeight: 500,
        },
      }
    },

    DateTimePicker: {
      styles: {
        input: {
          backgroundColor: 'var(--limo-surface)',
          borderColor: 'var(--limo-border)',
          color: 'var(--limo-text-primary)',
          '&:focus': {
            borderColor: 'var(--limo-primary)',
            boxShadow: '0 0 0 0.125rem var(--limo-primary-transparent)',
          }
        },
        label: {
          color: 'var(--limo-text-primary)',
          fontWeight: 500,
        },
      }
    },

    Tabs: {
      styles: {
        tab: {
          color: 'var(--limo-text-secondary)',
          '&[data-active]': {
            color: 'var(--limo-primary)',
            borderBottomColor: 'var(--limo-primary)',
          },
          '&:hover': {
            backgroundColor: 'var(--limo-surface-light)',
          }
        },
        tabsList: {
          borderBottomColor: 'var(--limo-border)',
        }
      }
    },

    Modal: {
      styles: {
        content: {
          backgroundColor: 'var(--limo-surface)',
          color: 'var(--limo-text-primary)',
        },
        header: {
          backgroundColor: 'var(--limo-surface)',
          borderBottomColor: 'var(--limo-border)',
        },
        title: {
          color: 'var(--limo-text-primary)',
        }
      }
    },

    Checkbox: {
      styles: {
        label: {
          color: 'var(--limo-text-primary)',
        }
      }
    },

    Radio: {
      styles: {
        label: {
          color: 'var(--limo-text-primary)',
        }
      }
    },

    Group: {
      styles: {
        root: {
          color: 'var(--limo-text-primary)',
        }
      }
    },

    Text: {
      styles: {
        root: {
          color: 'var(--limo-text-primary)',
        }
      }
    },

    Title: {
      styles: {
        root: {
          color: 'var(--limo-text-primary)',
        }
      }
    },
  },
});
