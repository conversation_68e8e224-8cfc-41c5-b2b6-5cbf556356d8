import { createTheme, MantineColorsTuple } from '@mantine/core';

// Define a custom primary color
const primaryColor: MantineColorsTuple = [
  '#e0f9ff',
  '#caeeff',
  '#9cd8ff',
  '#69c0fe',
  '#42acfd',
  '#2a9fff',
  '#1595ff',
  '#0081e4',
  '#0073cc',
  '#0065b5'
];

export const mantineTheme = createTheme({
  primaryColor: 'primary',
  colors: {
    primary: primaryColor,
    // Add any additional custom colors here
  },
  defaultRadius: 'md',
  fontFamily: 'Inter, sans-serif',
  primaryShade: { light: 5, dark: 7 },
  
  // Set default component styles
  components: {
    TextInput: {
      defaultProps: {
        styles: {
          input: {
            backgroundColor: 'var(--mantine-color-dark-6)',
            borderColor: 'var(--mantine-color-dark-4)',
            color: 'var(--mantine-color-white)',
          },
          label: {
            color: 'var(--mantine-color-gray-4)',
          },
        }
      }
    },
    Textarea: {
      defaultProps: {
        styles: {
          input: {
            backgroundColor: 'var(--mantine-color-dark-6)',
            borderColor: 'var(--mantine-color-dark-4)',
            color: 'var(--mantine-color-white)',
          },
          label: {
            color: 'var(--mantine-color-gray-4)',
          },
        }
      }
    },
    Button: {
      defaultProps: {
        color: 'primary',
      }
    },
    Paper: {
      defaultProps: {
        styles: {
          root: {
            backgroundColor: 'var(--mantine-color-dark-7)',
          }
        }
      }
    },
    Switch: {
      defaultProps: {
        color: 'primary',
      }
    },
    Card: {
      defaultProps: {
        styles: {
          root: {
            backgroundColor: 'var(--mantine-color-dark-6)',
          }
        }
      }
    },
  },
}); 