import { useState } from 'react';
import { useBookingStore } from '../../store/bookingStore';
import * as z from 'zod';

// Define the form data type
interface FormData {
  pickupDate: string;
  pickupTime: string;
  pickupLocation: {
    address: string;
    coordinates: [number, number];
  };
  dropoffLocation?: {
    address: string;
    coordinates: [number, number];
  };
  adults: number;
  children?: number;
  hourlyDuration?: number;
  flightInfo?: {
    airline?: string;
    flightNumber?: string;
  };
}

const bookingFormSchema = z.object({
  pickupDate: z.string().min(1, 'Pickup date is required'),
  pickupTime: z.string().min(1, 'Pickup time is required'),
  pickupLocation: z.object({
    address: z.string().min(1, 'Pickup location is required'),
    coordinates: z.tuple([z.number(), z.number()])
  }),
  dropoffLocation: z.object({
    address: z.string().min(1, 'Dropoff location is required'),
    coordinates: z.tuple([z.number(), z.number()])
  }).optional(),
  adults: z.number().min(1, 'At least 1 adult is required'),
  children: z.number().min(0),
  hourlyDuration: z.number().min(2, 'Minimum duration is 2 hours').optional(),
  flightInfo: z.object({
    airline: z.string().optional(),
    flightNumber: z.string().optional()
  }).optional()
});

export const useBookingForm = () => {
  const store = useBookingStore();
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set());

  const validateForm = () => {
    try {
      const formData: FormData = {
        pickupDate: store.pointToPointData?.pickupDate || '',
        pickupTime: store.pointToPointData?.pickupTime || '',
        pickupLocation: store.pointToPointData?.pickupLocation || { address: '', coordinates: [0, 0] },
        dropoffLocation: store.pointToPointData?.dropoffLocation,
        adults: store.pointToPointData?.adults || 0,
        children: store.pointToPointData?.children,
        hourlyDuration: store.hourlyData?.hours,
        flightInfo: store.airportData?.flightInfo
      };

      bookingFormSchema.parse(formData);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          const path = err.path.join('.');
          newErrors[path] = err.message;
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleNext = () => {
    if (validateForm()) {
      store.setStep(store.currentStep + 1);
    }
  };

  const validateField = (field: string, value: any): { isValid: boolean; message: string } => {
    try {
      const schema = bookingFormSchema.shape[field as keyof FormData];
      if (schema) {
        schema.parse(value);
        return { isValid: true, message: '' };
      }
      return { isValid: true, message: '' };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return { isValid: false, message: error.errors[0].message };
      }
      return { isValid: false, message: 'Invalid field value' };
    }
  };

  const validateFields = (fields: string[]) => {
    const formData: FormData = {
      pickupDate: store.pointToPointData?.pickupDate || '',
      pickupTime: store.pointToPointData?.pickupTime || '',
      pickupLocation: store.pointToPointData?.pickupLocation || { address: '', coordinates: [0, 0] },
      dropoffLocation: store.pointToPointData?.dropoffLocation,
      adults: store.pointToPointData?.adults || 0,
      children: store.pointToPointData?.children,
      hourlyDuration: store.hourlyData?.hours,
      flightInfo: store.airportData?.flightInfo
    };

    return fields.map(field => ({
      field,
      ...validateField(field, formData[field as keyof FormData])
    }));
  };

  return {
    errors,
    validateForm,
    handleNext,
    touchField: (field: string) => setTouchedFields(prev => new Set([...prev, field])),
    getFieldStatus: (field: string) => {
      const formData: FormData = {
        pickupDate: store.pointToPointData?.pickupDate || '',
        pickupTime: store.pointToPointData?.pickupTime || '',
        pickupLocation: store.pointToPointData?.pickupLocation || { address: '', coordinates: [0, 0] },
        dropoffLocation: store.pointToPointData?.dropoffLocation,
        adults: store.pointToPointData?.adults || 0,
        children: store.pointToPointData?.children,
        hourlyDuration: store.hourlyData?.hours,
        flightInfo: store.airportData?.flightInfo
      };
      
      return {
        ...validateField(field, formData[field as keyof FormData]),
        isTouched: touchedFields.has(field)
      };
    },
  };
};
