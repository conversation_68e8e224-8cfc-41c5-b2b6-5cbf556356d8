import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(dateStr: string): string {
  return new Date(dateStr).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

export function formatTime(timeString: string | undefined): string {
  if (!timeString) {
    return '';
  }

  const [hours, minutes] = timeString.split(':');
  const date = new Date();
  date.setHours(parseInt(hours), parseInt(minutes));
  return date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });
}

/**
 * Scrolls the booking form to the top
 * This can be called from any component that needs to scroll the form
 * Updated to be more selective and avoid interfering with page scrolling
 */
export const scrollFormToTop = () => {
  console.log('scrollFormToTop called');
  
  // CRITICAL FIX: Completely abort if hash navigation is active
  if ((window as any).preventFormScrolling) {
    console.log('Scroll behavior disabled due to hash navigation');
    return;
  }
  
  // CRITICAL FIX: Also check for hash in URL and abort
  if (window.location.hash) {
    console.log('Hash detected in URL, aborting form scroll:', window.location.hash);
    (window as any).preventFormScrolling = true;
    return;
  }
  
  // CRITICAL FIX: Check for active jQuery animations
  if ((window as any).jQuery && (window as any).jQuery(':animated').length > 0) {
    console.log('jQuery animations in progress, aborting form scroll');
    return;
  }
  
  try {
    // Find the form container
    const formContainer = document.querySelector('.booking-form-container');
    const formWrapper = document.getElementById('limo-booking-form');
    
    if (!formContainer) {
      console.log('Form container not found');
      return;
    }

    // Try to find a scrollable parent within our form
    // This works better than scrolling the whole page
    let scrollableParent = formContainer;
    
    // Check if the form wrapper is scrollable or has a fixed height
    if (formWrapper) {
      const wrapperStyle = window.getComputedStyle(formWrapper);
      if (
        wrapperStyle.overflow === 'auto' || 
        wrapperStyle.overflow === 'scroll' || 
        wrapperStyle.overflowY === 'auto' || 
        wrapperStyle.overflowY === 'scroll'
      ) {
        scrollableParent = formWrapper;
      }
    }
    
    // Try to scroll the form container itself if possible
    if (scrollableParent === formWrapper) {
      console.log('Scrolling form wrapper element');
      scrollableParent.scrollTop = 0;
      return;
    }
    
    // CRITICAL FIX: One more check for hash navigation
    if (window.location.hash || (window as any).preventFormScrolling) {
      console.log('Late hash detection, aborting form scroll');
      return;
    }
    
    // If we get here, we need to scroll just enough to see the top of our form
    // Get the form's position
    const rect = formContainer.getBoundingClientRect();
    const offset = 100; // Higher offset for WordPress headers
    
    // Calculate absolute position - with added safety checks
    const pageYOffset = window.pageYOffset !== undefined ? window.pageYOffset : 
                        (document.documentElement || document.body).scrollTop || 0;
    
    // Only scroll to the form's position, not all the way to the top of the page
    const absolutePosition = pageYOffset + rect.top - offset;
    
    console.log('Scrolling to form position:', absolutePosition);
    
    // Check if we're already at a good scroll position (form is in view)
    // Don't scroll if the form is already visible
    if (rect.top >= 0 && rect.top <= window.innerHeight / 3) {
      console.log('Form already visible, not scrolling');
      return;
    }
    
    // CRITICAL FIX: Check one more time for hash before scrolling
    if (window.location.hash) {
      console.log('Hash detected right before scrolling, aborting:', window.location.hash);
      return;
    }
    
    // CRITICAL FIX: Check if jQuery is handling scrolling
    if ((window as any).jQuery && typeof (window as any).jQuery.scrollTo === 'function') {
      console.log('jQuery.scrollTo detected, aborting our own scroll');
      return;
    }
    
    // Scroll to position - safely
    try {
      // CRITICAL FIX: Final check for jQuery activity
      if ((window as any).jQuery && (window as any).jQuery(':animated').length > 0) {
        console.log('Last-minute jQuery animations detected, aborting scroll');
        return;
      }
      
      // For best results with WordPress themes, scroll the nearest scrollable element
      const scrollElement = getScrollParent(formContainer);
      
      if (scrollElement && scrollElement !== document.documentElement && scrollElement !== document.body) {
        // If we found a scrollable parent, scroll that instead of the whole page
        const containerRect = formContainer.getBoundingClientRect();
        const scrollParentRect = scrollElement.getBoundingClientRect();
        const relativeTop = containerRect.top - scrollParentRect.top;
        
        console.log('Scrolling parent element', {
          scrollElement,
          relativeTop
        });
        
        scrollElement.scrollTo({
          top: relativeTop - offset,
          behavior: 'smooth'
        });
      } else {
        // As a last resort, scroll the window - but only if no hash navigation
        if (!window.location.hash && !(window as any).preventFormScrolling) {
          console.log('Scrolling window as fallback');
          window.scrollTo({
            top: absolutePosition,
            behavior: 'smooth'
          });
        } else {
          console.log('Hash detected during final scroll attempt, aborting');
        }
      }
    } catch (e) {
      // Fallback for older browsers - with final hash check
      console.log('Using basic scrollTo fallback');
      if (!window.location.hash && !(window as any).preventFormScrolling) {
        window.scrollTo(0, absolutePosition);
      }
    }
  } catch (err) {
    console.error('Error in scrollFormToTop:', err);
    // Don't fail if scrolling fails
  }
};

/**
 * Helper function to find the nearest scrollable parent
 */
function getScrollParent(element: Element): Element | null {
  if (!element) return document.documentElement;
  
  // Start checking from the element's parent
  let parent = element.parentElement;
  
  while (parent) {
    const style = window.getComputedStyle(parent);
    const overflowY = style.getPropertyValue('overflow-y');
    const isScrollable = overflowY === 'auto' || overflowY === 'scroll';
    
    if (isScrollable && parent.scrollHeight > parent.clientHeight) {
      return parent;
    }
    
    parent = parent.parentElement;
  }
  
  // If no scrollable parent found, return the document element
  return document.documentElement;
}

// Function to detect if we're in a WordPress environment
export const detectEnvironment = () => {
  try {
    const isWordPress = typeof window !== 'undefined' && 
      (
        // Check for WordPress global - safely with optional chaining
        !!(window as any)?.wp || 
        // Check for common WordPress classes
        !!document.querySelector('.wp-admin') || 
        !!document.querySelector('.wp-block') ||
        // Check URL for WordPress admin
        window.location.href.includes('/wp-admin/') ||
        // Check for WordPress body classes
        (document.body && document.body.className && document.body.className.includes('wp-')) ||
        // Check for WordPress config
        !!(window as any)?.limoBookingConfig?.isWordPress
      );
    
    console.log('🔍 Environment detection:', {
      isWordPress,
      userAgent: navigator.userAgent,
      windowSize: {
        innerWidth: window.innerWidth,
        innerHeight: window.innerHeight,
        outerWidth: window.outerWidth,
        outerHeight: window.outerHeight
      },
      documentElement: document.documentElement ? {
        clientWidth: document.documentElement.clientWidth,
        clientHeight: document.documentElement.clientHeight,
        scrollWidth: document.documentElement.scrollWidth,
        scrollHeight: document.documentElement.scrollHeight
      } : 'Not available',
      body: document.body ? {
        clientWidth: document.body.clientWidth,
        clientHeight: document.body.clientHeight,
        scrollWidth: document.body.scrollWidth,
        scrollHeight: document.body.scrollHeight
      } : 'Not available',
      location: window.location.href
    });
    
    return isWordPress;
  } catch (err) {
    console.error('Error in detectEnvironment:', err);
    // If there's an error, assume it's not WordPress to be safe
    return false;
  }
};
