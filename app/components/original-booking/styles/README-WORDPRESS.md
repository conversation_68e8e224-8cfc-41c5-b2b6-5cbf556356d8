# WordPress Plugin CSS Integration Guide

## Overview

This document provides guidance on integrating the Limo Booking Form with WordPress themes while maintaining proper styling and avoiding CSS conflicts.

## Namespaced CSS Architecture

We've implemented a namespaced CSS architecture to ensure the plugin's styles don't conflict with WordPress themes. This approach:

- Uses specific selectors with a unique plugin prefix
- Implements CSS custom properties for theme customization
- Ensures proper specificity through class hierarchy rather than forcing styles with `!important`
- Makes the plugin more maintainable and compatible with various WordPress themes

## Implementation Guide

### 1. Add the Root Class

Wrap your plugin content with the `limo-booking-plugin` class:

```html
<div class="limo-booking-plugin" data-theme="dark">
  <!-- Plugin content goes here -->
</div>
```

### 2. Use Namespaced Classes

Instead of using generic classes like `bg-primary`, use our namespaced classes:

| Generic Class | Namespaced Class |
|---------------|------------------|
| `bg-primary` | `limo-bg-primary` |
| `text-primary` | `limo-text` |
| `button` | `limo-button limo-button-primary` |

### 3. Theme Switching

To switch between light and dark themes, add the `data-theme` attribute to the root element:

```html
<!-- Dark theme (default) -->
<div class="limo-booking-plugin" data-theme="dark">
  <!-- Content -->
</div>

<!-- Light theme -->
<div class="limo-booking-plugin" data-theme="light">
  <!-- Content -->
</div>
```

### 4. WordPress Theme Integration

The plugin uses CSS variables that can be overridden by the WordPress theme:

```css
/* In your WordPress theme's CSS */
:root {
  --wp-primary: #your-brand-color;
  --wp-background: #your-background-color;
  /* Other variables as needed */
}
```

## Example Usage

### Button Example

```html
<div class="limo-booking-plugin">
  <button class="limo-button limo-button-primary">Book Now</button>
</div>
```

### Form Input Example

```html
<div class="limo-booking-plugin">
  <input type="text" class="limo-input" placeholder="Enter pickup location">
</div>
```

### Tabs Example

```html
<div class="limo-booking-plugin">
  <div class="limo-tabs">
    <button class="limo-tab" data-active="true">Point to Point</button>
    <button class="limo-tab">Hourly</button>
    <button class="limo-tab">Airport</button>
  </div>
</div>
```

## Benefits of This Approach

1. **No CSS Conflicts**: The namespaced approach prevents styles from affecting other elements on the WordPress site.

2. **Theme Compatibility**: The plugin adapts to the WordPress theme's color scheme through CSS variables.

3. **Maintainability**: Code is easier to maintain without relying on `!important` declarations.

4. **Performance**: More efficient CSS with better specificity and fewer overrides.

5. **Consistency**: Ensures consistent styling across different WordPress themes.

## File Structure

- `wordpress-plugin.css`: Contains all namespaced styles for WordPress integration
- `index.css`: Main stylesheet that imports all other CSS files

## Troubleshooting

If styles are not applying correctly:

1. Ensure the `limo-booking-plugin` class is added to the root container
2. Check that you're using the namespaced classes (e.g., `limo-button` instead of just `button`)
3. Verify that the WordPress theme is not applying styles with higher specificity