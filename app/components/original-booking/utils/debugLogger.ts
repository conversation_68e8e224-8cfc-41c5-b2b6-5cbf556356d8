interface DiagnosticResult {
  status: 'success' | 'warning' | 'error' | 'info';
  message: string;
  details?: any;
  recommendations?: string[];
}

type LogLevel = 'info' | 'warn' | 'error' | 'debug' | 'perf' | 'diagnostic' | 'css';

interface DebugConfig {
  enabled: boolean;
  logToConsole: boolean;
  logToUI: boolean;
  logLevels: LogLevel[];
  maxHistorySize: number;
  persistToLocalStorage: boolean;
  logNetworkCalls: boolean;
  groupSimilarLogs: boolean;
  trackCssChanges: boolean;
}

export interface LogEntry {
  timestamp: number;
  level: LogLevel;
  category: string;
  message: string;
  data?: any;
  diagnostic?: DiagnosticResult;
}

interface PerformanceMetric {
  action: string;
  startTime: number;
  duration?: number;
}

class BookingDebugger {
  private config: DebugConfig = {
    enabled: process.env.NODE_ENV === 'development',
    logToConsole: true,
    logToUI: true,
    logLevels: ['info', 'warn', 'error', 'debug', 'perf', 'diagnostic', 'css'],
    maxHistorySize: 1000,
    persistToLocalStorage: true,
    logNetworkCalls: true,
    groupSimilarLogs: true,
    trackCssChanges: true
  };

  private logHistory: LogEntry[] = [];
  private subscribers: Set<(logs: LogEntry[]) => void> = new Set();
  private performanceMetrics: Map<string, PerformanceMetric> = new Map();
  private readonly STORAGE_KEY = 'booking_debug_logs';
  private groupedLogs: Map<string, number> = new Map();
  private elementStyleSnapshots: Map<string, Map<string, string>> = new Map();
  private cssObservers: Map<string, MutationObserver> = new Map();

  constructor() {
    this.initializeLogger();
    this.setupNetworkInterceptor();
  }

  private getSystemInfo() {
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      screenResolution: `${window.screen.width}x${window.screen.height}`,
      viewport: `${window.innerWidth}x${window.innerHeight}`,
      timestamp: new Date().toISOString(),
      memory: (performance as any).memory ? {
        jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit,
        totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
        usedJSHeapSize: (performance as any).memory.usedJSHeapSize
      } : undefined
    };
  }

  private initializeLogger() {
    console.log('🔍 Debug Logger Initialized:', {
      isDev: import.meta.env.DEV,
      config: this.config,
      systemInfo: this.getSystemInfo()
    });

    if (this.config.persistToLocalStorage) {
      this.loadLogsFromStorage();
    }

    this.logSystemInfo();
    this.runInitialDiagnostics();
  }

  private async runInitialDiagnostics() {
    this.diagnostic('System', 'Running initial diagnostics', {
      status: 'info',
      message: 'Checking system configuration...'
    } as DiagnosticResult);

    // Check environment
    const envVars = this.checkEnvironmentVariables();
    this.diagnostic('Environment', 'Environment variables check', envVars);

    // Check browser capabilities
    const browserCaps = this.checkBrowserCapabilities();
    this.diagnostic('Browser', 'Browser capabilities check', browserCaps);

    // Check network connectivity
    const network = await this.checkNetworkConnectivity();
    this.diagnostic('Network', 'Network connectivity check', network);
  }

  private checkEnvironmentVariables(): DiagnosticResult {
    const isDev = import.meta.env.DEV;
    const mode = import.meta.env.MODE;
    
    return {
      status: 'success',
      message: 'Environment check completed',
      details: { isDev, mode }
    };
  }

  private checkBrowserCapabilities(): DiagnosticResult {
    const capabilities = {
      localStorage: !!window.localStorage,
      sessionStorage: !!window.sessionStorage,
      webWorkers: !!window.Worker,
      serviceWorker: 'serviceWorker' in navigator,
      webGL: !!document.createElement('canvas').getContext('webgl'),
      geolocation: 'geolocation' in navigator
    };

    const missingFeatures = Object.entries(capabilities)
      .filter(([, supported]) => !supported)
      .map(([feature]) => feature);

    return {
      status: missingFeatures.length ? 'warning' : 'success',
      message: missingFeatures.length 
        ? `Missing browser capabilities: ${missingFeatures.join(', ')}`
        : 'All required browser capabilities available',
      details: capabilities,
      recommendations: missingFeatures.map(feature => 
        `Enable ${feature} or implement fallback`)
    };
  }

  private async checkNetworkConnectivity(): Promise<DiagnosticResult> {
    try {
      const startTime = performance.now();
      await fetch('https://www.google.com/favicon.ico');
      const duration = performance.now() - startTime;

      return {
        status: 'success',
        message: 'Network is connected',
        details: {
          latency: `${Math.round(duration)}ms`,
          online: navigator.onLine
        }
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        status: 'error',
        message: 'Network connectivity issues detected',
        details: {
          error: errorMessage,
          online: navigator.onLine
        },
        recommendations: [
          'Check internet connection',
          'Verify firewall settings',
          'Check proxy configuration'
        ]
      };
    }
  }

  diagnostic(category: string, message: string, result: DiagnosticResult) {
    const icon = {
      success: '✅',
      warning: '⚠️',
      error: '❌',
      info: 'ℹ️'
    }[result.status] || 'ℹ️';

    this.log('diagnostic', category, `${icon} ${message}`, { 
      diagnostic: result 
    });
  }

  private getRequestUrl(input: RequestInfo | URL): string {
    if (input instanceof Request) {
      return input.url;
    } else if (input instanceof URL) {
      return input.href;
    }
    return String(input);
  }

  private setupNetworkInterceptor() {
    if (this.config.logNetworkCalls) {
      const originalFetch = window.fetch;
      const newFetch = async (input: RequestInfo | URL, init?: RequestInit) => {
        const startTime = performance.now();
        const url = this.getRequestUrl(input);
        const method = init?.method || 'GET';
        
        try {
          const response = await originalFetch(input, init);
          const duration = performance.now() - startTime;
          
          this.networkCall('fetch', url, method, duration, response.status, {
            headers: Object.fromEntries(response.headers.entries()),
            ok: response.ok,
            statusText: response.statusText
          });
          
          return response;
        } catch (error) {
          const duration = performance.now() - startTime;
          this.networkError('fetch', url, method, error, duration, {
            init: {
              ...init,
              headers: init?.headers ? Object.fromEntries(new Headers(init.headers).entries()) : undefined
            }
          });
          throw error;
        }
      };
      window.fetch = newFetch;
    }
  }

  private formatDuration(duration: number): string {
    if (duration < 1000) {
      return `${duration.toFixed(2)}ms`;
    }
    return `${(duration / 1000).toFixed(2)}s`;
  }

  networkCall(
    type: string,
    url: string,
    method: string,
    duration: number,
    status: number,
    details?: any
  ) {
    const formattedDuration = this.formatDuration(duration);
    this.log('debug', 'Network', `${method} ${url}`, {
      type,
      duration: formattedDuration,
      status,
      ...details
    });
  }

  networkError(
    type: string,
    url: string,
    method: string,
    error: any,
    duration: number,
    details?: any
  ) {
    const formattedDuration = this.formatDuration(duration);
    this.log('error', 'Network', `Failed ${method} ${url}`, {
      type,
      error: error instanceof Error ? error.message : String(error),
      duration: formattedDuration,
      ...details
    });
  }

  private consoleOutput(logEntry: LogEntry) {
    const styles: Record<LogLevel, string> = {
      info: 'color: #8B5E34; font-weight: bold',
      warn: 'color: #FFA500; font-weight: bold',
      error: 'color: #FF0000; font-weight: bold',
      debug: 'color: #808080; font-weight: bold',
      perf: 'color: #9370DB; font-weight: bold',
      diagnostic: 'color: #4CAF50; font-weight: bold',
      css: 'color: #38b2ac; font-weight: bold'
    };

    const timestamp = new Date(logEntry.timestamp).toLocaleTimeString();
    const logKey = `${logEntry.level}-${logEntry.category}-${logEntry.message}`;
    
    if (this.config.groupSimilarLogs) {
      const count = (this.groupedLogs.get(logKey) || 0) + 1;
      this.groupedLogs.set(logKey, count);
      
      if (count > 1) {
        return; // Skip duplicate logs
      }
    }

    const prefix = `%c[${logEntry.level.toUpperCase()}] ${timestamp}`;
    
    if (logEntry.diagnostic) {
      console.groupCollapsed(
        `${prefix} ${logEntry.category}: ${logEntry.message}`,
        styles[logEntry.level]
      );
      
      console.log('Status:', logEntry.diagnostic.status);
      console.log('Message:', logEntry.diagnostic.message);
      
      if (logEntry.diagnostic.details) {
        console.log('Details:', logEntry.diagnostic.details);
      }
      
      if (logEntry.diagnostic.recommendations?.length) {
        console.log('Recommendations:');
        logEntry.diagnostic.recommendations.forEach(rec => 
          console.log(`- ${rec}`));
      }
    } else {
      console.groupCollapsed(
        `${prefix} ${logEntry.category}: ${logEntry.message}`,
        styles[logEntry.level]
      );
      
      if (logEntry.data) {
        Object.entries(logEntry.data).forEach(([key, value]) => {
          console.log(`${key}:`, value);
        });
      }
    }
    
    console.groupEnd();
  }

  private logSystemInfo() {
    this.log('info', 'System', 'System Information', this.getSystemInfo());
  }

  private loadLogsFromStorage() {
    try {
      const storedLogs = localStorage.getItem(this.STORAGE_KEY);
      if (storedLogs) {
        this.logHistory = JSON.parse(storedLogs);
      }
    } catch (error) {
      // Silently handle storage access errors
      console.log('Unable to access localStorage for debug logs:', error instanceof Error ? error.message : String(error));
      // Disable persistence to prevent further errors
      this.config.persistToLocalStorage = false;
    }
  }

  private saveLogsToStorage() {
    try {
      if (this.config.persistToLocalStorage) {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.logHistory));
      }
    } catch (error) {
      // Silently handle storage access errors
      console.log('Unable to save to localStorage:', error instanceof Error ? error.message : String(error));
      // Disable persistence to prevent further errors
      this.config.persistToLocalStorage = false;
    }
  }

  private notifySubscribers() {
    this.subscribers.forEach(callback => callback(this.logHistory));
  }

  subscribe(callback: (logs: LogEntry[]) => void) {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  log(level: LogLevel, category: string, message: string, data?: any) {
    if (!this.config.enabled || !this.config.logLevels.includes(level)) return;

    const logEntry: LogEntry = {
      timestamp: Date.now(),
      level,
      category,
      message,
      data
    };

    this.addLogEntry(logEntry);
  }

  private addLogEntry(logEntry: LogEntry) {
    this.logHistory.push(logEntry);
    
    // Trim history if it exceeds max size
    if (this.logHistory.length > this.config.maxHistorySize) {
      this.logHistory = this.logHistory.slice(-this.config.maxHistorySize);
    }

    this.saveLogsToStorage();
    this.notifySubscribers();

    if (this.config.logToConsole) {
      this.consoleOutput(logEntry);
    }
  }

  startPerformanceMetric(action: string) {
    this.performanceMetrics.set(action, {
      action,
      startTime: performance.now()
    });
  }

  endPerformanceMetric(action: string) {
    const metric = this.performanceMetrics.get(action);
    if (metric) {
      const duration = performance.now() - metric.startTime;
      this.log('perf', 'Performance', `${action} completed`, {
        durationMs: duration.toFixed(2),
        action,
        timestamp: new Date().toISOString()
      });
      this.performanceMetrics.delete(action);
    }
  }

  clearLogs() {
    this.logHistory = [];
    this.saveLogsToStorage();
    this.notifySubscribers();
  }

  getLogHistory() {
    return this.logHistory;
  }

  toggleLogLevel(level: LogLevel) {
    const index = this.config.logLevels.indexOf(level);
    if (index === -1) {
      this.config.logLevels.push(level);
    } else {
      this.config.logLevels.splice(index, 1);
    }
    this.notifySubscribers();
  }

  renderDebugPanel() {
    if (!this.config.logToUI) return null;

    return `
      <div class="fixed bottom-0 right-0 w-96 h-64 bg-black/90 text-white p-4 overflow-auto text-xs">
        <div class="flex justify-between items-center mb-2">
          <h3 class="font-bold">Booking Debug Log</h3>
          <div class="space-x-2">
            <button onclick="window.bookingDebugger.clearLogs()" class="text-xs text-gray-400">Clear</button>
            <button onclick="window.bookingDebugger.exportLogs()" class="text-xs text-gray-400">Export</button>
          </div>
        </div>
        <div class="flex space-x-2 mb-2">
          ${this.config.logLevels.map(level => `
            <button 
              onclick="window.bookingDebugger.toggleLogLevel('${level}')"
              class="px-2 py-1 rounded text-xs ${this.config.logLevels.includes(level) ? 'bg-blue-500' : 'bg-gray-700'}"
            >
              ${level}
            </button>
          `).join('')}
        </div>
        <div class="space-y-1">
          ${this.logHistory.map(entry => `
            <div class="log-entry ${entry.level} hover:bg-black/50 p-1 rounded">
              <div class="flex justify-between">
                <span class="text-gray-400">${new Date(entry.timestamp).toLocaleTimeString()}</span>
                <span class="text-${this.getLevelColor(entry.level)}">[${entry.category}]</span>
              </div>
              <div>${entry.message}</div>
              ${entry.data ? `<pre class="text-gray-400 text-xs mt-1">${JSON.stringify(entry.data, null, 2)}</pre>` : ''}
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }

  private getLevelColor(level: LogLevel): string {
    const colors: Record<LogLevel, string> = {
      info: '#4299e1',      // blue
      warn: '#ecc94b',      // yellow
      error: '#f56565',     // red
      debug: '#68d391',     // green
      perf: '#9f7aea',      // purple
      diagnostic: '#ed64a6', // pink
      css: '#38b2ac'        // teal
    };
    return colors[level] || '#a0aec0'; // default to a gray
  }

  exportLogs() {
    const blob = new Blob([JSON.stringify(this.logHistory, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `booking-debug-logs-${new Date().toISOString()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  // User interaction logging
  userAction(action: string, details?: any) {
    this.log('info', 'User Action', action, details);
  }

  // Form related logging
  formTransition(from: number, to: number, data?: any) {
    this.log('info', 'Navigation', `Step transition: ${from} → ${to}`, data);
  }

  fieldUpdate(fieldName: string, value: any, isValid: boolean) {
    this.log('debug', 'Field Update', `${fieldName}: ${value}`, { isValid });
  }

  validationError(fieldName: string, error: string) {
    this.log('error', 'Validation', `${fieldName}: ${error}`);
  }

  formValidation(fieldName: string, value: any, validationResult: boolean, errors?: string[]) {
    this.log('debug', 'Validation', `Field ${fieldName} validation`, {
      value,
      isValid: validationResult,
      errors
    });
  }

  // Map related logging
  mapEvent(eventType: string, data: any) {
    this.log('debug', 'Map', eventType, data);
  }

  // State management logging
  stateUpdate(stateName: string, oldValue: any, newValue: any) {
    this.log('debug', 'State', `${stateName} changed`, {
      from: oldValue,
      to: newValue,
      diff: JSON.stringify(newValue) !== JSON.stringify(oldValue)
    });
  }

  // Error handling
  error(source: string, error: Error | string) {
    this.log('error', 'Error', typeof error === 'string' ? error : error.message, {
      source,
      stack: error instanceof Error ? error.stack : undefined
    });
  }

  errorBoundary(error: Error, componentStack: string) {
    this.log('error', 'ErrorBoundary', error.message, {
      stack: error.stack,
      componentStack
    });
  }

  // API related logging
  apiCall(endpoint: string, method: string, data?: any) {
    this.log('debug', 'API', `${method.toUpperCase()} ${endpoint}`, data);
  }

  /**
   * Capture the computed CSS styles for a DOM element
   * @param selector - CSS selector for the element
   * @param properties - Optional array of specific CSS properties to capture. If not provided, captures all properties.
   * @param label - Optional label to identify this element in logs
   * @returns Object with the computed styles or null if element not found
   */
  captureElementStyles(selector: string, properties?: string[], label?: string): object | null {
    const element = document.querySelector(selector);
    if (!element) {
      this.log('css', 'StyleCapture', `Element not found: ${selector}`, { selector });
      return null;
    }

    const styles = window.getComputedStyle(element);
    const result: Record<string, string> = {};

    if (properties && properties.length > 0) {
      // Capture only specific properties
      properties.forEach(prop => {
        result[prop] = styles.getPropertyValue(prop);
      });
    } else {
      // Capture all properties
      for (let i = 0; i < styles.length; i++) {
        const property = styles[i];
        result[property] = styles.getPropertyValue(property);
      }
    }

    const elementId = label || selector;
    this.log('css', 'StyleCapture', `Captured styles for ${elementId}`, { 
      selector, 
      element: this.getElementInfo(element as HTMLElement),
      styles: result 
    });

    return result;
  }

  /**
   * Capture all CSS variables (custom properties) from an element and its computed styles
   * @param selector - CSS selector for the element
   * @returns Object with CSS variables and their values, or null if element not found
   */
  captureCssVariables(selector: string = ':root'): object | null {
    const element = document.querySelector(selector);
    if (!element) {
      this.log('css', 'CSSVariables', `Element not found: ${selector}`, { selector });
      return null;
    }

    const styles = window.getComputedStyle(element);
    const variables: Record<string, string> = {};

    // Get all CSS variables
    for (let i = 0; i < styles.length; i++) {
      const property = styles[i];
      if (property.startsWith('--')) {
        variables[property] = styles.getPropertyValue(property).trim();
      }
    }

    this.log('css', 'CSSVariables', `Captured ${Object.keys(variables).length} CSS variables from ${selector}`, { 
      selector, 
      element: this.getElementInfo(element as HTMLElement),
      variables 
    });

    return variables;
  }

  /**
   * Start tracking CSS changes for a specific element
   * @param selector - CSS selector for the element to track
   * @param label - Optional label to identify this element in logs
   */
  trackElementStyles(selector: string, label?: string): void {
    if (!this.config.trackCssChanges) return;

    const element = document.querySelector(selector);
    if (!element) {
      this.log('css', 'StyleTracker', `Cannot track element - not found: ${selector}`, { selector });
      return;
    }

    const elementId = label || selector;
    
    // Take initial snapshot
    this.takeStyleSnapshot(selector, elementId);
    
    // If we're already observing this element, disconnect the old observer
    if (this.cssObservers.has(elementId)) {
      this.cssObservers.get(elementId)?.disconnect();
    }
    
    // Create a new mutation observer to track attribute changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach(mutation => {
        if (mutation.type === 'attributes' && 
            (mutation.attributeName === 'style' || mutation.attributeName === 'class')) {
          this.compareStyleSnapshots(selector, elementId);
        }
      });
    });
    
    // Start observing
    observer.observe(element, { 
      attributes: true, 
      attributeFilter: ['style', 'class'] 
    });
    
    // Store the observer
    this.cssObservers.set(elementId, observer);
    
    this.log('css', 'StyleTracker', `Started tracking styles for ${elementId}`, { 
      selector,
      element: this.getElementInfo(element as HTMLElement)
    });
  }

  /**
   * Stop tracking CSS changes for a specific element
   * @param selectorOrLabel - The CSS selector or label used when starting tracking
   */
  stopTrackingElementStyles(selectorOrLabel: string): void {
    if (this.cssObservers.has(selectorOrLabel)) {
      this.cssObservers.get(selectorOrLabel)?.disconnect();
      this.cssObservers.delete(selectorOrLabel);
      this.log('css', 'StyleTracker', `Stopped tracking styles for ${selectorOrLabel}`, { selector: selectorOrLabel });
    } else {
      this.log('css', 'StyleTracker', `No tracking found for ${selectorOrLabel}`, { selector: selectorOrLabel });
    }
  }

  /**
   * Take a snapshot of an element's current computed styles
   * @private
   */
  private takeStyleSnapshot(selector: string, elementId: string): void {
    const element = document.querySelector(selector);
    if (!element) return;
    
    const styles = window.getComputedStyle(element);
    const snapshot = new Map<string, string>();
    
    for (let i = 0; i < styles.length; i++) {
      const property = styles[i];
      snapshot.set(property, styles.getPropertyValue(property));
    }
    
    this.elementStyleSnapshots.set(elementId, snapshot);
  }

  /**
   * Compare current styles with the previous snapshot and log changes
   * @private
   */
  private compareStyleSnapshots(selector: string, elementId: string): void {
    const element = document.querySelector(selector);
    if (!element) return;
    
    const previousSnapshot = this.elementStyleSnapshots.get(elementId);
    if (!previousSnapshot) return;
    
    const currentStyles = window.getComputedStyle(element);
    const changes: Record<string, { previous: string; current: string }> = {};
    
    // Check for changes
    previousSnapshot.forEach((previousValue, property) => {
      const currentValue = currentStyles.getPropertyValue(property);
      if (previousValue !== currentValue) {
        changes[property] = { 
          previous: previousValue, 
          current: currentValue 
        };
      }
    });
    
    // If there are changes, log them
    if (Object.keys(changes).length > 0) {
      this.log('css', 'StyleChanged', `Detected ${Object.keys(changes).length} style changes for ${elementId}`, { 
        selector,
        element: this.getElementInfo(element as HTMLElement),
        changes 
      });
      
      // Update the snapshot
      this.takeStyleSnapshot(selector, elementId);
    }
  }

  /**
   * Get basic information about a DOM element for logging
   * @private
   */
  private getElementInfo(element: HTMLElement): object {
    return {
      tagName: element.tagName,
      id: element.id,
      classList: Array.from(element.classList),
      dimensions: {
        width: element.offsetWidth,
        height: element.offsetHeight
      },
      position: {
        top: element.offsetTop,
        left: element.offsetLeft
      }
    };
  }

  /**
   * Capture a screenshot of a specific DOM element and convert it to a data URL
   * @param selector - CSS selector for the element to capture
   * @param label - Optional label for the screenshot
   */
  async captureElementScreenshot(selector: string, label?: string): Promise<string | null> {
    try {
      const element = document.querySelector(selector);
      if (!element) {
        this.log('css', 'Screenshot', `Element not found: ${selector}`, { selector });
        return null;
      }

      // Check if html2canvas is available
      if (typeof (window as any).html2canvas !== 'function') {
        this.log('css', 'Screenshot', 'html2canvas library is required but not available', { selector });
        console.warn('html2canvas library is required for element screenshots. Include it in your page.');
        return null;
      }

      const canvas = await (window as any).html2canvas(element);
      const dataUrl = canvas.toDataURL('image/png');
      
      this.log('css', 'Screenshot', `Captured screenshot of ${label || selector}`, {
        selector,
        element: this.getElementInfo(element as HTMLElement),
        screenshot: '(data URL too large to log)',
        timestamp: new Date().toISOString()
      });
      
      return dataUrl;
    } catch (error) {
      this.log('error', 'Screenshot', `Failed to capture screenshot: ${error}`, { selector, error });
      return null;
    }
  }

  /**
   * Log information about a specific CSS issue or observation
   * @param description - Description of the CSS issue
   * @param selector - CSS selector for the relevant element
   * @param details - Additional details about the CSS issue
   */
  cssIssue(description: string, selector: string, details?: any): void {
    const element = document.querySelector(selector);
    const elementInfo = element ? this.getElementInfo(element as HTMLElement) : { notFound: true };
    
    this.log('css', 'CSSIssue', description, {
      selector,
      element: elementInfo,
      details
    });
  }

  /**
   * Check CSS-related browser features
   * @returns DiagnosticResult with CSS feature support
   */
  checkCssFeatures(): DiagnosticResult {
    const features = {
      flexbox: this.testCssFeature('flex'),
      grid: this.testCssFeature('grid'),
      cssVariables: this.testCssFeature('--test'),
      transforms: this.testCssFeature('transform'),
      transitions: this.testCssFeature('transition'),
      animations: this.testCssFeature('animation')
    };
    
    const unsupportedFeatures = Object.entries(features)
      .filter(([, supported]) => !supported)
      .map(([feature]) => feature);
    
    return {
      status: unsupportedFeatures.length > 0 ? 'warning' : 'success',
      message: unsupportedFeatures.length > 0 
        ? `Some CSS features not supported: ${unsupportedFeatures.join(', ')}`
        : 'All CSS features supported',
      details: features
    };
  }

  /**
   * Test if a CSS feature is supported
   * @private
   */
  private testCssFeature(property: string): boolean {
    const element = document.createElement('div');
    try {
      if (property === '--test') {
        element.style.setProperty('--test', 'value');
        return element.style.getPropertyValue('--test') === 'value';
      } else {
        (element.style as any)[property] = 'test';
        return (element.style as any)[property] === 'test';
      }
    } catch (e) {
      return false;
    }
  }
}

// Export the singleton instance
export const bookingDebugger = new BookingDebugger();

// Expose the debugger to the window object for debugging purposes
if (typeof window !== 'undefined') {
  (window as any).bookingDebugger = bookingDebugger;
}