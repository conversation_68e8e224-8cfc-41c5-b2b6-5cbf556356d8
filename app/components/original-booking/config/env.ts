import { bookingDebugger } from '../utils/debugLogger';

// Log environment variables loading
bookingDebugger.log('info', 'Config', 'Loading environment variables...');
bookingDebugger.log('debug', 'Config', `MAPBOX_ACCESS_TOKEN: ${process.env.NEXT_PUBLIC_MAPBOX_TOKEN ? 'present' : 'missing'}`);

// Get configuration from WordPress if available
const wpConfig = typeof window !== 'undefined' && 'limoBookingConfig' in window 
  ? (window as any).limoBookingConfig || {}
  : {
      hereApiKey: '',
      apiBaseUrl: '',
      restUrl: '',
      nonce: '',
      mapboxToken: '',
      theme: {},
      isWordPress: false
    };

export const config = {
  weatherApiKey: process.env.NEXT_PUBLIC_OPENWEATHER_API_KEY || '',
  mapboxApiKey: process.env.NEXT_PUBLIC_MAPBOX_TOKEN || '',
  mapboxToken: wpConfig.mapboxToken || process.env.NEXT_PUBLIC_MAPBOX_TOKEN || '',
  hereApiKey: wpConfig.hereApiKey || process.env.NEXT_PUBLIC_HERE_API_KEY || '',
  aviationApiKey: process.env.NEXT_PUBLIC_AVIATION_API_KEY || '',
  stripePublicKey: process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY || '',
  apiUrl: wpConfig.apiBaseUrl || process.env.NEXT_PUBLIC_API_URL || '',
  appMode: process.env.NEXT_PUBLIC_APP_MODE || 'standalone',
  appTitle: process.env.NEXT_PUBLIC_APP_TITLE || 'Transflow SaaS',
  wpApiUrl: wpConfig.restUrl || '',
  wpNonce: wpConfig.nonce || '',
  isDebug: process.env.NODE_ENV === 'development',
  isWordPress: wpConfig.isWordPress || false,
} as const;

// Log config object
bookingDebugger.log('debug', 'Config', `Loaded config: ${JSON.stringify(config, null, 2)}`);

// Type for the config
export type Config = typeof config; 