import React from 'react';
import { useBookingStore } from '../../store/bookingStore';
import { BookingType } from '../../types/booking';
import { BookingTabs } from './BookingTabs';
import { ProgressSteps } from './ProgressSteps';
import { TripSummary } from './TripSummary';
import { PointToPointForm } from './Forms/PointToPointForm';
import { HourlyForm } from './Forms/HourlyForm';
import { AirportForm } from './Forms/AirportForm';
import { MultiDayForm } from './Forms/MultiDayForm';
import { VehicleSelection } from './VehicleSelection';
import { BookingConfirmation } from './BookingConfirmation';
import { MobileStepper } from './MobileStepper';
import { bookingDebugger } from '../../../utils/debugLogger';
import { DebugPanel } from '../DebugPanel';
import { useEffect, useState, useRef } from 'react';
import { cn, detectEnvironment } from '../../lib/utils';
import { ErrorBoundary } from '../ErrorBoundary';
import { ChevronRightIcon, XMarkIcon, SwatchIcon } from '@heroicons/react/24/outline';
import { ThemeSelection } from './ThemeSelection';
import type { BookingFormProps, BookingData } from './types';

// Declare global window interface to add our custom property
declare global {
  interface Window {
    closeTripSummary?: () => void;
  }
}

export const BookingForm: React.FC<BookingFormProps & { onBookingSubmit?: (data: BookingData) => Promise<any> }> = ({
  apiKeys,
  vehicleImages,
  ajaxUrl,
  nonce,
  formType = 'point-to-point',
  onBookingSubmit
}) => {
  const store = useBookingStore();
  const [isMobileSummaryOpen, setIsMobileSummaryOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [showDebugPanel, setShowDebugPanel] = useState(false);
  const [showThemeSelector, setShowThemeSelector] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const previousDataRef = useRef<string>("");
  
  // Check if we're in WordPress environment
  const isWordPress = window.limoBookingConfig?.isWordPress || false;

  useEffect(() => {
    // Initialize store with props
    store.setFormType(formType);

    // Set up global close function for trip summary
    window.closeTripSummary = () => setIsMobileSummaryOpen(false);

    // Clean up on unmount
    return () => {
      window.closeTripSummary = undefined;
    };
  }, [formType]);

  // Add effect to track form changes
  useEffect(() => {
    const currentData = JSON.stringify({
      activeTab: store.activeTab,
      bookingData: store.bookingData
    });

    if (previousDataRef.current && previousDataRef.current !== currentData) {
      setIsUpdating(true);

      // Hide the updating text after 1.5 seconds
      const timer = setTimeout(() => {
        setIsUpdating(false);
      }, 1500);

      return () => clearTimeout(timer);
    }

    previousDataRef.current = currentData;
  }, [store.activeTab, store.bookingData]);

  // Debug panel toggle
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        setShowDebugPanel(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  // Theme selector toggle
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'T') {
        setShowThemeSelector(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  const handleVehicleSelect = (vehicleId: string) => {
    store.setBookingData({ ...store.bookingData, vehicleId });
    store.setStep(3);
  };

  const handleBookingSubmit = async (data: BookingData) => {
    try {
      if (onBookingSubmit) {
        // Use the custom submit handler if provided (Next.js integration)
        const result = await onBookingSubmit(data);
        if (result.success) {
          store.setStep(4);
        }
      } else {
        // Fallback to original WordPress AJAX submission
        const response = await fetch(ajaxUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-WP-Nonce': nonce
          },
          body: JSON.stringify({
            action: 'limo_booking_submit',
            ...data
          })
        });

        if (!response.ok) {
          throw new Error('Failed to submit booking');
        }

        const result = await response.json();
        if (result.success) {
          store.setStep(4);
        } else {
          throw new Error(result.message || 'Failed to submit booking');
        }
      }
    } catch (error) {
      console.error('Booking submission error:', error);
      // Handle error state
    }
  };

  return (
    <ErrorBoundary>
      <div 
        ref={containerRef}
        className={cn(
          'relative min-h-[600px] w-full max-w-7xl mx-auto bg-background text-text-primary',
          'rounded-lg shadow-lg overflow-hidden'
        )}
      >
        {/* Mobile Trip Summary Toggle */}
        <div className="lg:hidden bg-surface p-4 border-b border-white/10">
          <button
            onClick={() => setIsMobileSummaryOpen(true)}
            className={cn(
              "w-full flex items-center justify-between p-4 rounded-lg",
              "bg-surface/80 backdrop-blur-sm transition-all duration-300",
              "hover:bg-surface-light relative overflow-hidden",
              isUpdating && "border-2 border-primary/50"
            )}
          >
            <span className="text-xl font-light text-white">Trip Summary</span>
            <div className="flex items-center gap-3">
              {isUpdating && (
                <div className="flex items-center gap-2">
                  <div 
                    className="animate-ping h-2 w-2 rounded-full bg-primary"
                  />
                  <span className="text-sm font-medium text-primary">Updating...</span>
                </div>
              )}
              <ChevronRightIcon className={cn(
                "w-5 h-5 transition-colors",
                isUpdating ? "text-primary" : "text-neutral-400"
              )} />
            </div>
          </button>
        </div>

        {/* Main Content */}
        <div className="flex flex-col lg:flex-row h-full">
          {/* Left Side - Trip Summary (Desktop) */}
          <div className="hidden lg:block lg:w-96 border-r border-surface-dark">
            <div className="sticky top-0 h-screen overflow-y-auto p-6">
              <TripSummary />
            </div>
          </div>

          {/* Right Side - Form */}
          <div className="flex-1 p-6">
            <BookingTabs />
            <ProgressSteps />
            
            <div className="mt-8">
              {store.currentStep === 1 && (
                <>
                  {store.activeTab === 'point-to-point' && <PointToPointForm />}
                  {store.activeTab === 'hourly' && <HourlyForm />}
                  {store.activeTab === 'airport' && <AirportForm />}
                  {store.activeTab === 'multi-day' && <MultiDayForm />}
                </>
              )}
              
              {store.currentStep === 2 && (
                <VehicleSelection
                  onSelect={handleVehicleSelect}
                  vehicleImages={vehicleImages}
                />
              )}
              {store.currentStep === 3 && (
                <BookingConfirmation
                  onSubmit={handleBookingSubmit}
                  apiKeys={apiKeys}
                  nonce={nonce}
                />
              )}
              {store.currentStep === 4 && (
                <div className="success-message">
                  <h2>Booking Confirmed!</h2>
                  <p>Thank you for your booking. We will contact you shortly with more details.</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Mobile Stepper */}
        <MobileStepper />

        {/* Debug Panel */}
        {showDebugPanel && <DebugPanel />}

        {/* Theme Selector */}
        {showThemeSelector && (
          <ThemeSelection
            isOpen={showThemeSelector}
            onClose={() => setShowThemeSelector(false)}
          />
        )}

        {/* Theme Toggle Button */}
        <button
          onClick={() => setShowThemeSelector(prev => !prev)}
          className="fixed bottom-4 right-4 p-2 rounded-full bg-primary text-white shadow-lg hover:bg-primary-dark transition-colors"
        >
          <SwatchIcon className="w-6 h-6" />
        </button>

        {/* Mobile Trip Summary Panel */}
        {isMobileSummaryOpen && (
          <>
            <div
              className="fixed inset-0 bg-black/50 z-40 lg:hidden"
              onClick={() => setIsMobileSummaryOpen(false)}
            />
            <div
              className={cn(
                "fixed inset-y-0 right-0 w-[90%] max-w-md bg-background z-50 lg:hidden",
                "transform transition-transform duration-300",
                isMobileSummaryOpen ? "translate-x-0" : "translate-x-full"
              )}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="relative h-full overflow-auto">
                <button 
                  onClick={() => setIsMobileSummaryOpen(false)}
                  className="absolute top-4 right-4 p-2 rounded-full bg-surface text-white hover:bg-surface-light"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
                <TripSummary inPanel={true} />
              </div>
            </div>
          </>
        )}
      </div>
    </ErrorBoundary>
  );
};
