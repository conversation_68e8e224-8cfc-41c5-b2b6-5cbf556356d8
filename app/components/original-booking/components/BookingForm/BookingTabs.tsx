import { useBookingStore } from '../../store/bookingStore';
import { cn } from '../../lib/utils';
import {
  ArrowsRightLeftIcon,
  ClockIcon,
  PaperAirplaneIcon,
  CalendarDaysIcon
} from '@heroicons/react/24/outline';
import type { BookingType } from '../../store/bookingStore';
import { useEffect } from 'react';

const tabs: Array<{ id: BookingType; label: string; icon: any }> = [
  {
    id: 'point-to-point',
    label: 'Point to Point',
    icon: ArrowsRightLeftIcon
  },
  {
    id: 'hourly',
    label: 'By the Hour',
    icon: ClockIcon
  },
  {
    id: 'airport',
    label: 'Airport Transfer',
    icon: PaperAirplaneIcon
  }
  // Temporarily hidden for current release
  // {
  //   id: 'multi-day',
  //   label: 'Event/Multi-Trips',
  //   icon: CalendarDaysIcon
  // }
];

export const BookingTabs = () => {
  const { formType, setFormType, setActiveTab } = useBookingStore();

  // Get theme config from WordPress
  const wpConfig = window.limoBookingConfig?.theme;
  const isWordPress = window.limoBookingConfig?.isWordPress;

  // Force white text colors for active state
  const activeStyles = {
    backgroundColor: wpConfig?.primary_color || 'var(--primary)',
    color: '#ffffff !important',
    fill: '#ffffff !important',
    stroke: '#ffffff !important',
  };

  const inactiveStyles = {
    backgroundColor: 'var(--surface-light)',
    color: 'var(--text-primary)',
    fill: 'var(--text-primary)',
    stroke: 'var(--text-primary)',
  };

  // Log theme config and environment on mount
  useEffect(() => {
    console.log('BookingTabs Initialization:', {
      environment: isWordPress ? 'WordPress' : 'Standalone',
      wpConfig,
      activeStyles,
      inactiveStyles,
      currentFormType: formType,
      timestamp: new Date().toISOString()
    });
  }, []);

  // Handle tab change
  const handleTabChange = (tabId: BookingType) => {
    if (tabId === formType) {
      console.log('Tab already active:', tabId);
      return;
    }

    console.log('Tab change initiated:', {
      from: formType,
      to: tabId,
      environment: isWordPress ? 'WordPress' : 'Standalone',
      timestamp: new Date().toISOString()
    });

    // Update both form type and active tab
    setFormType(tabId);
    setActiveTab(tabId);

    // Log state after change
    setTimeout(() => {
      const state = useBookingStore.getState();
      console.log('Store state after tab change:', {
        activeTab: state.activeTab,
        formType: state.formType,
        currentStep: state.currentStep,
        environment: isWordPress ? 'WordPress' : 'Standalone',
        timestamp: new Date().toISOString()
      });
    }, 0);
  };

  return (
    <div className="flex gap-2">
      {tabs.map((tab) => {
        const isActive = formType === tab.id;

        return (
          <button
            key={tab.id}
            onClick={() => handleTabChange(tab.id)}
            className={cn(
              "flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-lg transition-all",
              "font-medium text-sm",
              isActive
                ? "bg-primary !text-white [&_*]:!text-white [&_svg]:!text-white [&_svg]:!fill-white [&_svg]:!stroke-white"
                : "bg-surface text-primary hover:bg-surface-light"
            )}
            style={isActive ? activeStyles : inactiveStyles}
            data-active={isActive}
            data-tab-id={tab.id}
          >
            <tab.icon className="w-5 h-5" />
            <span>{tab.label}</span>
          </button>
        );
      })}
    </div>
  );
};
