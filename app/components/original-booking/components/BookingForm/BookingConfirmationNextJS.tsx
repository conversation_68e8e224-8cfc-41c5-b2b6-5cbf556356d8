import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useBookingStore } from '../../../store/bookingStore';
import { cn } from '../../../lib/utils';
import { toast } from 'react-hot-toast';
import { 
  UserCircleIcon, 
  ShieldCheckIcon, 
  ClockIcon, 
  CreditCardIcon,
  UserPlusIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

// Form Data Interfaces
interface ContactFormData {
  fullName: string;
  email: string;
  phone: string;
  specialRequests?: string;
}

interface BookingConfirmationProps {
  apiKeys: {
    here: string;
    mapbox: string;
    resend: string;
    salesmate: string;
  };
  nonce: string;
  onSubmit: (data: any) => Promise<void>;
}

export const BookingConfirmation: React.FC<BookingConfirmationProps> = ({
  apiKeys,
  nonce,
  onSubmit
}) => {
  const [createAccount, setCreateAccount] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [configError, setConfigError] = useState<string | null>(null);
  const { currentStep, setStep, formType, pointToPointData, hourlyData, airportData, multiDayData } = useBookingStore();

  const { register, handleSubmit, formState: { errors } } = useForm<ContactFormData>();

  const handleBack = () => {
    setStep(currentStep - 1);
  };

  const onFormSubmit = async (formData: ContactFormData) => {
    setIsSubmitting(true);
    setConfigError(null);

    try {
      // Get the active booking data
      let activeBookingData;
      switch (formType) {
        case 'point-to-point':
          activeBookingData = pointToPointData;
          break;
        case 'hourly':
          activeBookingData = hourlyData;
          break;
        case 'airport':
          activeBookingData = airportData;
          break;
        case 'multi-day':
          activeBookingData = multiDayData;
          break;
      }

      if (!activeBookingData) {
        throw new Error('Please complete the booking form before proceeding.');
      }

      // Validate required fields
      if (!activeBookingData.pickupLocation?.address) {
        throw new Error('Pickup location is required.');
      }

      if (!activeBookingData.pickupDate) {
        throw new Error('Pickup date is required.');
      }

      if (!activeBookingData.selectedVehicle) {
        throw new Error('Please select a vehicle.');
      }

      // Construct the booking data for our Next.js API
      const bookingData = {
        pickup_location: activeBookingData.pickupLocation.address,
        dropoff_location: activeBookingData.dropoffLocation?.address || '',
        pickup_date: activeBookingData.pickupDate,
        pickup_time: activeBookingData.pickupTime || '09:00',
        service_type: formType,
        passenger_count: (activeBookingData.adults || 1) + (activeBookingData.children || 0),
        adults: activeBookingData.adults || 1,
        children: activeBookingData.children || 0,
        special_instructions: formData.specialRequests,
        vehicle_type: activeBookingData.selectedVehicle,
        city: activeBookingData.pickupLocation.address?.split(',')[1]?.trim() || 'Unknown',
        // Contact info
        customer_name: formData.fullName,
        customer_email: formData.email,
        customer_phone: formData.phone,
        create_account: createAccount
      };

      console.log('Submitting booking data:', bookingData);

      // Submit to our Next.js API
      await onSubmit(bookingData);

      // Show success message
      toast.success('Your booking request has been submitted successfully!');

      // Move to next step (thank you page)
      setStep(4);
    } catch (error) {
      console.error('Submission error:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      if (errorMessage.includes('API Key is missing') || 
          errorMessage.includes('administrator to set up') ||
          errorMessage.includes('administrator to verify')) {
        setConfigError(errorMessage);
      } else {
        toast.error(errorMessage || 'There was an error submitting your booking request. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={cn("booking-confirmation-container p-6")}>
      <div className="relative">
        {isSubmitting && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 rounded-lg">
            <div className="bg-white p-4 rounded-lg">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-sm text-gray-600">Submitting your request...</p>
            </div>
          </div>
        )}
        
        <h2 className="text-2xl font-bold mb-6 text-primary">Request a Quote</h2>
        
        {configError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mt-0.5 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-red-800">Configuration Error</h3>
                <p className="text-sm text-red-700 mt-1">{configError}</p>
                <p className="text-xs text-red-600 mt-2">
                  This is a website configuration issue. Please contact the site administrator.
                </p>
              </div>
            </div>
          </div>
        )}
        
        <div className="bg-surface-dark p-6 rounded-lg border border-border mb-8">
          <form onSubmit={handleSubmit(onFormSubmit)}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-theme-primary mb-1">
                  Full Name *
                </label>
                <input
                  type="text"
                  placeholder="Enter your full name"
                  {...register('fullName', { required: true })}
                  className={cn(
                    'w-full h-[46px] bg-surface text-theme-primary border border-border rounded-lg px-4 py-2',
                    'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary',
                    errors.fullName && 'border-red-400 focus:border-red-400 focus:ring-red-400/50'
                  )}
                />
                {errors.fullName && (
                  <p className="text-sm text-red-400 mt-1">Full name is required</p>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-theme-primary mb-1">
                  Email *
                </label>
                <input
                  type="email"
                  placeholder="Enter your email address"
                  {...register('email', { required: true, pattern: /^\S+@\S+$/i })}
                  className={cn(
                    'w-full h-[46px] bg-surface text-theme-primary border border-border rounded-lg px-4 py-2',
                    'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary',
                    errors.email && 'border-red-400 focus:border-red-400 focus:ring-red-400/50'
                  )}
                />
                {errors.email && (
                  <p className="text-sm text-red-400 mt-1">Valid email is required</p>
                )}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-theme-primary mb-1">
                  Phone Number *
                </label>
                <input
                  type="tel"
                  placeholder="Enter your phone number"
                  {...register('phone', { required: true })}
                  className={cn(
                    'w-full h-[46px] bg-surface text-theme-primary border border-border rounded-lg px-4 py-2',
                    'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary',
                    errors.phone && 'border-red-400 focus:border-red-400 focus:ring-red-400/50'
                  )}
                />
                {errors.phone && (
                  <p className="text-sm text-red-400 mt-1">Phone number is required</p>
                )}
              </div>
              
              <div className="flex items-end pb-2">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={createAccount}
                    onChange={(e) => setCreateAccount(e.target.checked)}
                    className="w-4 h-4 text-primary bg-surface border-border rounded focus:ring-primary focus:ring-2"
                  />
                  <UserPlusIcon className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-theme-muted">Create Account</span>
                </label>
              </div>
            </div>
            
            <div className="mb-6">
              <label className="block text-sm font-medium text-theme-primary mb-1">
                Special Requests
              </label>
              <textarea
                placeholder="Any special requests or instructions"
                rows={3}
                {...register('specialRequests')}
                className="w-full bg-surface text-theme-primary border border-border rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary resize-none"
              />
            </div>
            
            {/* Service Guarantees */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div className="bg-surface-light p-4 rounded-lg border border-border">
                <div className="flex items-center gap-2 mb-2">
                  <ShieldCheckIcon className="w-5 h-5 text-primary" />
                  <span className="text-sm font-medium text-primary">Insured Service</span>
                </div>
                <p className="text-xs text-theme-muted">Full coverage for your journey</p>
              </div>
              
              <div className="bg-surface-light p-4 rounded-lg border border-border">
                <div className="flex items-center gap-2 mb-2">
                  <CreditCardIcon className="w-5 h-5 text-primary" />
                  <span className="text-sm font-medium text-primary">Secure Payment</span>
                </div>
                <p className="text-xs text-theme-muted">Your payment information is safe</p>
              </div>
            </div>
            
            {/* Cancellation Policy */}
            <div className="bg-surface-light p-4 rounded-lg border border-border mb-6">
              <h3 className="text-sm font-medium text-primary mb-3 flex items-center gap-2">
                <ClockIcon className="w-4 h-4" />
                Cancellation Policy
              </h3>
              <div className="space-y-2 text-xs">
                <div className="flex items-center gap-2">
                  <CheckCircleIcon className="w-4 h-4 text-green-500" />
                  <span className="text-theme-muted">100% refund if cancelled 72 hours before</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircleIcon className="w-4 h-4 text-yellow-500" />
                  <span className="text-theme-muted">50% refund if cancelled 48 hours before</span>
                </div>
                <div className="flex items-center gap-2">
                  <ExclamationTriangleIcon className="w-4 h-4 text-red-500" />
                  <span className="text-theme-muted">Full charge if cancelled within 24 hours</span>
                </div>
              </div>
            </div>
            
            <div className="border-t border-border pt-6">
              <div className="flex justify-between">
                <button
                  type="button"
                  onClick={handleBack}
                  disabled={isSubmitting}
                  className="px-6 py-2 text-theme-muted border border-border rounded-lg hover:bg-surface-light transition-colors disabled:opacity-50"
                >
                  Back
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50 min-w-[150px]"
                >
                  {isSubmitting ? 'Submitting...' : 'Request Quote'}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
