import React, { useState, useEffect } from 'react';
import { useBookingStore } from '../../store/bookingStore';
import { cn } from '../../lib/utils';
import { toast } from 'react-hot-toast';
import { 
  BuildingOfficeIcon,
  StarIcon,
  ClockIcon,
  CheckBadgeIcon,
  TruckIcon,
  MapPinIcon,
  PhoneIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

interface Affiliate {
  id: string;
  company_name: string;
  city: string;
  state: string;
  rating: number;
  avg_response_time: string;
  on_time_rate: number;
  completed_trips: number;
  base_rate: number;
  verified: boolean;
  status: 'active' | 'inactive';
  phone_number?: string;
  email?: string;
}

interface AffiliateSelectionStepProps {
  onSubmit: (data: any) => Promise<void>;
}

export const AffiliateSelectionStep: React.FC<AffiliateSelectionStepProps> = ({ onSubmit }) => {
  const [affiliates, setAffiliates] = useState<Affiliate[]>([]);
  const [selectedAffiliate, setSelectedAffiliate] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { 
    currentStep, 
    setStep, 
    formType, 
    pointToPointData, 
    hourlyData, 
    airportData, 
    multiDayData 
  } = useBookingStore();

  // Get active booking data
  const getActiveBookingData = () => {
    switch (formType) {
      case 'point-to-point': return pointToPointData;
      case 'hourly': return hourlyData;
      case 'airport': return airportData;
      case 'multi-day': return multiDayData;
      default: return pointToPointData;
    }
  };

  const activeBookingData = getActiveBookingData();

  // Fetch matching affiliates
  const fetchMatchingAffiliates = async () => {
    if (!activeBookingData?.pickupLocation?.address) {
      setError('Pickup location is required to find affiliates');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const city = activeBookingData.pickupLocation.address.split(',')[1]?.trim() || 'Unknown';
      const vehicleType = activeBookingData.selectedVehicle || 'sedan';
      const serviceType = formType;
      const passengers = (activeBookingData.adults || 1) + (activeBookingData.children || 0);

      console.log('Fetching affiliates with:', {
        city,
        vehicleType,
        serviceType,
        date: activeBookingData.pickupDate,
        time: activeBookingData.pickupTime,
        passengers
      });

      const response = await fetch('/api/event-manager/quotes/match-affiliates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          city,
          vehicleType,
          serviceType,
          date: activeBookingData.pickupDate,
          time: activeBookingData.pickupTime || '09:00',
          passengers
        })
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Received affiliates:', data.affiliates?.length || 0);
        setAffiliates(data.affiliates || []);
        
        if (!data.affiliates || data.affiliates.length === 0) {
          setError('No affiliates found for your location and requirements. Please try a different location or contact us directly.');
        }
      } else {
        const errorText = await response.text();
        console.error('Failed to fetch affiliates:', response.status, errorText);
        setError('Failed to find available affiliates. Please try again.');
      }
    } catch (error) {
      console.error('Error fetching affiliates:', error);
      setError('Network error. Please check your connection and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Load affiliates on component mount
  useEffect(() => {
    fetchMatchingAffiliates();
  }, []);

  const handleBack = () => {
    setStep(currentStep - 1);
  };

  const handleAffiliateSelect = (affiliateId: string) => {
    setSelectedAffiliate(affiliateId);
  };

  const handleSubmit = async () => {
    if (!selectedAffiliate) {
      toast.error('Please select an affiliate to proceed');
      return;
    }

    const affiliate = affiliates.find(a => a.id === selectedAffiliate);
    if (!affiliate) {
      toast.error('Selected affiliate not found');
      return;
    }

    setIsSubmitting(true);

    try {
      // Construct booking data with affiliate information
      const bookingData = {
        pickup_location: activeBookingData.pickupLocation?.address,
        dropoff_location: activeBookingData.dropoffLocation?.address || '',
        pickup_date: activeBookingData.pickupDate,
        pickup_time: activeBookingData.pickupTime || '09:00',
        service_type: formType,
        passenger_count: (activeBookingData.adults || 1) + (activeBookingData.children || 0),
        adults: activeBookingData.adults || 1,
        children: activeBookingData.children || 0,
        vehicle_type: activeBookingData.selectedVehicle,
        city: activeBookingData.pickupLocation?.address?.split(',')[1]?.trim() || 'Unknown',
        
        // Affiliate information
        selected_affiliate_id: affiliate.id,
        affiliate_company_name: affiliate.company_name,
        estimated_rate: affiliate.base_rate,
        
        // SaaS integration data
        event_id: activeBookingData.eventId,
        passenger_ids: activeBookingData.passengerIds || [],
        is_standalone: activeBookingData.isStandalone ?? true,
      };

      console.log('Submitting booking with affiliate:', bookingData);

      await onSubmit(bookingData);
      
      toast.success('Quote request sent to affiliate successfully!');
      setStep(4); // Move to success step
    } catch (error) {
      console.error('Submission error:', error);
      toast.error('Failed to submit quote request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatRating = (rating: number) => {
    return rating ? rating.toFixed(1) : 'N/A';
  };

  const formatOnTimeRate = (rate: number) => {
    return rate ? `${(rate * 100).toFixed(0)}%` : 'N/A';
  };

  return (
    <div className={cn("affiliate-selection-container p-6")}>
      <div className="relative">
        {(isLoading || isSubmitting) && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 rounded-lg">
            <div className="bg-white p-4 rounded-lg">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto">
                <ArrowPathIcon className="h-8 w-8" />
              </div>
              <p className="mt-2 text-sm text-gray-600">
                {isLoading ? 'Finding available affiliates...' : 'Submitting your request...'}
              </p>
            </div>
          </div>
        )}
        
        <h2 className="text-2xl font-bold mb-6 text-primary">Select Transportation Provider</h2>
        
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mt-0.5 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
                <button
                  onClick={fetchMatchingAffiliates}
                  className="mt-2 text-xs text-red-600 underline hover:text-red-800"
                >
                  Try again
                </button>
              </div>
            </div>
          </div>
        )}

        {!isLoading && !error && affiliates.length === 0 && (
          <div className="text-center py-8">
            <BuildingOfficeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Affiliates Available</h3>
            <p className="text-gray-600 mb-4">
              We couldn't find any transportation providers for your location and requirements.
            </p>
            <button
              onClick={fetchMatchingAffiliates}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
            >
              Search Again
            </button>
          </div>
        )}

        {affiliates.length > 0 && (
          <div className="space-y-4 mb-8">
            <p className="text-sm text-gray-600 mb-4">
              Found {affiliates.length} transportation provider{affiliates.length !== 1 ? 's' : ''} available for your request:
            </p>
            
            {affiliates.map((affiliate) => (
              <div
                key={affiliate.id}
                onClick={() => handleAffiliateSelect(affiliate.id)}
                className={cn(
                  'border rounded-lg p-6 cursor-pointer transition-all',
                  selectedAffiliate === affiliate.id
                    ? 'border-primary bg-primary/5 ring-2 ring-primary/20'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                )}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <BuildingOfficeIcon className="h-6 w-6 text-primary" />
                      <h3 className="text-lg font-semibold text-gray-900">{affiliate.company_name}</h3>
                      {affiliate.verified && (
                        <CheckBadgeIcon className="h-5 w-5 text-green-500" />
                      )}
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center gap-1">
                        <MapPinIcon className="h-4 w-4" />
                        {affiliate.city}, {affiliate.state}
                      </div>
                      {affiliate.phone_number && (
                        <div className="flex items-center gap-1">
                          <PhoneIcon className="h-4 w-4" />
                          {affiliate.phone_number}
                        </div>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center gap-1">
                        <StarIcon className="h-4 w-4 text-yellow-500" />
                        <span className="font-medium">{formatRating(affiliate.rating)}</span>
                        <span className="text-gray-500">rating</span>
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <ClockIcon className="h-4 w-4 text-blue-500" />
                        <span className="font-medium">{affiliate.avg_response_time || '< 15 min'}</span>
                        <span className="text-gray-500">response</span>
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <CheckBadgeIcon className="h-4 w-4 text-green-500" />
                        <span className="font-medium">{formatOnTimeRate(affiliate.on_time_rate)}</span>
                        <span className="text-gray-500">on-time</span>
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <TruckIcon className="h-4 w-4 text-purple-500" />
                        <span className="font-medium">{affiliate.completed_trips || 0}</span>
                        <span className="text-gray-500">trips</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-lg font-bold text-primary">
                      ${affiliate.base_rate || 'Quote'}
                    </div>
                    <div className="text-xs text-gray-500">Base rate</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
        
        <div className="border-t border-gray-200 pt-6">
          <div className="flex justify-between">
            <button
              type="button"
              onClick={handleBack}
              disabled={isSubmitting}
              className="px-6 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
            >
              Back
            </button>
            <button
              onClick={handleSubmit}
              disabled={isSubmitting || !selectedAffiliate}
              className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50 min-w-[150px]"
            >
              {isSubmitting ? 'Submitting...' : 'Request Quote'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
