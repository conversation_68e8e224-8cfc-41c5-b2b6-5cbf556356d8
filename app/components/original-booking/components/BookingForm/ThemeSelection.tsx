import React from 'react';
import { Dialog } from '@headlessui/react';
import { useThemeStore } from '../../store/themeStore';
import { ColorScheme, BackgroundMode, FontColors } from '../../types/theme';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';
import { themeService } from '../../../services/themeService';
import { useEffect, useState, useRef } from 'react';

interface ColorTheme {
  id: string;
  name: string;
  description: string;
  colors: {
    base: string;
    light: string;
    dark: string;
  };
}

interface BackgroundTheme {
  id: string;
  name: string;
  colors: {
    main: string;
    surface: string;
    surfaceDark: string;
  };
}

interface FontTheme {
  id: string;
  name: string;
  colors: {
    primary: string;
    secondary: string;
    muted: string;
    disabled: string;
  };
}

const colorThemes: ColorTheme[] = [
  {
    id: 'classic-brown',
    name: 'Classic Brown',
    description: 'Elegant and sophisticated brown theme',
    colors: {
      base: '#765a3d',
      light: '#8b6d4c',
      dark: '#5d472f',
    },
  },
  {
    id: 'ocean-blue',
    name: 'Ocean Blue',
    description: 'Professional and trustworthy blue theme',
    colors: {
      base: '#2563eb',
      light: '#3b82f6',
      dark: '#1d4ed8',
    },
  },
  {
    id: 'forest-green',
    name: 'Forest Green',
    description: 'Natural and calming green theme',
    colors: {
      base: '#16a34a',
      light: '#22c55e',
      dark: '#15803d',
    },
  },
  {
    id: 'royal-purple',
    name: 'Royal Purple',
    description: 'Luxurious and premium purple theme',
    colors: {
      base: '#7e22ce',
      light: '#9333ea',
      dark: '#6b21a8',
    },
  },
  {
    id: 'ruby-red',
    name: 'Ruby Red',
    description: 'Bold and energetic red theme',
    colors: {
      base: '#dc2626',
      light: '#ef4444',
      dark: '#b91c1c',
    },
  },
  {
    id: 'sunset-orange',
    name: 'Sunset Orange',
    description: 'Warm and inviting orange theme',
    colors: {
      base: '#ea580c',
      light: '#f97316',
      dark: '#c2410c',
    },
  },
  {
    id: 'tropical-teal',
    name: 'Tropical Teal',
    description: 'Calming and professional teal theme',
    colors: {
      base: '#0d9488',
      light: '#14b8a6',
      dark: '#0f766e',
    },
  },
  {
    id: 'deep-indigo',
    name: 'Deep Indigo',
    description: 'Modern and sophisticated indigo theme',
    colors: {
      base: '#4338ca',
      light: '#4f46e5',
      dark: '#3730a3',
    },
  },
  {
    id: 'midnight-dark',
    name: 'Midnight Dark',
    description: 'Sleek and modern dark theme',
    colors: {
      base: '#18181b',
      light: '#27272a',
      dark: '#09090b',
    },
  },
];

const backgroundThemes: BackgroundTheme[] = [
  {
    id: 'dark',
    name: 'Dark Mode',
    colors: {
      main: '#000000',
      surface: '#141414',
      surfaceDark: '#1A1A1A',
    },
  },
  {
    id: 'light',
    name: 'Light Mode',
    colors: {
      main: '#ffffff',
      surface: '#f3f4f6',
      surfaceDark: '#e5e7eb',
    },
  },
  {
    id: 'slate',
    name: 'Slate',
    colors: {
      main: '#0f172a',
      surface: '#1e293b',
      surfaceDark: '#334155',
    },
  },
  {
    id: 'navy',
    name: 'Navy',
    colors: {
      main: '#020617',
      surface: '#0f172a',
      surfaceDark: '#1e293b',
    },
  },
];

const fontThemes: FontTheme[] = [
  {
    id: 'light-mode',
    name: 'Dark on Light',
    colors: {
      primary: '#000000',
      secondary: '#374151',
      muted: '#6b7280',
      disabled: '#9ca3af',
    },
  },
  {
    id: 'dark-mode',
    name: 'Light on Dark',
    colors: {
      primary: '#ffffff',
      secondary: '#e5e7eb',
      muted: '#9ca3af',
      disabled: '#6b7280',
    },
  },
];

// Helper function to convert hex to RGB
const hexToRgb = (hex: string) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};

// Helper function to generate lighter and darker shades of a color
const generateColorVariations = (baseColor: string) => {
  const rgb = hexToRgb(baseColor);
  if (!rgb) return { light: baseColor, dark: baseColor };

  // Generate lighter shade (increase brightness)
  const lighterR = Math.min(255, Math.round(rgb.r * 1.2));
  const lighterG = Math.min(255, Math.round(rgb.g * 1.2));
  const lighterB = Math.min(255, Math.round(rgb.b * 1.2));
  
  // Generate darker shade (decrease brightness)
  const darkerR = Math.round(rgb.r * 0.8);
  const darkerG = Math.round(rgb.g * 0.8);
  const darkerB = Math.round(rgb.b * 0.8);
  
  // Convert back to hex
  const lightHex = `#${lighterR.toString(16).padStart(2, '0')}${lighterG.toString(16).padStart(2, '0')}${lighterB.toString(16).padStart(2, '0')}`;
  const darkHex = `#${darkerR.toString(16).padStart(2, '0')}${darkerG.toString(16).padStart(2, '0')}${darkerB.toString(16).padStart(2, '0')}`;
  
  return { light: lightHex, dark: darkHex };
};

// Helper function to generate surface variations from a background color
const generateSurfaceVariations = (backgroundColor: string) => {
  const rgb = hexToRgb(backgroundColor);
  if (!rgb) return { surface: backgroundColor, surfaceDark: backgroundColor };

  // For dark backgrounds, make surface slightly lighter
  // For light backgrounds, make surface slightly darker
  const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
  let surfaceR, surfaceG, surfaceB, surfaceDarkR, surfaceDarkG, surfaceDarkB;
  
  if (brightness < 128) {
    // Dark background - make surface lighter
    surfaceR = Math.min(255, Math.round(rgb.r * 1.2));
    surfaceG = Math.min(255, Math.round(rgb.g * 1.2));
    surfaceB = Math.min(255, Math.round(rgb.b * 1.2));
    
    // Make surface dark even lighter
    surfaceDarkR = Math.min(255, Math.round(rgb.r * 1.4));
    surfaceDarkG = Math.min(255, Math.round(rgb.g * 1.4));
    surfaceDarkB = Math.min(255, Math.round(rgb.b * 1.4));
  } else {
    // Light background - make surface darker
    surfaceR = Math.round(rgb.r * 0.95);
    surfaceG = Math.round(rgb.g * 0.95);
    surfaceB = Math.round(rgb.b * 0.95);
    
    // Make surface dark even darker
    surfaceDarkR = Math.round(rgb.r * 0.9);
    surfaceDarkG = Math.round(rgb.g * 0.9);
    surfaceDarkB = Math.round(rgb.b * 0.9);
  }
  
  // Convert back to hex
  const surfaceHex = `#${surfaceR.toString(16).padStart(2, '0')}${surfaceG.toString(16).padStart(2, '0')}${surfaceB.toString(16).padStart(2, '0')}`;
  const surfaceDarkHex = `#${surfaceDarkR.toString(16).padStart(2, '0')}${surfaceDarkG.toString(16).padStart(2, '0')}${surfaceDarkB.toString(16).padStart(2, '0')}`;
  
  return { surface: surfaceHex, surfaceDark: surfaceDarkHex };
};

// Add a constant to identify custom theme in localStorage
const CUSTOM_THEME_KEY = 'limo-booking-custom-theme';
const MAP_THEME_KEY = 'limo-booking-map-theme';

interface ThemeSelectionProps {
  isOpen: boolean;
  onClose: () => void;
}

type MapTheme = 'dark' | 'light';

const mapThemes: Record<MapTheme, {
  name: string;
  description: string;
  style: string;
  preview: string;
}> = {
  dark: {
    name: 'Dark Map',
    description: 'Dark themed map for dark mode interfaces',
    style: 'mapbox://styles/mapbox/dark-v11',
    preview: '#242f3e'
  },
  light: {
    name: 'Light Map',
    description: 'Light themed map for light mode interfaces',
    style: 'mapbox://styles/mapbox/light-v11',
    preview: '#ffffff'
  }
};

// Helper function to directly set map style in CSS and dispatch event
const applyMapTheme = (theme: 'dark' | 'light') => {
  try {
    const style = theme === 'dark' 
      ? 'mapbox://styles/mapbox/dark-v11' 
      : 'mapbox://styles/mapbox/light-v11';
    
    // Set CSS variable for map style
    document.documentElement.style.setProperty('--map-style', style);
    
    // Save map theme to localStorage
    localStorage.setItem(MAP_THEME_KEY, theme);
    
    // Dispatch event for map components
    window.dispatchEvent(new CustomEvent('mapthemechange', {
      detail: { theme, style }
    }));
    
    console.log('Applied map theme:', { theme, style });
  } catch (error) {
    console.error('Error applying map theme:', error);
  }
};

export const ThemeSelection: React.FC<ThemeSelectionProps> = ({ isOpen, onClose }) => {
  const { colorScheme, backgroundMode, fontColors, mapTheme, setColorScheme, setBackgroundMode, setFontColors, setMapTheme } = useThemeStore();

  const [selectedColor, setSelectedColor] = useState(() => {
    const saved = themeService.loadTheme();
    return saved?.color.base || colorThemes[0].colors.base;
  });
  
  // Custom theme state
  const [customPrimaryColor, setCustomPrimaryColor] = useState(() => {
    // Try to load custom theme from localStorage
    const savedCustomTheme = localStorage.getItem(CUSTOM_THEME_KEY);
    if (savedCustomTheme) {
      try {
        const parsed = JSON.parse(savedCustomTheme);
        return parsed.primaryColor || '#765a3d';
      } catch (e) {
        console.error('Error parsing saved custom theme:', e);
      }
    }
    return '#765a3d';
  });
  
  const [customBackgroundColor, setCustomBackgroundColor] = useState(() => {
    // Try to load custom theme from localStorage
    const savedCustomTheme = localStorage.getItem(CUSTOM_THEME_KEY);
    if (savedCustomTheme) {
      try {
        const parsed = JSON.parse(savedCustomTheme);
        return parsed.backgroundColor || '#000000';
      } catch (e) {
        console.error('Error parsing saved custom theme:', e);
      }
    }
    return '#000000';
  });
  
  const [showCustomTheme, setShowCustomTheme] = useState(false);
  const [isCustomThemeActive, setIsCustomThemeActive] = useState(() => {
    // Check if custom theme was active
    const savedCustomTheme = localStorage.getItem(CUSTOM_THEME_KEY);
    if (savedCustomTheme) {
      try {
        const parsed = JSON.parse(savedCustomTheme);
        return parsed.isActive || false;
      } catch (e) {
        console.error('Error parsing saved custom theme:', e);
      }
    }
    return false;
  });

  // Define applyColorTheme function
  const applyColorTheme = (theme: typeof colorThemes[0]) => {
    const root = document.documentElement;
    root.style.setProperty('--primary', theme.colors.base);
    root.style.setProperty('--primary-light', theme.colors.light);
    root.style.setProperty('--primary-dark', theme.colors.dark);

    const baseRgb = hexToRgb(theme.colors.base);
    if (baseRgb) {
      root.style.setProperty(
        '--primary-transparent',
        `rgba(${baseRgb.r}, ${baseRgb.g}, ${baseRgb.b}, 0.5)`
      );
    }

    // Get current background and font themes from CSS variables
    const currentBackground = {
      main: getComputedStyle(root).getPropertyValue('--background').trim(),
      surface: getComputedStyle(root).getPropertyValue('--surface').trim(),
      surfaceDark: getComputedStyle(root).getPropertyValue('--surface-dark').trim()
    };

    const currentFont = {
      primary: getComputedStyle(root).getPropertyValue('--text-primary').trim(),
      secondary: getComputedStyle(root).getPropertyValue('--text-secondary').trim(),
      muted: getComputedStyle(root).getPropertyValue('--text-muted').trim(),
      disabled: getComputedStyle(root).getPropertyValue('--text-disabled').trim()
    };

    // Save theme to localStorage with current background and font themes
    themeService.saveTheme({
      color: theme.colors,
      background: currentBackground,
      font: currentFont
    });

    // If custom theme was active, mark it as inactive
    if (isCustomThemeActive) {
      setIsCustomThemeActive(false);
      localStorage.setItem(CUSTOM_THEME_KEY, JSON.stringify({
        primaryColor: customPrimaryColor,
        backgroundColor: customBackgroundColor,
        isActive: false,
        timestamp: new Date().toISOString()
      }));
    }

    setSelectedColor(theme.colors.base);
  };

  // Define applyBackgroundTheme function
  const applyBackgroundTheme = (theme: typeof backgroundThemes[0]) => {
    const root = document.documentElement;
    root.style.setProperty('--background', theme.colors.main);
    root.style.setProperty('--surface', theme.colors.surface);
    root.style.setProperty('--surface-dark', theme.colors.surfaceDark);
    root.style.setProperty('--surface-light', `${theme.colors.surface}33`);
    root.style.setProperty('--border', '#333333');
    root.style.setProperty('--border-hover', '#4D4D4D');

    // Update overlay colors
    root.style.setProperty('--overlay-light', 'rgba(255, 255, 255, 0.05)');
    root.style.setProperty('--overlay-medium', 'rgba(255, 255, 255, 0.1)');
    root.style.setProperty('--overlay-dark', 'rgba(0, 0, 0, 0.5)');

    // Save current theme state
    const currentColor = {
      base: getComputedStyle(root).getPropertyValue('--primary').trim(),
      light: getComputedStyle(root).getPropertyValue('--primary-light').trim(),
      dark: getComputedStyle(root).getPropertyValue('--primary-dark').trim()
    };

    const currentFont = {
      primary: getComputedStyle(root).getPropertyValue('--text-primary').trim(),
      secondary: getComputedStyle(root).getPropertyValue('--text-secondary').trim(),
      muted: getComputedStyle(root).getPropertyValue('--text-muted').trim(),
      disabled: getComputedStyle(root).getPropertyValue('--text-disabled').trim()
    };

    themeService.saveTheme({
      color: currentColor,
      background: theme.colors,
      font: currentFont
    });
  };

  // Define applyFontTheme function
  const applyFontTheme = (theme: typeof fontThemes[0]) => {
    const root = document.documentElement;
    root.style.setProperty('--text-primary', theme.colors.primary);
    root.style.setProperty('--text-secondary', theme.colors.secondary);
    root.style.setProperty('--text-muted', theme.colors.muted);
    root.style.setProperty('--text-disabled', theme.colors.disabled);

    // Save current theme state
    const currentColor = {
      base: getComputedStyle(root).getPropertyValue('--primary').trim(),
      light: getComputedStyle(root).getPropertyValue('--primary-light').trim(),
      dark: getComputedStyle(root).getPropertyValue('--primary-dark').trim()
    };

    const currentBackground = {
      main: getComputedStyle(root).getPropertyValue('--background').trim(),
      surface: getComputedStyle(root).getPropertyValue('--surface').trim(),
      surfaceDark: getComputedStyle(root).getPropertyValue('--surface-dark').trim()
    };

    themeService.saveTheme({
      color: currentColor,
      background: currentBackground,
      font: theme.colors
    });
  };

  // Define applyCustomTheme function
  const applyCustomTheme = (saveToStorageOrEvent: boolean | React.MouseEvent = true) => {
    // Determine if this is called from an event handler or directly
    const saveToStorage = typeof saveToStorageOrEvent === 'boolean' ? saveToStorageOrEvent : true;
    
    // Generate color variations
    const primaryVariations = generateColorVariations(customPrimaryColor);
    const surfaceVariations = generateSurfaceVariations(customBackgroundColor);
    
    // Create theme objects
    const customColorTheme = {
      id: 'custom',
      name: 'Custom',
      description: 'Your custom theme',
      colors: {
        base: customPrimaryColor,
        light: primaryVariations.light,
        dark: primaryVariations.dark,
      },
    };
    
    const customBackgroundTheme = {
      id: 'custom',
      name: 'Custom',
      colors: {
        main: customBackgroundColor,
        surface: surfaceVariations.surface,
        surfaceDark: surfaceVariations.surfaceDark,
      },
    };
    
    // Apply the custom theme
    const root = document.documentElement;
    
    // Apply color theme
    root.style.setProperty('--primary', customColorTheme.colors.base);
    root.style.setProperty('--primary-light', customColorTheme.colors.light);
    root.style.setProperty('--primary-dark', customColorTheme.colors.dark);
    
    // Apply background theme
    root.style.setProperty('--background', customBackgroundTheme.colors.main);
    root.style.setProperty('--surface', customBackgroundTheme.colors.surface);
    root.style.setProperty('--surface-dark', customBackgroundTheme.colors.surfaceDark);
    
    // Calculate and set transparent variants
    const baseRgb = hexToRgb(customColorTheme.colors.base);
    if (baseRgb) {
      root.style.setProperty(
        '--primary-transparent',
        `rgba(${baseRgb.r}, ${baseRgb.g}, ${baseRgb.b}, 0.5)`
      );
    }
    
    // Determine appropriate font colors based on background brightness
    const bgRgb = hexToRgb(customBackgroundColor);
    let fontTheme;
    if (bgRgb) {
      const brightness = (bgRgb.r * 299 + bgRgb.g * 587 + bgRgb.b * 114) / 1000;
      if (brightness < 128) {
        // Dark background - use light text
        fontTheme = fontThemes[1]; // Light on Dark
      } else {
        // Light background - use dark text
        fontTheme = fontThemes[0]; // Dark on Light
      }
      
      // Apply font theme
      root.style.setProperty('--text-primary', fontTheme.colors.primary);
      root.style.setProperty('--text-secondary', fontTheme.colors.secondary);
      root.style.setProperty('--text-muted', fontTheme.colors.muted);
      root.style.setProperty('--text-disabled', fontTheme.colors.disabled);
    }
    
    // Save the complete theme to themeService
    themeService.saveTheme({
      color: customColorTheme.colors,
      background: customBackgroundTheme.colors,
      font: fontTheme ? fontTheme.colors : {
        primary: '#ffffff',
        secondary: '#e5e7eb',
        muted: '#9ca3af',
        disabled: '#6b7280'
      }
    });
    
    setSelectedColor(customPrimaryColor);
    setIsCustomThemeActive(true);
    
    // Save custom theme to localStorage for persistence
    if (saveToStorage) {
      localStorage.setItem(CUSTOM_THEME_KEY, JSON.stringify({
        primaryColor: customPrimaryColor,
        backgroundColor: customBackgroundColor,
        mapTheme: mapTheme,
        isActive: true,
        timestamp: new Date().toISOString()
      }));
    }
    
    // Log the applied custom theme for debugging
    console.log('Applied custom theme:', {
      primary: customPrimaryColor,
      background: customBackgroundColor,
      mapTheme: mapTheme,
      timestamp: new Date().toISOString()
    });
  };

  // Handle map theme change
  const handleMapThemeChange = (theme: 'dark' | 'light') => {
    setMapTheme(theme);
    applyMapTheme(theme);
    
    // If custom theme is active, update its saved state with the new map theme
    if (isCustomThemeActive) {
      localStorage.setItem(CUSTOM_THEME_KEY, JSON.stringify({
        primaryColor: customPrimaryColor,
        backgroundColor: customBackgroundColor,
        mapTheme: theme,
        isActive: true,
        timestamp: new Date().toISOString()
      }));
    }
  };

  // Apply saved custom theme on initial load if it was active
  useEffect(() => {
    if (isCustomThemeActive) {
      console.log('Applying saved custom theme on initial load');
      applyCustomTheme(false); // Don't save again to avoid circular calls
    }
    
    // Load saved map theme
    const savedMapTheme = localStorage.getItem(MAP_THEME_KEY);
    if (savedMapTheme && (savedMapTheme === 'dark' || savedMapTheme === 'light')) {
      console.log('Applying saved map theme on initial load:', savedMapTheme);
      setMapTheme(savedMapTheme);
      applyMapTheme(savedMapTheme);
    }
  }, []);

  useEffect(() => {
    const saved = themeService.loadTheme();
    if (saved) {
      setSelectedColor(saved.color.base);

      // Apply all theme aspects
      const root = document.documentElement;

      // Apply color theme
      root.style.setProperty('--primary', saved.color.base);
      root.style.setProperty('--primary-light', saved.color.light);
      root.style.setProperty('--primary-dark', saved.color.dark);

      // Apply background theme
      root.style.setProperty('--background', saved.background.main);
      root.style.setProperty('--surface', saved.background.surface);
      root.style.setProperty('--surface-dark', saved.background.surfaceDark);
      root.style.setProperty('--surface-light', `${saved.background.surface}33`);

      // Apply font theme
      root.style.setProperty('--text-primary', saved.font.primary);
      root.style.setProperty('--text-secondary', saved.font.secondary);
      root.style.setProperty('--text-muted', saved.font.muted);
      root.style.setProperty('--text-disabled', saved.font.disabled);

      // Calculate and set transparent variants
      const rgbBase = hexToRgb(saved.color.base);
      if (rgbBase) {
        root.style.setProperty(
          '--primary-transparent',
          `rgba(${rgbBase.r}, ${rgbBase.g}, ${rgbBase.b}, 0.5)`
        );
      }
    }
  }, []);

  // Add a close handler that triggers the reload
  const handleClose = () => {
    // No need to reload the page anymore since we're persisting the theme
    onClose();
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-[9000]">
      <div className="fixed inset-0 bg-black/80 theme-modal-backdrop" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4 theme-modal">
        <Dialog.Panel className="w-full max-w-4xl bg-surface rounded-2xl shadow-xl max-h-[90vh] flex flex-col">
          <div className="p-6 flex-shrink-0 border-b border-white/10">
            <div className="flex items-center justify-between">
              <Dialog.Title className="text-2xl font-medium text-text-primary">
                Theme Settings
              </Dialog.Title>
              <button
                onClick={onClose}
                className="p-2 rounded-lg hover:bg-surface-light transition-colors"
              >
                <XMarkIcon className="w-6 h-6 text-text-muted" />
              </button>
            </div>
          </div>

          <div className="p-6 overflow-y-auto">
            {/* Color Scheme Selection */}
            <div className="mb-8">
              <h3 className="text-lg font-medium text-text-primary mb-4">Color Scheme</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {colorThemes.map((theme) => (
                  <button
                    key={theme.id}
                    onClick={() => {
                      applyColorTheme(theme);
                      // When selecting a predefined theme, mark custom theme as inactive
                      setIsCustomThemeActive(false);
                      localStorage.setItem(CUSTOM_THEME_KEY, JSON.stringify({
                        primaryColor: customPrimaryColor,
                        backgroundColor: customBackgroundColor,
                        isActive: false,
                        timestamp: new Date().toISOString()
                      }));
                    }}
                    className={cn(
                      "p-6 rounded-lg transition-all group text-left",
                      "hover:bg-surface-light",
                      selectedColor === theme.colors.base && !isCustomThemeActive
                        ? "bg-surface border-2 border-primary"
                        : "bg-surface border border-border"
                    )}
                  >
                    <h4 className="text-base font-medium text-primary mb-2">{theme.name}</h4>
                    <p className="text-sm text-disabled mb-4">{theme.description}</p>
                    <div className="flex gap-2">
                      <div
                        className="w-8 h-8 rounded-full border-2 border-white/10 group-hover:border-white/20 transition-colors"
                        style={{ backgroundColor: theme.colors.dark }}
                      />
                      <div
                        className="w-8 h-8 rounded-full border-2 border-white/10 group-hover:border-white/20 transition-colors"
                        style={{ backgroundColor: theme.colors.base }}
                      />
                      <div
                        className="w-8 h-8 rounded-full border-2 border-white/10 group-hover:border-white/20 transition-colors"
                        style={{ backgroundColor: theme.colors.light }}
                      />
                    </div>
                  </button>
                ))}
                
                {/* Custom Theme Button */}
                <button
                  onClick={() => setShowCustomTheme(!showCustomTheme)}
                  className={cn(
                    "p-6 rounded-lg transition-all group text-left",
                    "hover:bg-surface-light",
                    isCustomThemeActive
                      ? "bg-surface border-2 border-primary"
                      : "bg-surface border border-border"
                  )}
                >
                  <h4 className="text-base font-medium text-primary mb-2">Custom Theme</h4>
                  <p className="text-sm text-disabled mb-4">Create your own custom color theme</p>
                  <div className="flex gap-2">
                    <div
                      className="w-8 h-8 rounded-full border-2 border-white/10 group-hover:border-white/20 transition-colors"
                      style={{ backgroundColor: customPrimaryColor }}
                    />
                    <div
                      className="w-8 h-8 rounded-full border-2 border-white/10 group-hover:border-white/20 transition-colors"
                      style={{ backgroundColor: customBackgroundColor }}
                    />
                  </div>
                </button>
              </div>
              
              {/* Custom Theme Panel */}
              {showCustomTheme && (
                <div className="mt-4 p-4 rounded-lg bg-surface-dark border border-border">
                  <h4 className="text-base font-medium text-primary mb-4">Create Custom Theme</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-2">
                        Primary Color
                      </label>
                      <div className="flex items-center gap-2">
                        <input
                          type="color"
                          value={customPrimaryColor}
                          onChange={(e) => setCustomPrimaryColor(e.target.value)}
                          className="w-10 h-10 rounded cursor-pointer"
                        />
                        <input
                          type="text"
                          value={customPrimaryColor}
                          onChange={(e) => setCustomPrimaryColor(e.target.value)}
                          className="bg-surface-light text-text-primary px-3 py-2 rounded border border-border"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-2">
                        Background Color
                      </label>
                      <div className="flex items-center gap-2">
                        <input
                          type="color"
                          value={customBackgroundColor}
                          onChange={(e) => setCustomBackgroundColor(e.target.value)}
                          className="w-10 h-10 rounded cursor-pointer"
                        />
                        <input
                          type="text"
                          value={customBackgroundColor}
                          onChange={(e) => setCustomBackgroundColor(e.target.value)}
                          className="bg-surface-light text-text-primary px-3 py-2 rounded border border-border"
                        />
                      </div>
                    </div>
                  </div>
                  
                  {/* Display current map theme selection */}
                  <div className="mt-4 p-3 rounded bg-surface border border-border">
                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium text-text-secondary">Current Map Theme</h5>
                        <p className="text-text-primary">{mapThemes[mapTheme].name}</p>
                      </div>
                      <div 
                        className="w-10 h-10 rounded border border-white/10"
                        style={{ backgroundColor: mapThemes[mapTheme].preview }}
                      />
                    </div>
                    <p className="text-xs text-text-muted mt-2">
                      You can change the map theme in the Map Theme section below.
                    </p>
                  </div>
                  
                  <div className="mt-4">
                    <button
                      onClick={applyCustomTheme}
                      className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition-colors"
                    >
                      Apply Custom Theme
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Background Mode Selection */}
            <div className="mb-8">
              <h3 className="text-lg font-medium text-text-primary mb-4">Background</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {backgroundThemes.map((theme) => (
                  <button
                    key={theme.id}
                    onClick={() => applyBackgroundTheme(theme)}
                    className={cn(
                      "p-4 rounded-lg transition-all text-left",
                      "hover:bg-surface-light",
                      selectedColor === theme.colors.main
                        ? "bg-surface border-2 border-primary"
                        : "bg-surface border border-border"
                    )}
                  >
                    <h4 className="text-base font-medium text-primary mb-2">{theme.name}</h4>
                    <div className="flex gap-2">
                      <div
                        className="w-8 h-8 rounded border-2 border-white/10 transition-colors"
                        style={{ backgroundColor: theme.colors.main }}
                      />
                      <div
                        className="w-8 h-8 rounded border-2 border-white/10 transition-colors"
                        style={{ backgroundColor: theme.colors.surface }}
                      />
                      <div
                        className="w-8 h-8 rounded border-2 border-white/10 transition-colors"
                        style={{ backgroundColor: theme.colors.surfaceDark }}
                      />
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Map Theme Selection */}
            <div className="mb-8">
              <h3 className="text-lg font-medium text-text-primary mb-4">Map Theme</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {(Object.entries(mapThemes) as [MapTheme, typeof mapThemes[MapTheme]][]).map(([key, theme]) => (
                  <button
                    key={key}
                    onClick={() => handleMapThemeChange(key)}
                    className={`p-4 rounded-xl border-2 transition-all ${mapTheme === key
                      ? 'border-primary shadow-lg shadow-primary/20'
                      : 'border-surface-dark hover:border-primary/50'
                      }`}
                  >
                    <h4 className="text-text-primary font-medium mb-2">{theme.name}</h4>
                    <p className="text-text-muted text-sm mb-4">{theme.description}</p>
                    <div
                      className="w-full h-24 rounded-lg relative overflow-hidden"
                      style={{ backgroundColor: theme.preview }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/10" />
                      <div className="absolute inset-0" style={{
                        background: 'linear-gradient(45deg, rgba(0,0,0,0.1) 25%, transparent 25%, transparent 50%, rgba(0,0,0,0.1) 50%, rgba(0,0,0,0.1) 75%, transparent 75%, transparent)',
                        backgroundSize: '10px 10px'
                      }} />
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Font Colors Selection */}
            <div>
              <h3 className="text-lg font-medium text-text-primary mb-4">Font Colors</h3>
              <div className="grid grid-cols-2 gap-4">
                {fontThemes.map((theme) => (
                  <button
                    key={theme.id}
                    onClick={() => applyFontTheme(theme)}
                    className={cn(
                      "p-4 rounded-lg transition-all text-left",
                      "hover:bg-surface-light",
                      fontColors.textPrimary === theme.colors.primary
                        ? "bg-surface border-2 border-primary"
                        : "bg-surface border border-border"
                    )}
                  >
                    <h4 className="text-base font-medium text-primary mb-2">{theme.name}</h4>
                    <div className="flex gap-2">
                      <div
                        className="w-8 h-8 rounded border-2 border-white/10 transition-colors"
                        style={{ backgroundColor: theme.colors.primary }}
                      />
                      <div
                        className="w-8 h-8 rounded border-2 border-white/10 transition-colors"
                        style={{ backgroundColor: theme.colors.secondary }}
                      />
                      <div
                        className="w-8 h-8 rounded border-2 border-white/10 transition-colors"
                        style={{ backgroundColor: theme.colors.muted }}
                      />
                      <div
                        className="w-8 h-8 rounded border-2 border-white/10 transition-colors"
                        style={{ backgroundColor: theme.colors.disabled }}
                      />
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}; 