'use client'

import React, { useState, useEffect } from 'react'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { Textarea } from '@/app/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/app/components/ui/dialog'
import { Badge } from '@/app/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Checkbox } from '@/app/components/ui/checkbox'
import { Plus, X, User, Search, UserPlus } from 'lucide-react'
import { toast } from 'sonner'

interface Passenger {
  id: string
  name: string
  email: string
  phone?: string
  company?: string
  group: 'VIP' | 'Staff' | 'Guest'
  dietaryRestrictions?: string
  specialRequirements?: string
}

interface PassengerSelectionProps {
  selectedPassengers: string[]
  onPassengersChange: (passengerIds: string[]) => void
  maxPassengers?: number
}

export function PassengerSelection({ 
  selectedPassengers, 
  onPassengersChange, 
  maxPassengers = 10 
}: PassengerSelectionProps) {
  const [passengers, setPassengers] = useState<Passenger[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [showNewPassengerDialog, setShowNewPassengerDialog] = useState(false)
  const [loading, setLoading] = useState(false)

  // New passenger form state
  const [newPassenger, setNewPassenger] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    group: 'Guest' as 'VIP' | 'Staff' | 'Guest',
    dietaryRestrictions: '',
    specialRequirements: ''
  })

  // Load passengers from API
  useEffect(() => {
    loadPassengers()
  }, [])

  const loadPassengers = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/passengers')
      if (response.ok) {
        const data = await response.json()
        setPassengers(data.data || [])
      }
    } catch (error) {
      console.error('Failed to load passengers:', error)
      toast.error('Failed to load passengers')
    } finally {
      setLoading(false)
    }
  }

  const handleCreatePassenger = async () => {
    if (!newPassenger.name || !newPassenger.email) {
      toast.error('Name and email are required')
      return
    }

    try {
      const response = await fetch('/api/passengers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          first_name: newPassenger.name.split(' ')[0] || '',
          last_name: newPassenger.name.split(' ').slice(1).join(' ') || '',
          email: newPassenger.email,
          phone_number: newPassenger.phone || '',
          passenger_type: newPassenger.group.toLowerCase(),
          company: newPassenger.company,
          dietary_restrictions: newPassenger.dietaryRestrictions,
          special_requirements: newPassenger.specialRequirements,
        }),
      })

      if (response.ok) {
        const data = await response.json()
        const createdPassenger: Passenger = {
          id: data.data.id,
          name: newPassenger.name,
          email: newPassenger.email,
          phone: newPassenger.phone,
          company: newPassenger.company,
          group: newPassenger.group,
          dietaryRestrictions: newPassenger.dietaryRestrictions,
          specialRequirements: newPassenger.specialRequirements,
        }
        
        setPassengers(prev => [...prev, createdPassenger])
        onPassengersChange([...selectedPassengers, createdPassenger.id])
        
        // Reset form
        setNewPassenger({
          name: '',
          email: '',
          phone: '',
          company: '',
          group: 'Guest',
          dietaryRestrictions: '',
          specialRequirements: ''
        })
        
        setShowNewPassengerDialog(false)
        toast.success('Passenger created successfully')
      } else {
        toast.error('Failed to create passenger')
      }
    } catch (error) {
      console.error('Failed to create passenger:', error)
      toast.error('Failed to create passenger')
    }
  }

  const togglePassengerSelection = (passengerId: string) => {
    if (selectedPassengers.includes(passengerId)) {
      onPassengersChange(selectedPassengers.filter(id => id !== passengerId))
    } else if (selectedPassengers.length < maxPassengers) {
      onPassengersChange([...selectedPassengers, passengerId])
    } else {
      toast.error(`Maximum ${maxPassengers} passengers allowed`)
    }
  }

  const filteredPassengers = passengers.filter(passenger =>
    passenger.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    passenger.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    passenger.company?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const selectedPassengerDetails = passengers.filter(p => selectedPassengers.includes(p.id))

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Select Passengers</h3>
          <p className="text-sm text-gray-600">
            Choose passengers for this trip ({selectedPassengers.length}/{maxPassengers} selected)
          </p>
        </div>
        
        <Dialog open={showNewPassengerDialog} onOpenChange={setShowNewPassengerDialog}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" className="text-blue-600 border-blue-200 hover:bg-blue-50">
              <UserPlus className="h-4 w-4 mr-2" />
              Add New Passenger
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Create New Passenger</DialogTitle>
              <DialogDescription>
                Add a new passenger to your account and select them for this trip.
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Full Name *</Label>
                  <Input
                    id="name"
                    value={newPassenger.name}
                    onChange={(e) => setNewPassenger(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter full name"
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={newPassenger.email}
                    onChange={(e) => setNewPassenger(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="Enter email"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={newPassenger.phone}
                    onChange={(e) => setNewPassenger(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="Enter phone number"
                  />
                </div>
                <div>
                  <Label htmlFor="company">Company</Label>
                  <Input
                    id="company"
                    value={newPassenger.company}
                    onChange={(e) => setNewPassenger(prev => ({ ...prev, company: e.target.value }))}
                    placeholder="Enter company"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="group">Passenger Group</Label>
                <Select 
                  value={newPassenger.group} 
                  onValueChange={(value: 'VIP' | 'Staff' | 'Guest') => 
                    setNewPassenger(prev => ({ ...prev, group: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Guest">Guest</SelectItem>
                    <SelectItem value="VIP">VIP</SelectItem>
                    <SelectItem value="Staff">Staff</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="dietary">Dietary Restrictions</Label>
                <Textarea
                  id="dietary"
                  value={newPassenger.dietaryRestrictions}
                  onChange={(e) => setNewPassenger(prev => ({ ...prev, dietaryRestrictions: e.target.value }))}
                  placeholder="Any dietary restrictions or allergies"
                  rows={2}
                />
              </div>
              
              <div>
                <Label htmlFor="special">Special Requirements</Label>
                <Textarea
                  id="special"
                  value={newPassenger.specialRequirements}
                  onChange={(e) => setNewPassenger(prev => ({ ...prev, specialRequirements: e.target.value }))}
                  placeholder="Any special requirements or accessibility needs"
                  rows={2}
                />
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowNewPassengerDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreatePassenger}>
                Create & Select Passenger
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Search passengers by name, email, or company..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Selected Passengers Summary */}
      {selectedPassengerDetails.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Selected Passengers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {selectedPassengerDetails.map((passenger) => (
                <Badge
                  key={passenger.id}
                  variant="secondary"
                  className="flex items-center gap-1"
                >
                  <User className="h-3 w-3" />
                  {passenger.name}
                  <button
                    onClick={() => togglePassengerSelection(passenger.id)}
                    className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Passenger List */}
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {loading ? (
          <div className="text-center py-8 text-gray-500">Loading passengers...</div>
        ) : filteredPassengers.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {searchTerm ? 'No passengers found matching your search.' : 'No passengers found.'}
          </div>
        ) : (
          filteredPassengers.map((passenger) => (
            <Card
              key={passenger.id}
              className={`cursor-pointer transition-colors ${
                selectedPassengers.includes(passenger.id)
                  ? 'ring-2 ring-blue-500 bg-blue-50'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => togglePassengerSelection(passenger.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      checked={selectedPassengers.includes(passenger.id)}
                      onChange={() => {}} // Handled by card click
                    />
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{passenger.name}</span>
                        <Badge variant={passenger.group === 'VIP' ? 'default' : 'secondary'} className="text-xs">
                          {passenger.group}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600">
                        {passenger.email}
                        {passenger.phone && ` • ${passenger.phone}`}
                        {passenger.company && ` • ${passenger.company}`}
                      </div>
                      {(passenger.dietaryRestrictions || passenger.specialRequirements) && (
                        <div className="text-xs text-gray-500 mt-1">
                          {passenger.dietaryRestrictions && `Dietary: ${passenger.dietaryRestrictions}`}
                          {passenger.dietaryRestrictions && passenger.specialRequirements && ' • '}
                          {passenger.specialRequirements && `Special: ${passenger.specialRequirements}`}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
