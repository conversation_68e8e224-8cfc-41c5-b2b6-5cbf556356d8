import React from 'react';

// Remove or comment out the Salesmate test function
// const testSalesmate = async () => { ... };

export const PaymentStep: React.FC = () => {
  return (
    <div className="space-y-8">
      <h2 className="text-2xl font-semibold text-primary">Payment Details</h2>

      {/* Payment form will be added here */}
      <div className="bg-surface rounded-lg p-6">
        <p className="text-disabled">Payment form coming soon...</p>
      </div>

      {/* Remove the Test Salesmate button */}
      {/* <button
        onClick={testSalesmate}
        className="px-4 py-2 bg-primary text-white rounded-lg"
      >
        Test Salesmate
      </button> */}

      {/* Hide the policy section */}
      {/* <div className="space-y-8">
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-primary">Cancellation Policy</h3>
          <div className="space-y-2 text-sm text-secondary">
            <p>100% refund if cancelled 72 hours before</p>
            <p>50% refund if cancelled 48 hours before</p>
            <p>Full charge if cancelled within 24 hours</p>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium text-primary">Chauffeur Details</h3>
          <div className="space-y-2 text-sm text-secondary">
            <p>Your Chauffeur</p>
            <p>Details will be provided 24 hours before pickup</p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="space-y-2 text-sm text-secondary">
            <p>Insured Service</p>
            <p>Full coverage for your journey</p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="space-y-2 text-sm text-secondary">
            <p>Secure Payment</p>
            <p>Your payment information is safe</p>
          </div>
        </div>
      </div> */}
    </div>
  );
}; 