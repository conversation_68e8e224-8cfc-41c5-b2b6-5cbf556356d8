import { useBookingStore } from '../../store/bookingStore';
import { useThemeStore } from '../../store/themeStore';
import { cn } from '../../lib/utils';
import { CheckIcon } from '@heroicons/react/24/solid';
import { useEffect } from 'react';
import { bookingDebugger } from '../../../utils/debugLogger';

const steps = [
  { id: 1, label: 'Trip Details' },
  { id: 2, label: 'Select Vehicle' },
  { id: 3, label: 'Request Quote' }
];

// Helper function to safely get CSS variable value
const getCSSVariable = (variableName: string): string => {
  const root = document.documentElement;
  const style = getComputedStyle(root);
  return style.getPropertyValue(variableName).trim();
};

export const ProgressSteps = () => {
  const { currentStep } = useBookingStore();
  const themeStore = useThemeStore();

  // Add logging for theme and styles
  useEffect(() => {
    bookingDebugger.log('debug', 'Theme', `Current step: ${currentStep}`, {
      windowInnerWidth: window.innerWidth,
      windowInnerHeight: window.innerHeight,
      viewportWidth: document.documentElement.clientWidth,
      viewportHeight: document.documentElement.clientHeight,
      devicePixelRatio: window.devicePixelRatio,
      userAgent: navigator.userAgent,
      cssVariables: {
        primary: getCSSVariable('--primary'),
        background: getCSSVariable('--background'),
        textPrimary: getCSSVariable('--text-primary')
      },
      steps: [1, 2, 3].map(step => ({
        step,
        isActive: step === currentStep,
        isComplete: step < currentStep
      }))
    });
  }, [currentStep]);

  // Get theme config from WordPress global
  useEffect(() => {
    // Only initialize if in WordPress context and not already initialized
    if (typeof window !== 'undefined' && 
        window.limoBookingConfig?.isWordPress && 
        window.limoBookingConfig?.theme) {
      try {
        // For WordPress, use the initializeFromWordPress method with a safe copy
        const safeTheme = { ...window.limoBookingConfig.theme };
        console.log('Initializing theme from WordPress in ProgressSteps with safe theme object:', safeTheme);
        themeStore.initializeFromWordPress(safeTheme);
      } catch (error) {
        console.error('Error initializing theme from WordPress in ProgressSteps:', error);
      }
    }
  }, []);  // Remove themeStore from dependencies to avoid re-initialization

  return (
    <div className="mt-8">
      {/* Progress Line */}
      <div className="relative">
        <div className="progress-line absolute left-0 right-0 top-1/2 -translate-y-1/2 h-0.5">
          <div
            className="progress-line-active h-full transition-all duration-300"
            style={{
              width: `${((currentStep - 1) / (steps.length - 1)) * 100}%`
            }}
          />
        </div>

        <div className="relative flex justify-between">
          {steps.map((step) => {
            const isCompleted = currentStep > step.id;
            const isCurrent = currentStep === step.id;
            const isUpcoming = currentStep < step.id;

            // Log step state and classes
            console.log(`Step ${step.id} State:`, {
              stepState: isCompleted ? 'completed' : isCurrent ? 'current' : 'upcoming',
              currentStep,
              stepId: step.id,
              label: step.label
            });

            return (
              <div key={step.id} className="flex flex-col items-center">
                <div
                  className={cn(
                    "w-10 h-10 rounded-full flex items-center justify-center border-2 transition-all duration-300 ease-in-out shadow-lg",
                    isCompleted ? "bg-primary border-primary" : isCurrent ? "bg-surface border-primary" : "bg-surface border-primary/50"
                  )}
                >
                  {isCompleted ? (
                    <CheckIcon className="w-5 h-5 text-primary" />
                  ) : (
                    <span className="text-primary">{step.id}</span>
                  )}
                </div>
                <span className="mt-2 text-sm font-medium text-primary">
                  {step.label}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
