'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/app/components/ui/dialog'
import { Badge } from '@/app/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card'
import { RadioGroup, RadioGroupItem } from '@/app/components/ui/radio-group'
import { Calendar, MapPin, Users, Plus, Search, CalendarPlus } from 'lucide-react'
import { toast } from 'sonner'

interface Event {
  id: string
  name: string
  type: string
  location: string
  date: string
  expectedPassengers: number
  status: 'draft' | 'confirmed' | 'completed' | 'cancelled'
  description?: string
}

interface EventSelectionProps {
  selectedEventId: string | null
  onEventChange: (eventId: string | null, eventData?: Event) => void
  isStandalone: boolean
  onStandaloneChange: (standalone: boolean) => void
}

export function EventSelection({ 
  selectedEventId, 
  onEventChange, 
  isStandalone, 
  onStandaloneChange 
}: EventSelectionProps) {
  const [events, setEvents] = useState<Event[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [showNewEventDialog, setShowNewEventDialog] = useState(false)
  const [loading, setLoading] = useState(false)

  // New event form state
  const [newEvent, setNewEvent] = useState({
    name: '',
    type: '',
    location: '',
    expectedPassengers: 1,
    description: ''
  })

  // Load events from API
  useEffect(() => {
    if (!isStandalone) {
      loadEvents()
    }
  }, [isStandalone])

  const loadEvents = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/events')
      if (response.ok) {
        const data = await response.json()
        setEvents(data.data || [])
      }
    } catch (error) {
      console.error('Failed to load events:', error)
      toast.error('Failed to load events')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateEvent = async () => {
    if (!newEvent.name || !newEvent.type || !newEvent.location) {
      toast.error('Name, type, and location are required')
      return
    }

    try {
      const response = await fetch('/api/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newEvent.name,
          type: newEvent.type,
          location: newEvent.location,
          expected_passengers: newEvent.expectedPassengers,
          description: newEvent.description,
          status: 'draft'
        }),
      })

      if (response.ok) {
        const data = await response.json()
        const createdEvent: Event = {
          id: data.data.id,
          name: newEvent.name,
          type: newEvent.type,
          location: newEvent.location,
          date: new Date().toISOString(),
          expectedPassengers: newEvent.expectedPassengers,
          status: 'draft',
          description: newEvent.description,
        }
        
        setEvents(prev => [...prev, createdEvent])
        onEventChange(createdEvent.id, createdEvent)
        
        // Reset form
        setNewEvent({
          name: '',
          type: '',
          location: '',
          expectedPassengers: 1,
          description: ''
        })
        
        setShowNewEventDialog(false)
        toast.success('Event created successfully')
      } else {
        toast.error('Failed to create event')
      }
    } catch (error) {
      console.error('Failed to create event:', error)
      toast.error('Failed to create event')
    }
  }

  const handleEventSelection = (eventId: string) => {
    const event = events.find(e => e.id === eventId)
    onEventChange(eventId, event)
  }

  const filteredEvents = events.filter(event =>
    event.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.location.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const selectedEvent = events.find(e => e.id === selectedEventId)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900">Quote Type</h3>
        <p className="text-sm text-gray-600">
          Choose whether this quote is for an existing event or a standalone booking.
        </p>
      </div>

      {/* Quote Type Selection */}
      <RadioGroup
        value={isStandalone ? 'standalone' : 'event'}
        onValueChange={(value) => {
          const standalone = value === 'standalone'
          onStandaloneChange(standalone)
          if (standalone) {
            onEventChange(null)
          }
        }}
        className="grid grid-cols-2 gap-4"
      >
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="event" id="event" />
          <Label htmlFor="event" className="flex items-center gap-2 cursor-pointer">
            <Calendar className="h-4 w-4" />
            Link to Event
          </Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="standalone" id="standalone" />
          <Label htmlFor="standalone" className="flex items-center gap-2 cursor-pointer">
            <MapPin className="h-4 w-4" />
            Standalone Quote
          </Label>
        </div>
      </RadioGroup>

      {/* Event Selection (only shown when not standalone) */}
      {!isStandalone && (
        <div className="space-y-4">
          {/* Header with Create Button */}
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-md font-medium text-gray-900">Select Event</h4>
              <p className="text-sm text-gray-600">
                Choose an existing event or create a new one.
              </p>
            </div>
            
            <Dialog open={showNewEventDialog} onOpenChange={setShowNewEventDialog}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="text-blue-600 border-blue-200 hover:bg-blue-50">
                  <CalendarPlus className="h-4 w-4 mr-2" />
                  Create New Event
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Create New Event</DialogTitle>
                  <DialogDescription>
                    Create a new event and link this quote to it.
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="eventName">Event Name *</Label>
                    <Input
                      id="eventName"
                      value={newEvent.name}
                      onChange={(e) => setNewEvent(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Enter event name"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="eventType">Event Type *</Label>
                      <Select 
                        value={newEvent.type} 
                        onValueChange={(value) => setNewEvent(prev => ({ ...prev, type: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="corporate">Corporate</SelectItem>
                          <SelectItem value="wedding">Wedding</SelectItem>
                          <SelectItem value="conference">Conference</SelectItem>
                          <SelectItem value="airport">Airport Transfer</SelectItem>
                          <SelectItem value="tour">Tour</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="expectedPassengers">Expected Passengers</Label>
                      <Input
                        id="expectedPassengers"
                        type="number"
                        min="1"
                        value={newEvent.expectedPassengers}
                        onChange={(e) => setNewEvent(prev => ({ ...prev, expectedPassengers: parseInt(e.target.value) || 1 }))}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="eventLocation">Location *</Label>
                    <Input
                      id="eventLocation"
                      value={newEvent.location}
                      onChange={(e) => setNewEvent(prev => ({ ...prev, location: e.target.value }))}
                      placeholder="Enter event location"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="eventDescription">Description</Label>
                    <Input
                      id="eventDescription"
                      value={newEvent.description}
                      onChange={(e) => setNewEvent(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Brief description of the event"
                    />
                  </div>
                </div>
                
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowNewEventDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateEvent}>
                    Create & Select Event
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search events by name, type, or location..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Selected Event Summary */}
          {selectedEvent && (
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="text-sm font-medium text-blue-900">Selected Event</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-blue-900">{selectedEvent.name}</span>
                    <Badge variant="secondary">{selectedEvent.type}</Badge>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-blue-700">
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {selectedEvent.location}
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {selectedEvent.expectedPassengers} passengers
                    </div>
                  </div>
                  {selectedEvent.description && (
                    <p className="text-sm text-blue-600">{selectedEvent.description}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Event List */}
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {loading ? (
              <div className="text-center py-8 text-gray-500">Loading events...</div>
            ) : filteredEvents.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                {searchTerm ? 'No events found matching your search.' : 'No events found.'}
              </div>
            ) : (
              filteredEvents.map((event) => (
                <Card
                  key={event.id}
                  className={`cursor-pointer transition-colors ${
                    selectedEventId === event.id
                      ? 'ring-2 ring-blue-500 bg-blue-50'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => handleEventSelection(event.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{event.name}</span>
                          <Badge variant="secondary" className="text-xs">
                            {event.type}
                          </Badge>
                          <Badge 
                            variant={event.status === 'confirmed' ? 'default' : 'outline'} 
                            className="text-xs"
                          >
                            {event.status}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {event.location}
                          </div>
                          <div className="flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            {event.expectedPassengers} passengers
                          </div>
                        </div>
                        {event.description && (
                          <p className="text-sm text-gray-500 mt-1">{event.description}</p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </div>
      )}

      {/* Standalone Quote Info */}
      {isStandalone && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-green-600" />
              <span className="text-green-900 font-medium">Standalone Quote</span>
            </div>
            <p className="text-sm text-green-700 mt-1">
              This quote will be created independently without linking to any event.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
