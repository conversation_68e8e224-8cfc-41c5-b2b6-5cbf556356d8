import { useState, useEffect, useRef } from 'react';
import { Switch } from '@headlessui/react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import {
  PlusIcon,
  MinusIcon,
  XMarkIcon,
  ChevronRightIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  UserGroupIcon,
  UserIcon,
  CalendarIcon,
  ClockIcon,
  Bars3Icon
} from '@heroicons/react/24/outline';
import { useBookingStore } from '../../../store/bookingStore';
import { LocationAutocomplete } from './shared/LocationAutocomplete';
import { DayData, Location } from '../../../types/booking';
import { cn, scrollFormToTop } from '../../../lib/utils';
import { DateTimePicker } from './shared/DateTimePicker';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { bookingDebugger } from '../../utils/debugLogger';
import { ToggleButton } from './shared/ToggleButton';
import { cityConfigs } from '../../config/cityConfig';
import { MapComponent } from '../MapComponent';

// Define location type
type RouteLocation = {
  type: 'pickup' | 'stop' | 'dropoff';
  address: string;
  coordinates: [number, number];
  stopIndex?: number;
};

// Add custom marker styles
const markerStyles = `
  .marker {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    font-weight: 600;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s;
  }
  .marker.pickup {
    background-color: var(--success);
  }
  .marker.dropoff {
    background-color: var(--error);
  }
  .marker.stop {
    background-color: var(--primary);
  }
  .marker:hover {
    transform: scale(1.1);
  }
  .mapboxgl-popup {
    max-width: 300px !important;
  }
  .mapboxgl-popup-content {
    background: var(--surface-dark) !important;
    border: 1px solid var(--border-color);
    padding: 12px !important;
  }
  .mapboxgl-popup-tip {
    border-top-color: var(--surface-dark) !important;
    border-bottom-color: var(--surface-dark) !important;
  }
`;

// Add the styles to the document
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.innerHTML = markerStyles;
  document.head.appendChild(style);
}

// Helper function to get route between two points
async function getRoute(start: [number, number], end: [number, number]) {
  const response = await fetch(
    `https://api.mapbox.com/directions/v5/mapbox/driving/${start[0]},${start[1]};${end[0]},${end[1]}?geometries=geojson&access_token=${mapboxgl.accessToken}`
  );
  const data = await response.json();
  return data.routes[0].geometry;
}

// Add drag handle component
const DragHandle = ({ className }: { className?: string }) => (
  <div className={cn("cursor-move touch-none", className)}>
    <Bars3Icon className="w-5 h-5" />
  </div>
);

const TripDetails = ({
  day,
  dayIndex,
  startDate,
  isCollapsed
}: {
  day: DayData;
  dayIndex: number;
  startDate: string | null;
  isCollapsed: boolean;
}) => {
  if (!day.pickupLocation || !day.dropoffLocation) return null;

  // Calculate the date for this day based on start date
  const getDayDate = () => {
    if (!startDate) return 'Date not set';
    const date = new Date(startDate);
    date.setDate(date.getDate() + dayIndex);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className={cn(
      "overflow-hidden transition-all duration-300",
      isCollapsed ? "h-0" : "h-auto"
    )}>
      {/* Single row trip summary */}
      <div className="flex items-center gap-4 py-3">
        {/* Date */}
        <div className="flex items-center gap-2 text-primary min-w-[120px]">
          <CalendarIcon className="w-4 h-4 text-primary" />
          <span className="text-sm">{getDayDate()}</span>
        </div>

        {/* Divider */}
        <div className="h-6 w-px bg-border"></div>

        {/* Route Summary */}
        <div className="flex items-center gap-2 flex-grow">
          {/* Pickup */}
          <div className="flex items-center gap-2 min-w-0">
            <div className="w-6 h-6 rounded-full bg-success flex items-center justify-center text-primary text-sm flex-shrink-0">
              A
            </div>
            <div className="truncate text-sm text-primary">{day.pickupLocation.address}</div>
          </div>

          {/* Stops indicator */}
          {day.stops.length > 0 && (
            <>
              <div className="h-px w-4 bg-primary"></div>
              <div className="w-6 h-6 rounded-full bg-primary flex items-center justify-center text-primary text-sm flex-shrink-0">
                {day.stops.length}
              </div>
              <div className="h-px w-4 bg-primary"></div>
            </>
          )}

          {/* Dropoff */}
          <div className="flex items-center gap-2 min-w-0">
            <div className="w-6 h-6 rounded-full bg-error flex items-center justify-center text-primary text-sm flex-shrink-0">
              B
            </div>
            <div className="truncate text-sm text-primary">{day.dropoffLocation.address}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

const DaySection = ({
  day,
  index,
  isCollapsed,
  onToggleCollapse,
  onUpdateDay,
  onRemoveDay,
  startDate,
  isActive,
  dragHandleProps
}: {
  day: DayData;
  index: number;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  onUpdateDay: (data: Partial<DayData>) => void;
  onRemoveDay: () => void;
  startDate: string | null;
  isActive: boolean;
  dragHandleProps: any;
}) => {
  return (
    <div className={cn(
      "space-y-6 rounded-lg p-6 transition-colors",
      isActive ? "bg-surface-light" : "bg-surface"
    )}>
      <div className="flex items-center gap-4">
        {/* Drag Handle */}
        <div {...dragHandleProps}>
          <DragHandle className="text-disabled hover:text-primary transition-colors cursor-move" />
        </div>

        {/* Day Title and Toggle */}
        <div className="flex items-center gap-3">
          <h3 className="text-primary text-lg font-medium">Day {index + 1}</h3>
          <button
            onClick={onToggleCollapse}
            className="text-disabled hover:text-primary transition-colors"
          >
            <ChevronRightIcon className={`w-5 h-5 transition-transform ${isCollapsed ? '' : 'rotate-90'}`} />
          </button>
        </div>

        {/* Trip Details when collapsed */}
        {isCollapsed && day.pickupLocation && day.dropoffLocation && (
          <div className="flex items-center gap-4 ml-4 flex-grow">
            {/* Route Summary */}
            <div className="flex items-center gap-2 flex-grow">
              {/* Pickup */}
              <div className="flex items-center gap-2 min-w-0">
                <div className="w-6 h-6 rounded-full bg-success flex items-center justify-center text-primary text-sm flex-shrink-0">
                  A
                </div>
                <div className="truncate text-sm text-primary">{day.pickupLocation.address}</div>
              </div>

              {/* Stops indicator */}
              {day.stops.length > 0 && (
                <>
                  <div className="h-px w-4 bg-primary"></div>
                  <div className="w-6 h-6 rounded-full bg-primary flex items-center justify-center text-primary text-sm flex-shrink-0">
                    {day.stops.length}
                  </div>
                  <div className="h-px w-4 bg-primary"></div>
                </>
              )}

              {/* Dropoff */}
              <div className="flex items-center gap-2 min-w-0">
                <div className="w-6 h-6 rounded-full bg-error flex items-center justify-center text-primary text-sm flex-shrink-0">
                  B
                </div>
                <div className="truncate text-sm text-primary">{day.dropoffLocation.address}</div>
              </div>
            </div>
          </div>
        )}

        {/* Remove Button - Only for non-first day */}
        {index > 0 && (
          <button
            onClick={onRemoveDay}
            className="ml-auto text-disabled hover:text-error transition-colors"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>
        )}
      </div>

      {!isCollapsed && (
        <>
          {/* Pickup Location */}
          <div className="relative z-[90]">
            <h3 className="text-primary text-base mb-2">Pickup Location:</h3>
            <LocationAutocomplete
              value={day.pickupLocation?.address || ''}
              onChange={(location: Location) => onUpdateDay({ pickupLocation: location })}
              placeholder="Enter pickup address"
              className="w-full"
              cityConfig={cityConfigs.worldwide}
            />
          </div>

          {/* Stops Section */}
          <div className="relative z-[80] bg-surface rounded-lg p-6 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-primary text-base">Stops Along the Way:</h3>
              <button
                onClick={() => onUpdateDay({
                  stops: [...(day.stops || []), {
                    id: Math.random().toString(36).substr(2, 9),
                    location: null
                  }]
                })}
                className="flex items-center space-x-2 text-primary hover:text-primary/80 transition-colors"
              >
                <PlusIcon className="w-5 h-5" />
                <span>Add Stop</span>
              </button>
            </div>

            <div className="space-y-3">
              {day.stops?.map((stop, stopIndex) => (
                <div
                  key={stop.id}
                  className="flex items-center space-x-3 bg-surface p-3 rounded-lg"
                >
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary flex items-center justify-center text-primary font-medium">
                    {stopIndex + 1}
                  </div>
                  <div className="flex-grow">
                    <LocationAutocomplete
                      value={stop.location?.address || ''}
                      onChange={(location: Location) => {
                        const newStops = [...day.stops];
                        newStops[stopIndex] = { ...stop, location };
                        onUpdateDay({ stops: newStops });
                      }}
                      placeholder={`Stop ${stopIndex + 1}`}
                      className="w-full"
                      cityConfig={cityConfigs.worldwide}
                    />
                  </div>
                  <button
                    onClick={() => onUpdateDay({
                      stops: day.stops.filter(s => s.id !== stop.id)
                    })}
                    className="text-disabled hover:text-error transition-colors"
                  >
                    <XMarkIcon className="w-5 h-5" />
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Drop-off Location */}
          <div className="relative z-[70]">
            <h3 className="text-primary text-base mb-2">Drop-off Location:</h3>
            <LocationAutocomplete
              value={day.dropoffLocation?.address || ''}
              onChange={(location: Location) => {
                onUpdateDay({
                  dropoffLocation: location,
                  stops: day.stops || []
                });
              }}
              placeholder="Enter drop-off address"
              className="w-full"
              cityConfig={cityConfigs.worldwide}
            />
          </div>
        </>
      )}
    </div>
  );
};

// Update the map instance type
type MapInstance = mapboxgl.Map;

export const MultiDayForm = () => {
  const {
    multiDayData,
    updateMultiDayData,
    currentStep,
    setStep
  } = useBookingStore();
  const [daysInput, setDaysInput] = useState(multiDayData.totalDays.toString());
  const [visibleDays, setVisibleDays] = useState<Set<string>>(new Set([multiDayData.days[0]?.id]));
  const [pickupDate, setPickupDate] = useState(new Date());
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapRef = useRef<MapInstance | null>(null);
  const markersRef = useRef<mapboxgl.Marker[]>([]);
  const routeLayersRef = useRef<{ [key: string]: string[] }>({});
  const [activeDay, setActiveDay] = useState<string | null>(multiDayData.days[0]?.id || null);
  const [totalDistance, setTotalDistance] = useState<{ [key: string]: number }>({});
  const [totalDuration, setTotalDuration] = useState<{ [key: string]: number }>({});
  const formRef = useRef<HTMLDivElement>(null);

  // Update map markers and routes
  useEffect(() => {
    if (!mapContainerRef.current) return;

    try {
      // Initialize map
      const map = new mapboxgl.Map({
        container: mapContainerRef.current,
        style: 'mapbox://styles/mapbox/dark-v11',
        center: [-95.7129, 37.0902],
        zoom: 4
      });

      mapRef.current = map;

      // Remove existing layers if they exist
      const layerId = 'route-layer';
      if (mapRef.current?.getLayer(layerId)) {
        mapRef.current.removeLayer(layerId);
      }
      if (mapRef.current?.getSource(layerId)) {
        mapRef.current.removeSource(layerId);
      }

      bookingDebugger.log('debug', 'Map', 'Updating map markers and routes', {
        visibleDays: Array.from(visibleDays),
        activeDay,
        totalDays: multiDayData.days.length
      });

      // Clear existing markers and routes
      markersRef.current.forEach(marker => marker.remove());
      markersRef.current = [];

      // Clear all existing route layers
      Object.entries(routeLayersRef.current || {}).forEach(([dayId, layers]) => {
        layers.forEach(layerId => {
          const sourceId = layerId.replace('layer', 'source');
          if (mapRef.current?.getLayer(layerId)) {
            mapRef.current.removeLayer(layerId);
            mapRef.current.removeSource(sourceId);
          }
        });
      });
      routeLayersRef.current = {};

      const bounds = new mapboxgl.LngLatBounds();
      let hasValidLocations = false;

      // Process each visible day
      multiDayData.days.forEach(async (day, dayIndex) => {
        bookingDebugger.log('debug', 'Map', `Processing day ${dayIndex + 1}`, {
          dayId: day.id,
          hasPickup: !!day.pickupLocation,
          numStops: day.stops?.length || 0,
          hasDropoff: !!day.dropoffLocation
        });

        // Create an array to store all locations in sequence
        const allLocations: RouteLocation[] = [];
        let hasValidRoute = false;

        // Add pickup location if valid
        if (day.pickupLocation && Array.isArray(day.pickupLocation.coordinates) && day.pickupLocation.coordinates.length === 2) {
          allLocations.push({
            type: 'pickup',
            address: day.pickupLocation.address,
            coordinates: day.pickupLocation.coordinates as [number, number]
          });
          hasValidRoute = true;
          bookingDebugger.log('debug', 'Map', `Added pickup location to route`, {
            dayId: day.id,
            address: day.pickupLocation.address,
            coordinates: day.pickupLocation.coordinates
          });
        }

        // Add stops if valid
        if (day.stops) {
          day.stops.forEach((stop, index) => {
            if (stop.location && Array.isArray(stop.location.coordinates) && stop.location.coordinates.length === 2) {
              allLocations.push({
                type: 'stop',
                stopIndex: index,
                address: stop.location.address,
                coordinates: stop.location.coordinates as [number, number]
              });
              hasValidRoute = true;
              bookingDebugger.log('debug', 'Map', `Added stop ${index + 1} to route`, {
                dayId: day.id,
                stopIndex: index,
                address: stop.location.address,
                coordinates: stop.location.coordinates
              });
            }
          });
        }

        // Add dropoff location if valid
        if (day.dropoffLocation && Array.isArray(day.dropoffLocation.coordinates) && day.dropoffLocation.coordinates.length === 2) {
          allLocations.push({
            type: 'dropoff',
            address: day.dropoffLocation.address,
            coordinates: day.dropoffLocation.coordinates as [number, number]
          });
          hasValidRoute = true;
          bookingDebugger.log('debug', 'Map', `Added dropoff location to route`, {
            dayId: day.id,
            address: day.dropoffLocation.address,
            coordinates: day.dropoffLocation.coordinates
          });
        }

        bookingDebugger.log('debug', 'Map', `Route locations summary for day ${dayIndex + 1}`, {
          dayId: day.id,
          totalLocations: allLocations.length,
          locationTypes: allLocations.map(loc => loc.type),
          hasValidRoute,
          routeSequence: allLocations.map((loc, idx) => ({
            position: idx + 1,
            type: loc.type,
            address: loc.address
          }))
        });

        if (!hasValidRoute || allLocations.length === 0) {
          bookingDebugger.log('warn', 'Map', `No valid route for day ${dayIndex + 1}`, {
            dayId: day.id,
            hasValidRoute,
            numLocations: allLocations.length
          });
          return;
        }

        // Draw route segments between consecutive points
        if (allLocations.length >= 2) {
          let totalDist = 0;
          let totalDur = 0;

          // Initialize route layers for this day
          if (!routeLayersRef.current[day.id]) {
            routeLayersRef.current[day.id] = [];
          }

          // Draw all segments
          for (let i = 0; i < allLocations.length - 1; i++) {
            const start = allLocations[i];
            const end = allLocations[i + 1];
            const segmentId = `${day.id}-segment-${i}`;
            const sourceId = `route-${segmentId}`;
            const layerId = `route-layer-${segmentId}`;

            try {
              const response = await fetch(
                `https://api.mapbox.com/directions/v5/mapbox/driving/${start.coordinates.join(',')};${end.coordinates.join(',')}?geometries=geojson&overview=full&access_token=${mapboxgl.accessToken}`
              );
              const data = await response.json();

              if (data.routes?.[0]?.geometry) {
                // Add source
                mapRef.current?.addSource(sourceId, {
                  type: 'geojson',
                  data: {
                    type: 'Feature',
                    properties: {},
                    geometry: data.routes[0].geometry
                  }
                });

                // Add layer
                mapRef.current?.addLayer({
                  id: layerId,
                  type: 'line',
                  source: sourceId,
                  layout: {
                    'line-join': 'round',
                    'line-cap': 'round',
                    'visibility': 'visible'
                  },
                  paint: {
                    'line-color': day.id === activeDay ? 'var(--primary)' : 'var(--primary-transparent)',
                    'line-width': day.id === activeDay ? 4 : 2,
                    'line-opacity': day.id === activeDay ? 0.8 : 0.4
                  }
                });

                routeLayersRef.current[day.id].push(layerId);
                totalDist += data.routes[0].distance || 0;
                totalDur += data.routes[0].duration || 0;

                bookingDebugger.log('debug', 'Map', `Route segment ${i + 1} completed`, {
                  dayId: day.id,
                  segmentId,
                  segmentDistance: data.routes[0].distance,
                  segmentDuration: data.routes[0].duration,
                  from: { type: start.type, address: start.address },
                  to: { type: end.type, address: end.address },
                  isLastSegment: i === allLocations.length - 2
                });
              }
            } catch (error) {
              bookingDebugger.log('error', 'Map', `Failed to draw route segment ${i + 1}`, {
                dayId: day.id,
                segmentId,
                error: error instanceof Error ? error.message : String(error),
                from: { type: start.type, address: start.address },
                to: { type: end.type, address: end.address }
              });
            }
          }

          if (day.id === activeDay) {
            setTotalDistance(prev => ({ ...prev, [day.id]: totalDist }));
            setTotalDuration(prev => ({ ...prev, [day.id]: totalDur }));

            bookingDebugger.log('info', 'Map', `Completed route drawing for day ${dayIndex + 1}`, {
              dayId: day.id,
              totalDistance: totalDist,
              totalDuration: totalDur,
              numSegmentsDrawn: allLocations.length - 1,
              routeLayers: routeLayersRef.current[day.id]
            });
          }
        }
      });

      if (hasValidLocations) {
        mapRef.current?.fitBounds(bounds, {
          padding: { top: 50, bottom: 50, left: 50, right: 50 },
          maxZoom: 15
        });
        bookingDebugger.log('debug', 'Map', 'Map bounds updated');
      }
    } catch (error) {
      console.error('Error initializing map:', error);
    }
  }, [
    multiDayData.days,
    visibleDays,
    activeDay,
    JSON.stringify(multiDayData.days.map(day => ({
      pickup: day.pickupLocation?.coordinates,
      stops: day.stops?.map(stop => stop.location?.coordinates),
      dropoff: day.dropoffLocation?.coordinates
    })))
  ]);

  const toggleDay = (dayId: string) => {
    setVisibleDays(prev => {
      const newSet = new Set(prev);
      if (newSet.has(dayId)) {
        newSet.delete(dayId);
      } else {
        newSet.add(dayId);
      }
      return newSet;
    });
  };

  const handleDaysChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value, 10);
    if (!isNaN(value) && value > 0) {
      const days = [...multiDayData.days];
      if (value > days.length) {
        // Add more days
        while (days.length < value) {
          days.push({
            id: Math.random().toString(36).substr(2, 9),
            pickupLocation: null,
            dropoffLocation: null,
            pickupDate: null,
            pickupTime: null,
            stops: [],
            isCollapsed: false
          });
        }
      } else {
        // Remove days
        days.splice(value);
      }
      updateMultiDayData({ days });
    }
  };

  const handleDayUpdate = (dayId: string, data: Partial<DayData>) => {
    bookingDebugger.log('info', 'Form', `Updating day ${dayId}`, {
      updatedFields: Object.keys(data),
      data
    });

    const newDays = multiDayData.days.map(day =>
      day.id === dayId ? { ...day, ...data } : day
    );
    updateMultiDayData({ days: newDays });
  };

  // Add day handler with auto-collapse
  const addDay = () => {
    const newDayId = (multiDayData.days.length + 1).toString();
    const newDay = {
      id: newDayId,
      pickupLocation: null,
      dropoffLocation: null,
      pickupDate: null,
      pickupTime: null,
      stops: [],
      isCollapsed: false
    };

    // Collapse all other days
    const updatedDays = multiDayData.days.map(day => ({
      ...day,
      isCollapsed: true
    }));

    updateMultiDayData({
      days: [...updatedDays, newDay],
      totalDays: multiDayData.totalDays + 1
    });
    setDaysInput((multiDayData.totalDays + 1).toString());
    setActiveDay(newDayId);
  };

  // Toggle day collapse handler
  const toggleDayCollapse = (dayId: string) => {
    bookingDebugger.log('info', 'Form', `Toggling day ${dayId} collapse state`);

    const newDays = multiDayData.days.map(day => ({
      ...day,
      isCollapsed: day.id === dayId ? !day.isCollapsed : true
    }));
    updateMultiDayData({ days: newDays });
    setActiveDay(dayId);

    bookingDebugger.log('debug', 'Form', 'Day collapse states updated', {
      newStates: newDays.map(d => ({ id: d.id, isCollapsed: d.isCollapsed }))
    });
  };

  const removeDay = (dayId: string) => {
    const newDays = multiDayData.days.filter(day => day.id !== dayId);
    updateMultiDayData({
      days: newDays,
      totalDays: newDays.length
    });
    setDaysInput(newDays.length.toString());
  };

  const moveDay = (dayId: string, direction: 'up' | 'down') => {
    const currentIndex = multiDayData.days.findIndex(d => d.id === dayId);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= multiDayData.days.length) return;

    const newDays = [...multiDayData.days];
    const [removed] = newDays.splice(currentIndex, 1);
    newDays.splice(newIndex, 0, removed);

    updateMultiDayData({ days: newDays });
  };

  const handleDateTimeChange = (date: Date) => {
    bookingDebugger.log('info', 'Form', 'Start date/time changed', {
      newDate: date.toISOString(),
      formattedTime: date.toLocaleTimeString()
    });

    const timeString = date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit'
    });
    updateMultiDayData({
      startDate: date.toISOString(),
      startTime: timeString
    });
  };

  // Format distance and duration
  const formatDistance = (meters: number) => {
    const miles = meters * 0.000621371;
    return `${miles.toFixed(1)} mi`;
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.round((seconds % 3600) / 60);
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
  };

  // Next Button handler
  const handleNextStep = () => {
    console.log('MultiDayForm - handleNextStep called');
    
    // If form is out of view, scroll it into view
    if (formRef.current) {
      // Get the form's position
      const rect = formRef.current.getBoundingClientRect();
      const offset = 100; // Higher offset for WordPress headers
      
      // Calculate absolute position
      const absolutePosition = window.pageYOffset + rect.top - offset;
      
      console.log('Scrolling to position:', absolutePosition);
      
      // Scroll to position
      window.scrollTo(0, absolutePosition);
    }
    
    // Proceed to next step
    setStep(currentStep + 1);
  };

  return (
    <div ref={formRef} className="space-y-6">
      {/* Trip Details Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Date & Time */}
        <div>
          <h3 className="text-theme-primary text-base mb-2">Start Date & Time:</h3>
          <DateTimePicker
            date={multiDayData.startDate ? new Date(multiDayData.startDate) : pickupDate}
            onChange={(date: Date) => {
              setPickupDate(date);
              handleDateTimeChange(date);
            }}
          />
        </div>

        {/* Adults Counter */}
        <div>
          <h3 className="text-theme-primary text-base mb-2">Adults:</h3>
          <div className="flex items-center bg-surface rounded-lg h-[46px]">
            <button
              onClick={() => {
                const newAdults = Math.max(1, multiDayData.adults - 1);
                updateMultiDayData({ adults: newAdults });
              }}
              disabled={multiDayData.adults === 1}
              className="px-4 py-3 text-theme-muted hover:text-theme-primary transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <MinusIcon className="w-5 h-5" />
            </button>
            <div className="flex-1 text-center">
              <span className="text-theme-primary font-medium">{multiDayData.adults || 1}</span>
            </div>
            <button
              onClick={() => {
                updateMultiDayData({ adults: multiDayData.adults + 1 });
              }}
              className="px-4 py-3 text-theme-muted hover:text-theme-primary transition-colors"
            >
              <PlusIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Children Counter */}
        <div>
          <h3 className="text-theme-primary text-base mb-2">Children:</h3>
          <div className="flex items-center bg-surface rounded-lg h-[46px]">
            <button
              onClick={() => {
                const newChildren = Math.max(0, multiDayData.children - 1);
                updateMultiDayData({ children: newChildren });
              }}
              disabled={multiDayData.children === 0}
              className="px-4 py-3 text-theme-muted hover:text-theme-primary transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <MinusIcon className="w-5 h-5" />
            </button>
            <div className="flex-1 text-center">
              <span className="text-theme-primary font-medium">{multiDayData.children || 0}</span>
            </div>
            <button
              onClick={() => {
                updateMultiDayData({ children: multiDayData.children + 1 });
              }}
              className="px-4 py-3 text-theme-muted hover:text-theme-primary transition-colors"
            >
              <PlusIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Car Seats Toggle */}
        {(multiDayData.children || 0) > 0 && (
          <div>
            <h3 className="text-theme-primary text-base mb-2">Car Seats</h3>
            <div className="h-[46px] flex items-center justify-between bg-surface rounded-lg px-4">
              <span className="text-theme-muted text-sm">Need car seats?</span>
              <ToggleButton
                checked={multiDayData.needCarSeats}
                onChange={(checked) => updateMultiDayData({ needCarSeats: checked })}
              />
            </div>
          </div>
        )}

        {/* Total Trip Info */}
        <div>
          <h3 className="text-primary text-base mb-2">Trip Overview:</h3>
          <div className="bg-surface-dark rounded-lg p-3 space-y-2">
            <div className="text-sm text-disabled">
              Total Days: {multiDayData.totalDays}
            </div>
            {activeDay && (
              <>
                <div className="text-sm text-disabled">
                  Day Distance: {formatDistance(totalDistance[activeDay] || 0)}
                </div>
                <div className="text-sm text-disabled">
                  Est. Duration: {formatDuration(totalDuration[activeDay] || 0)}
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Map Container */}
      <div className="relative">
        <MapComponent />
        <div className="absolute top-4 left-4 flex flex-wrap gap-2 max-w-[calc(100%-2rem)]">
          {multiDayData.days.map((day, index) => {
            // Calculate date for this day
            const dayDate = day.pickupDate ? new Date(day.pickupDate) :
              (multiDayData.startDate ? new Date(multiDayData.startDate) : null);
            if (dayDate && index > 0) {
              dayDate.setDate(dayDate.getDate() + index);
            }

            return (
              <button
                key={day.id}
                className={cn(
                  "px-3 py-2 rounded-lg flex flex-col items-center transition-colors text-sm",
                  day.id === activeDay
                    ? "bg-primary text-primary"
                    : "bg-surface text-disabled hover:bg-surface-light"
                )}
                onClick={() => toggleDay(day.id)}
              >
                <span>Day {index + 1}</span>
                {dayDate && (
                  <span className="text-xs opacity-75">
                    {dayDate.toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric'
                    })}
                  </span>
                )}
              </button>
            );
          })}
        </div>
      </div>

      {/* Service Duration */}
      <div className="flex items-center justify-between">
        <h3 className="text-primary text-base">Number of Days:</h3>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <button
              className="text-disabled hover:text-primary transition-colors disabled:opacity-50"
              disabled={parseInt(daysInput) <= 1}
              onClick={() => {
                const newDays = Math.max(1, parseInt(daysInput) - 1);
                setDaysInput(newDays.toString());
                updateMultiDayData({ totalDays: newDays });
              }}
            >
              <MinusIcon className="w-5 h-5" />
            </button>
            <input
              type="number"
              min="1"
              value={daysInput}
              onChange={handleDaysChange}
              className="w-16 bg-surface-dark border border-border rounded px-2 py-1 text-center text-primary"
            />
            <button
              className="text-disabled hover:text-primary transition-colors"
              onClick={() => {
                const newDays = parseInt(daysInput) + 1;
                setDaysInput(newDays.toString());
                updateMultiDayData({ totalDays: newDays });
              }}
            >
              <PlusIcon className="w-5 h-5" />
            </button>
          </div>
          <span className="text-disabled">days</span>
        </div>
      </div>

      {/* Days Sections */}
      <DragDropContext
        onDragEnd={(result) => {
          if (!result.destination) return;

          const days = Array.from(multiDayData.days);
          const [reorderedDay] = days.splice(result.source.index, 1);
          days.splice(result.destination.index, 0, reorderedDay);

          updateMultiDayData({ days });
        }}
      >
        <Droppable droppableId="days">
          {(provided) => (
            <div
              className="space-y-4"
              {...provided.droppableProps}
              ref={provided.innerRef}
            >
              {multiDayData.days.map((day, index) => (
                <Draggable
                  key={day.id}
                  draggableId={day.id}
                  index={index}
                >
                  {(provided) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                    >
                      <DaySection
                        day={day}
                        index={index}
                        isCollapsed={day.isCollapsed || false}
                        onToggleCollapse={() => toggleDayCollapse(day.id)}
                        onUpdateDay={(data) => handleDayUpdate(day.id, data)}
                        onRemoveDay={() => removeDay(day.id)}
                        startDate={multiDayData.startDate}
                        isActive={day.id === activeDay}
                        dragHandleProps={provided.dragHandleProps}
                      />
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {/* Add Day Button */}
      <button
        className="w-full py-3 rounded-lg border-2 border-dashed border-border text-disabled hover:text-primary hover:border-primary/20 transition-colors"
        onClick={() => {
          const days = [...multiDayData.days];
          days.push({
            id: Math.random().toString(36).substr(2, 9),
            pickupLocation: null,
            dropoffLocation: null,
            pickupDate: null,
            pickupTime: null,
            stops: [],
            isCollapsed: false
          });
          updateMultiDayData({ days });
        }}
      >
        <PlusIcon className="w-5 h-5 mx-auto" />
      </button>

      {/* Next Button */}
      <div className="flex justify-end pt-6">
        <button
          className="px-6 py-3 rounded-lg bg-primary text-primary hover:bg-primary/90 transition-colors"
          onClick={handleNextStep}
        >
          Continue
        </button>
      </div>
    </div>
  );
}; 