import { cn } from '../../../../../lib/utils';
import { Switch } from '../../../../../components/ui/Switch';

interface ToggleButtonProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label?: string;
}

export const ToggleButton = ({ checked, onChange, label }: ToggleButtonProps) => {
  if (label) {
    return (
      <div className="flex items-center justify-between">
        <span className="text-sm text-primary">{label}</span>
        <Switch
          checked={checked}
          onChange={(e) => onChange(e.target.checked)}
        />
      </div>
    );
  }

  // When no label, just return the switch centered
  return (
    <Switch
      checked={checked}
      onChange={(e) => onChange(e.target.checked)}
    />
  );
};
