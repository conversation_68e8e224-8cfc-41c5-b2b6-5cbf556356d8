import { useState, useEffect, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import {
  MapPinIcon,
  ClockIcon,
  XMarkIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  MapIcon
} from '@heroicons/react/24/outline';
import { bookingDebugger } from '../../../../utils/debugLogger';
import { useFloating, offset, flip, shift, autoUpdate } from '@floating-ui/react';
import { CityConfig } from '../../../../config/cityConfig';
import { searchLocations, HereLocation, PLACE_CATEGORIES } from '../../../services/hereApi';
import { generateUUID } from '../../../../utils/uuid';
import './LocationAutocomplete.css';

interface LocationAutocompleteProps {
  value: string;
  onChange: (location: {
    address: string;
    coordinates: [number, number];
    name?: string;
    iata?: string;
  }) => void;
  placeholder?: string;
  className?: string;
  cityConfig?: CityConfig;
  isAirportSearch?: boolean;
}

// Debounce utility function
const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: ReturnType<typeof setTimeout>;

  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), wait);
  };
};

// Add interface for recent searches
interface RecentSearch {
  id: string;
  query: string;
  location: {
    address: string;
    coordinates: [number, number];
    name?: string;
    iata?: string;
  };
  timestamp: number;
}

interface GroupedSuggestions {
  [category: string]: HereLocation[];
}

interface CategoryState {
  [category: string]: boolean;
}

export const LocationAutocomplete = ({
  value,
  onChange,
  placeholder,
  className,
  cityConfig,
  isAirportSearch
}: LocationAutocompleteProps) => {
  const [inputValue, setInputValue] = useState(value);
  const [suggestions, setSuggestions] = useState<HereLocation[]>([]);
  const [groupedSuggestions, setGroupedSuggestions] = useState<GroupedSuggestions>({});
  const [expandedCategories, setExpandedCategories] = useState<CategoryState>({});
  const [recentSearches, setRecentSearches] = useState<RecentSearch[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadingRef = useRef<HTMLDivElement>(null);

  const { refs, floatingStyles } = useFloating({
    placement: 'bottom-start',
    middleware: [offset(4), flip(), shift()],
    whileElementsMounted: autoUpdate,
  });

  // Log initial props and config
  useEffect(() => {
    bookingDebugger.log('info', 'LocationAutocomplete', 'Component initialized', {
      initialValue: value,
      cityConfig: cityConfig ? { ...cityConfig, bbox: cityConfig.bbox } : 'none',
      isAirportSearch
    });
  }, []);

  // Track value changes
  useEffect(() => {
    if (value !== inputValue) {
      bookingDebugger.log('debug', 'LocationAutocomplete', 'External value changed', {
        from: inputValue,
        to: value
      });
      setInputValue(value);
    }
  }, [value]);

  // Load recent searches on mount
  useEffect(() => {
    const saved = localStorage.getItem('recent-location-searches');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setRecentSearches(parsed);
      } catch (e) {
        console.error('Failed to parse recent searches:', e);
      }
    }
  }, []);

  // Setup infinite scroll observer
  useEffect(() => {
    if (loadingRef.current && hasMore) {
      observerRef.current = new IntersectionObserver(
        entries => {
          if (entries[0].isIntersecting && !isLoading) {
            setPage(prev => prev + 1);
          }
        },
        { threshold: 0.5 }
      );

      observerRef.current.observe(loadingRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, isLoading]);

  const getUserLocation = async () => {
    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject);
      });

      setUserLocation({
        lat: position.coords.latitude,
        lng: position.coords.longitude
      });

      // Trigger a new search with the user's location
      if (inputValue) {
        handleSearch(inputValue, {
          lat: position.coords.latitude,
          lng: position.coords.longitude
        });
      }
    } catch (error) {
      setError('Could not get your location. Please check your browser settings.');
    }
  };

  const handleSearch = useCallback(async (query: string, locationOverride?: { lat: number; lng: number }) => {
    if (!query || query.length < 2) {
      bookingDebugger.log('debug', 'LocationAutocomplete', 'Search skipped - query too short', { query });
      setSuggestions([]);
      setGroupedSuggestions({});
      return;
    }

    bookingDebugger.log('info', 'LocationAutocomplete', `Initiating search for: ${query}`, {
      userLocation: userLocation ? `${userLocation.lat},${userLocation.lng}` : 'none',
      cityBounds: cityConfig?.bbox || 'none'
    });
    
    setIsLoading(true);
    setError(null);

    try {
      // Get user's location for better results
      let lat: number | undefined = locationOverride?.lat;
      let lng: number | undefined = locationOverride?.lng;

      if (!locationOverride && userLocation) {
        lat = userLocation.lat;
        lng = userLocation.lng;
        bookingDebugger.log('debug', 'LocationAutocomplete', 'Using user location');
      } else if (!locationOverride && cityConfig?.bbox) {
        const [minLng, minLat, maxLng, maxLat] = cityConfig.bbox.split(',').map(Number);
        lat = (minLat + maxLat) / 2;
        lng = (minLng + maxLng) / 2;
        bookingDebugger.log('debug', 'LocationAutocomplete', 'Using city center location');
      }

      const results = await searchLocations(query, {
        isAirportSearch,
        lat,
        lng,
        limit: 20,
        offset: page * 20
      });

      bookingDebugger.log('debug', 'LocationAutocomplete', `Received ${results.length} results`);

      // Group results by category
      const grouped = results.reduce((acc: GroupedSuggestions, item) => {
        const category = item.details?.category?.[0] || 'Other';
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(item);
        return acc;
      }, {});

      bookingDebugger.log('debug', 'LocationAutocomplete', 'Grouped results by category', {
        categories: Object.keys(grouped),
        counts: Object.fromEntries(Object.entries(grouped).map(([k, v]) => [k, v.length]))
      });

      // Sort categories by number of results
      const sortedGrouped = Object.fromEntries(
        Object.entries(grouped).sort(([, a], [, b]) => b.length - a.length)
      );

      if (page === 0) {
        setGroupedSuggestions(sortedGrouped);
        setSuggestions(results);
      } else {
        setGroupedSuggestions(prev => {
          const newGrouped = { ...prev };
          Object.entries(sortedGrouped).forEach(([category, items]) => {
            if (!newGrouped[category]) {
              newGrouped[category] = items;
            } else {
              newGrouped[category] = [...newGrouped[category], ...items];
            }
          });
          return newGrouped;
        });
        setSuggestions(prev => [...prev, ...results]);
      }

      setHasMore(results.length === 20);
      setIsOpen(true);

      // Initialize expanded state for new categories
      setExpandedCategories(prev => ({
        ...prev,
        ...Object.fromEntries(
          Object.keys(sortedGrouped).map(category => [category, prev[category] ?? true])
        )
      }));

      bookingDebugger.log('debug', 'LocationAutocomplete', 'Search results received', {
        count: results.length,
        query,
        results
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Search failed';
      setError(errorMessage);
      bookingDebugger.log('error', 'LocationAutocomplete', 'Search failed', {
        error: err,
        query,
        errorMessage
      });
      setSuggestions([]);
      setGroupedSuggestions({});
    } finally {
      setIsLoading(false);
    }
  }, [userLocation, cityConfig, isAirportSearch, page]);

  const debouncedSearch = useCallback(
    debounce((query: string) => {
      bookingDebugger.log('debug', 'LocationAutocomplete', 'Debounced search triggered', { query });
      setPage(0);
      handleSearch(query);
    }, 300),
    []
  );

  // Save recent search
  const saveRecentSearch = (location: HereLocation) => {
    const newSearch: RecentSearch = {
      id: generateUUID(),
      query: inputValue,
      location: {
        address: location.address.label,
        coordinates: location.coordinates,
        name: location.name,
        iata: location.iata
      },
      timestamp: Date.now()
    };

    setRecentSearches(prev => {
      const updated = [newSearch, ...prev.slice(0, 4)]; // Keep only last 5
      localStorage.setItem('recent-location-searches', JSON.stringify(updated));
      return updated;
    });
  };

  const handleSelect = useCallback((suggestion: HereLocation) => {
    bookingDebugger.log('info', 'LocationAutocomplete', 'Location selected', {
      id: suggestion.id,
      name: suggestion.name,
      coordinates: suggestion.coordinates
    });
    
    setInputValue(suggestion.address.label);
    onChange({
      address: suggestion.address.label,
      coordinates: suggestion.coordinates,
      name: suggestion.name,
      iata: suggestion.iata
    });
    saveRecentSearch(suggestion);
    setSuggestions([]);
    setIsOpen(false);
  }, [onChange]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    debouncedSearch(value);
    bookingDebugger.log('debug', 'LocationAutocomplete', `Input changed: ${value}`);
  };

  const handleFocus = () => {
    if (inputValue && suggestions.length > 0) {
      setIsOpen(true);
    }
  };

  const handleBlur = () => {
    // Delay closing to allow click events on suggestions
    setTimeout(() => setIsOpen(false), 200);
  };

  const handleClear = () => {
    setInputValue('');
    setSuggestions([]);
    setGroupedSuggestions({});
    setIsOpen(false);
    onChange({ address: '', coordinates: [0, 0] });
  };

  const toggleCategory = (category: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  return (
    <div ref={containerRef} className="relative">
      <div className="relative">
        <input
          ref={refs.setReference}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={`w-full h-[46px] bg-surface text-theme-primary border border-border rounded-lg px-4 py-2 pl-10 pr-20 focus:outline-none focus:ring-2 focus:ring-primary/50 ${className}`}
        />
        <MapPinIcon className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-primary" />

        <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-2">
          {inputValue && (
            <button
              onClick={handleClear}
              className="p-1 hover:bg-surface-light rounded-full"
            >
              <XMarkIcon className="w-5 h-5 text-theme-muted" />
            </button>
          )}
          <button
            onClick={getUserLocation}
            className="p-1 hover:bg-surface-light rounded-full"
          >
            <MapIcon className="w-5 h-5 text-theme-muted" />
          </button>
        </div>
      </div>

      {isOpen && (suggestions.length > 0 || recentSearches.length > 0 || isLoading || error) && (
        <div
          ref={refs.setFloating}
          style={floatingStyles}
          className="z-50 w-full bg-surface border border-border rounded-lg shadow-lg mt-1 overflow-hidden max-h-[60vh] overflow-y-auto"
        >
          {/* Show recent searches when no query */}
          {!inputValue && recentSearches.length > 0 && (
            <div className="p-2">
              <div className="text-xs text-theme-muted px-2 py-1">Recent Searches</div>
              {recentSearches.map((recent) => (
                <button
                  key={recent.id}
                  onClick={() => {
                    setInputValue(recent.location.address);
                    onChange(recent.location);
                    setIsOpen(false);
                  }}
                  className="w-full px-4 py-3 text-left hover:bg-surface-light transition-colors flex items-start gap-3"
                >
                  <ClockIcon className="w-5 h-5 text-theme-muted flex-shrink-0 mt-0.5" />
                  <div>
                    <div className="text-theme-primary font-medium">
                      {recent.location.name || recent.location.address}
                    </div>
                    <div className="text-sm text-theme-muted">
                      {recent.location.address}
                    </div>
                    <div className="text-xs text-theme-muted">
                      {new Date(recent.timestamp).toLocaleDateString()}
                    </div>
                  </div>
                </button>
              ))}
              <div className="border-t border-border mt-2" />
            </div>
          )}

          {isLoading && page === 0 && (
            <div className="p-4 space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="animate-pulse flex gap-3">
                  <div className="w-5 h-5 bg-surface-light rounded-full" />
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-surface-light rounded w-3/4" />
                    <div className="h-3 bg-surface-light rounded w-1/2" />
                  </div>
                </div>
              ))}
            </div>
          )}

          {error && (
            <div className="p-4 text-sm text-error">
              {error}
            </div>
          )}

          {!isLoading && !error && Object.entries(groupedSuggestions).map(([category, items]) => (
            <div key={category} className="border-t first:border-t-0 border-border">
              <button
                onClick={() => toggleCategory(category)}
                className="w-full px-4 py-2 text-left bg-surface-light hover:bg-surface-light/80 transition-colors flex items-center justify-between"
              >
                <span className="text-sm font-medium text-theme-primary">
                  {category} ({items.length})
                </span>
                {expandedCategories[category] ? (
                  <ChevronUpIcon className="w-4 h-4 text-theme-muted" />
                ) : (
                  <ChevronDownIcon className="w-4 h-4 text-theme-muted" />
                )}
              </button>

              {expandedCategories[category] && items.map((suggestion) => (
                <button
                  key={suggestion.id}
                  onClick={() => handleSelect(suggestion)}
                  className="w-full px-4 py-3 text-left hover:bg-surface-light transition-colors flex items-start gap-3"
                >
                  <MapPinIcon className="w-5 h-5 text-primary flex-shrink-0 mt-0.5" />
                  <div>
                    <div className="text-theme-primary font-medium">
                      {suggestion.name}
                    </div>
                    <div className="text-sm text-theme-muted">
                      {suggestion.address.street ? (
                        <>
                          {suggestion.address.street}
                          {suggestion.address.houseNumber && `, ${suggestion.address.houseNumber}`}
                        </>
                      ) : suggestion.address.label}
                    </div>
                    {(suggestion.address.district || suggestion.address.city) && (
                      <div className="text-xs text-theme-muted">
                        {[
                          suggestion.address.district,
                          suggestion.address.city,
                          suggestion.address.state
                        ].filter(Boolean).join(', ')}
                      </div>
                    )}
                    {suggestion.distance && (
                      <div className="text-xs text-theme-muted mt-1">
                        {(suggestion.distance / 1000).toFixed(1)} km away
                      </div>
                    )}
                  </div>
                </button>
              ))}
            </div>
          ))}

          {hasMore && (
            <div
              ref={loadingRef}
              className="p-4 text-center text-sm text-theme-muted"
            >
              {isLoading ? 'Loading more results...' : 'Scroll for more results'}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
