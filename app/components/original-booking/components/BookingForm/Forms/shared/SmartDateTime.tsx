import { useMemo } from 'react';
import { Input } from '../../../../components/ui/Input';
import { Button } from '../../../../components/ui/Button';
import { CalendarIcon, ClockIcon } from '@heroicons/react/24/outline';

interface SmartDateTimeProps {
  date: string;
  time: string;
  onDateChange: (date: string) => void;
  onTimeChange: (time: string) => void;
}

export const SmartDateTime = ({ date, time, onDateChange, onTimeChange }: SmartDateTimeProps) => {
  const suggestedTimes = useMemo(() => {
    return [
      { label: 'Morning', time: '09:00' },
      { label: 'Mid-Morning', time: '10:00' },
      { label: 'Afternoon', time: '14:00' },
      { label: 'Evening', time: '17:00' }
    ];
  }, []);

  return (
    <div className="space-y-6">
      {/* Date Input */}
      <div>
        <h3 className="text-white mb-2">Pickup Date</h3>
        <Input
          type="date"
          icon={<CalendarIcon className="w-5 h-5" />}
          value={date}
          onChange={(e) => onDateChange(e.target.value)}
          min={new Date().toISOString().split('T')[0]} // Prevent past dates
        />
      </div>

      {/* Time Input */}
      <div>
        <h3 className="text-white mb-2">Pickup Time</h3>
        <Input
          type="time"
          icon={<ClockIcon className="w-5 h-5" />}
          value={time}
          onChange={(e) => onTimeChange(e.target.value)}
        />

        <div className="grid grid-cols-2 gap-2 mt-2">
          {suggestedTimes.map(({ label, time }) => (
            <Button
              key={time}
              variant="outline"
              size="sm"
              onClick={() => onTimeChange(time)}
              className="text-sm py-2"
            >
              {label} ({time})
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
};
