import { useState, useEffect, forwardRef } from 'react';
import DatePicker from 'react-datepicker';
import { CalendarIcon } from '@heroicons/react/24/outline';
import { themeService } from '../../../../services/themeService';
import 'react-datepicker/dist/react-datepicker.css';

interface DateTimePickerProps {
  date: Date | null;
  onChange: (date: Date) => void;
  minDate?: Date;
}

export const DateTimePicker = ({ date, onChange, minDate }: DateTimePickerProps) => {
  const [isDarkTheme, setIsDarkTheme] = useState(themeService.isParentThemeDark());

  // Update theme based on parent theme
  useEffect(() => {
    const updateTheme = () => {
      const isDark = themeService.isParentThemeDark();
      setIsDarkTheme(isDark);
    };

    // Initial theme setup
    updateTheme();

    // Listen for theme changes
    window.addEventListener('themechange', updateTheme);

    // Clean up
    return () => {
      window.removeEventListener('themechange', updateTheme);
    };
  }, []);

  // Custom input component for DatePicker with forwardRef
  const CustomInput = forwardRef<HTMLInputElement, { value?: string; onClick?: () => void }>(
    ({ value, onClick }, ref) => (
      <div className="relative">
        <input
          ref={ref}
          type="text"
          value={value || ''}
          onClick={onClick}
          readOnly
          placeholder="Select date and time"
          className="w-full h-[46px] bg-surface text-theme-primary border border-border rounded-lg px-4 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-primary/50 cursor-pointer"
        />
        <CalendarIcon className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-primary pointer-events-none" />
      </div>
    )
  );

  return (
    <DatePicker
      selected={date}
      onChange={(date: Date | null) => {
        if (date) {
          onChange(date);
        }
      }}
      showTimeSelect
      timeFormat="HH:mm"
      timeIntervals={15}
      dateFormat="MMMM d, yyyy h:mm aa"
      minDate={minDate || new Date()}
      customInput={<CustomInput />}
      popperClassName="datepicker-popper"
      calendarClassName="react-datepicker-calendar"
      wrapperClassName="react-datepicker-wrapper"
    />
  );
};
