import { ReactNode } from 'react';

type FormLayoutProps = {
  children: ReactNode;
};

export const FormLayout = ({ children }: FormLayoutProps) => {
  return (
    <div className="space-y-8">
      {/* Form Sections */}
      <div className="space-y-6">
        {/* Pickup Date/Time */}
        <div>
          <h3 className="text-white mb-4">Pickup Time</h3>
          <div className="grid grid-cols-2 gap-4">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};
