import { useState, useEffect, useRef } from 'react';
import { useBookingStore } from '../../../../store/bookingStore';
import { DateTimePicker } from './shared/DateTimePicker';
import { LocationAutocomplete } from './shared/LocationAutocomplete';
import { ToggleButton } from './shared/ToggleButton';
import {
  PlusIcon,
  MinusIcon,
  XMarkIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  UserGroupIcon,
  CheckBadgeIcon
} from '@heroicons/react/24/outline';
import { Switch } from '@headlessui/react';
import { bookingDebugger } from '../../../utils/debugLogger';
import { cn } from '../../../utils/cn';
import { cityConfigs } from '../../config/cityConfig';
import { scrollFormToTop, detectEnvironment } from '../../../lib/utils';

interface Location {
  address: string;
  coordinates: [number, number];
}

interface Stop {
  id: string;
  location: Location | null;
  isMoving?: boolean;
}

export const PointToPointForm = () => {
  const { pointToPointData, updatePointToPointData, currentStep, setStep } = useBookingStore();
  const [stops, setStops] = useState<Stop[]>(pointToPointData.stops || []);
  const [pickupDate, setPickupDate] = useState<Date | null>(null);
  const [isRoundTrip, setIsRoundTrip] = useState(false);
  const [returnDate, setReturnDate] = useState(new Date());
  const [isStopsExpanded, setIsStopsExpanded] = useState(false);
  const lastUpdateRef = useRef<string | null>(null);
  const formRef = useRef<HTMLDivElement>(null);

  // Add useEffect to sync stops with store
  useEffect(() => {
    if (JSON.stringify(stops) !== JSON.stringify(pointToPointData.stops)) {
      updatePointToPointData({ stops });
    }
  }, [stops, pointToPointData.stops]);

  // Add the missing functions for stop management
  const generateId = () => Math.random().toString(36).substr(2, 9);

  const addStop = () => {
    const newStop = {
      id: generateId(),
      location: null,
      isMoving: false
    };
    setStops(prevStops => [...prevStops, newStop]);
  };

  const removeStop = (stopId: string) => {
    setStops(prevStops => prevStops.filter(stop => stop.id !== stopId));
  };

  const updateStop = (stopId: string, location: Location | null) => {
    setStops(stops.map(stop =>
      stop.id === stopId ? { ...stop, location } : stop
    ));
  };

  const moveStop = async (fromIndex: number, toIndex: number) => {
    if (toIndex < 0 || toIndex >= stops.length) return;

    const newStops = [...stops];
    newStops[fromIndex] = { ...newStops[fromIndex], isMoving: true };
    setStops(newStops);

    await new Promise(resolve => setTimeout(resolve, 200));

    const [removed] = newStops.splice(fromIndex, 1);
    newStops.splice(toIndex, 0, { ...removed, isMoving: false });
    setStops(newStops);
    updatePointToPointData({ stops: newStops });
  };

  const handleLocationUpdate = (type: 'pickup' | 'dropoff', location: Location) => {
    const updateKey = `${type}-${location.address}`;
    if (lastUpdateRef.current === updateKey) {
      return;
    }
    lastUpdateRef.current = updateKey;

    if (type === 'pickup') {
      updatePointToPointData({ pickupLocation: location });
    } else {
      updatePointToPointData({ dropoffLocation: location });
    }
    bookingDebugger.log('info', 'Location', `${type} location changed`, location);
  };

  const handleDateChange = (date: Date) => {
    bookingDebugger.log('info', 'Date', 'Pickup date changed', {
      date: date.toISOString(),
      formattedDate: date.toLocaleDateString()
    });
    setPickupDate(date);
    updatePointToPointData({ pickupDate: date.toISOString() });
  };

  const handleReturnDateChange = (date: Date) => {
    bookingDebugger.log('info', 'Date', 'Return date changed', {
      date: date.toISOString(),
      formattedDate: date.toLocaleDateString()
    });
    setReturnDate(date);
    updatePointToPointData({
      returnDate: date.toISOString()
    });
  };

  const handleCarSeatsToggle = (checked: boolean) => {
    updatePointToPointData({
      needCarSeats: checked,
    });
  };

  const handleNextStep = () => {
    console.log('PointToPointForm - handleNextStep called');
    
    // If form is out of view, scroll it into view
    if (formRef.current) {
      // Get the form's position
      const rect = formRef.current.getBoundingClientRect();
      const offset = 100; // Higher offset for WordPress headers
      
      // Calculate absolute position
      const absolutePosition = window.pageYOffset + rect.top - offset;
      
      console.log('Scrolling to position:', absolutePosition);
      
      // Scroll to position
      window.scrollTo(0, absolutePosition);
    }
    
    // Proceed to next step
    console.log('Setting next step:', currentStep + 1);
    setStep(currentStep + 1);
  };

  const validateForm = () => {
    // Implement form validation logic here
    return true; // Placeholder return, actual implementation needed
  };

  return (
    <div ref={formRef} className="space-y-6 w-full">
      {/* Row 1: Date + Return Trip Toggle */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Pickup Date & Time */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-theme-primary text-base">Pickup Date & Time:</h3>
            <div className="hidden lg:flex items-center gap-2">
              <span className="text-sm text-theme-muted">Return Trip</span>
              <ToggleButton
                checked={pointToPointData.isRoundTrip}
                onChange={(checked) => updatePointToPointData({ isRoundTrip: checked })}
              />
            </div>
          </div>
          <DateTimePicker
            date={pointToPointData.pickupDate ? new Date(pointToPointData.pickupDate) : null}
            onChange={handleDateChange}
          />
        </div>

        {/* Return Trip Toggle - Mobile Only */}
        <div className="lg:hidden">
          <div className="flex items-center justify-between">
            <span className="text-theme-muted text-sm">Return Trip</span>
            <ToggleButton
              checked={pointToPointData.isRoundTrip}
              onChange={(checked) => updatePointToPointData({ isRoundTrip: checked })}
            />
          </div>
        </div>

        {/* Return Date & Time */}
        {pointToPointData.isRoundTrip && (
          <div>
            <h3 className="text-theme-primary text-base mb-2">Return Date & Time:</h3>
            <DateTimePicker
              date={pointToPointData.returnDate ? new Date(pointToPointData.returnDate) : null}
              onChange={handleReturnDateChange}
              minDate={pointToPointData.pickupDate ? new Date(pointToPointData.pickupDate) : undefined}
            />
          </div>
        )}
      </div>

      {/* Pickup Location */}
      <div className="relative z-[90]">
        <h3 className="text-theme-primary text-base mb-2">Pickup Location:</h3>
        <LocationAutocomplete
          value={pointToPointData.pickupLocation?.address || ''}
          onChange={(location) => handleLocationUpdate('pickup', location)}
          placeholder="Enter pickup address"
          className="w-full"
          cityConfig={cityConfigs.worldwide}
        />
      </div>

      {/* Stops Section */}
      <div className="relative z-[80]">
        <button
          onClick={() => setIsStopsExpanded(!isStopsExpanded)}
          className="w-full flex items-center justify-between p-4 bg-surface rounded-lg text-theme-primary hover:bg-surface-light transition-colors"
        >
          <div className="flex items-center gap-3">
            <h3 className="text-sm text-theme-primary">Stops</h3>
            <span className="text-xs text-theme-muted">{stops.length} stop{stops.length !== 1 ? 's' : ''}</span>
          </div>
          <ChevronDownIcon
            className={cn(
              "w-5 h-5 text-primary transition-transform duration-200",
              isStopsExpanded ? "rotate-180" : ""
            )}
          />
        </button>

        {isStopsExpanded && (
          <div className="space-y-4 p-4 bg-surface rounded-lg">
            {stops.map((stop, index) => (
              <div key={stop.id} className="flex items-center gap-3">
                <div className="flex-grow">
                  <LocationAutocomplete
                    value={stop.location?.address || ''}
                    onChange={(location) => updateStop(stop.id, location)}
                    placeholder={`Stop ${index + 1}`}
                    className="w-full"
                    cityConfig={cityConfigs.worldwide}
                  />
                </div>
                <div className="flex items-center gap-2 flex-shrink-0">
                  <button
                    onClick={() => removeStop(stop.id)}
                    className="p-2 text-red-400 hover:text-red-300 hover:bg-red-400/10 rounded-lg transition-colors"
                    title="Remove stop"
                  >
                    <XMarkIcon className="w-5 h-5" />
                  </button>
                </div>
              </div>
            ))}
            <button
              onClick={addStop}
              className="w-full p-3 rounded-lg border-2 border-dashed border-border text-primary hover:text-primary/70 hover:border-primary/20 transition-colors"
            >
              <PlusIcon className="w-5 h-5 mx-auto" />
            </button>
          </div>
        )}
      </div>

      {/* Drop-off Location */}
      <div className="relative z-20">
        <h3 className="text-theme-primary text-base mb-2">Drop-off Location:</h3>
        <LocationAutocomplete
          value={pointToPointData.dropoffLocation?.address || ''}
          onChange={(location) => handleLocationUpdate('dropoff', location)}
          placeholder="Enter drop-off address"
          className="w-full"
          cityConfig={cityConfigs.worldwide}
        />
      </div>

      {/* Row 3: Passengers and Car Seats */}
      <div className="grid grid-cols-3 gap-4">
        {/* Adults Counter (1/3) */}
        <div>
          <h3 className="text-theme-primary text-base mb-2">Adults:</h3>
          <div className="flex items-center bg-surface rounded-lg">
            <button
              onClick={() => updatePointToPointData({
                adults: Math.max(1, (pointToPointData.adults || 1) - 1)
              })}
              className="px-4 py-3 text-primary hover:text-primary/70 transition-colors"
            >
              <MinusIcon className="w-5 h-5" />
            </button>
            <div className="flex-1 text-center text-theme-primary">
              {pointToPointData.adults || 1}
            </div>
            <button
              onClick={() => updatePointToPointData({
                adults: (pointToPointData.adults || 1) + 1
              })}
              className="px-4 py-3 text-primary hover:text-primary/70 transition-colors"
            >
              <PlusIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Children Counter (1/3) */}
        <div>
          <h3 className="text-theme-primary text-base mb-2">Children:</h3>
          <div className="flex items-center bg-surface rounded-lg">
            <button
              onClick={() => updatePointToPointData({
                children: Math.max(0, (pointToPointData.children || 0) - 1)
              })}
              className="px-4 py-3 text-primary hover:text-primary/70 transition-colors"
            >
              <MinusIcon className="w-5 h-5" />
            </button>
            <div className="flex-1 text-center text-theme-primary">
              {pointToPointData.children || 0}
            </div>
            <button
              onClick={() => updatePointToPointData({
                children: (pointToPointData.children || 0) + 1
              })}
              className="px-4 py-3 text-primary hover:text-primary/70 transition-colors"
            >
              <PlusIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Car Seats Toggle (1/3) */}
        {(pointToPointData.children || 0) > 0 && (
          <div>
            <h3 className="text-theme-primary text-base mb-2">Car Seats</h3>
            <div className="h-[46px] flex items-center justify-between bg-surface rounded-lg px-4">
              <ToggleButton
                checked={pointToPointData.needCarSeats}
                onChange={(checked) => updatePointToPointData({ needCarSeats: checked })}
              />
            </div>
          </div>
        )}
      </div>

      {/* Car Seats Options - Shows when toggle is on */}
      {(pointToPointData.children || 0) > 0 && pointToPointData.needCarSeats && (
        <div className="grid grid-cols-3 gap-4 mt-4">
          {/* Infant Seats */}
          <div className="bg-surface p-4 rounded-lg">
            <div className="text-theme-primary mb-1">Infant Seats</div>
            <div className="text-sm text-theme-muted mb-3">0-1 years</div>
            <div className="flex items-center justify-between">
              <button
                onClick={() => updatePointToPointData({
                  infantSeats: Math.max(0, (pointToPointData.infantSeats || 0) - 1)
                })}
                className="text-primary hover:text-primary/70 transition-colors"
              >
                <MinusIcon className="w-5 h-5" />
              </button>
              <span className="text-theme-primary">{pointToPointData.infantSeats || 0}</span>
              <button
                onClick={() => updatePointToPointData({
                  infantSeats: (pointToPointData.infantSeats || 0) + 1
                })}
                className="text-primary hover:text-primary/70 transition-colors"
              >
                <PlusIcon className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Toddler Seats */}
          <div className="bg-surface p-4 rounded-lg">
            <div className="text-theme-primary mb-1">Toddler Seats</div>
            <div className="text-sm text-theme-muted mb-3">1-3 years</div>
            <div className="flex items-center justify-between">
              <button
                onClick={() => updatePointToPointData({
                  toddlerSeats: Math.max(0, (pointToPointData.toddlerSeats || 0) - 1)
                })}
                className="text-primary hover:text-primary/70 transition-colors"
              >
                <MinusIcon className="w-5 h-5" />
              </button>
              <span className="text-theme-primary">{pointToPointData.toddlerSeats || 0}</span>
              <button
                onClick={() => updatePointToPointData({
                  toddlerSeats: (pointToPointData.toddlerSeats || 0) + 1
                })}
                className="text-primary hover:text-primary/70 transition-colors"
              >
                <PlusIcon className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Booster Seats */}
          <div className="bg-surface p-4 rounded-lg">
            <div className="text-theme-primary mb-1">Booster Seats</div>
            <div className="text-sm text-theme-muted mb-3">4-7 years</div>
            <div className="flex items-center justify-between">
              <button
                onClick={() => updatePointToPointData({
                  boosterSeats: Math.max(0, (pointToPointData.boosterSeats || 0) - 1)
                })}
                className="text-primary hover:text-primary/70 transition-colors"
              >
                <MinusIcon className="w-5 h-5" />
              </button>
              <span className="text-theme-primary">{pointToPointData.boosterSeats || 0}</span>
              <button
                onClick={() => updatePointToPointData({
                  boosterSeats: (pointToPointData.boosterSeats || 0) + 1
                })}
                className="text-primary hover:text-primary/70 transition-colors"
              >
                <PlusIcon className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Next Button */}
      <div className="flex justify-end pt-6">
        <button
          onClick={handleNextStep}
          className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-light transition-colors"
        >
          Next: Select Vehicle
        </button>
      </div>
    </div>
  );
};
