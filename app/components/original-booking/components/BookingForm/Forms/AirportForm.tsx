import { useState, useEffect, useRef } from 'react';
import { useBookingStore } from '../../store/bookingStore';
import { DateTimePicker } from './shared/DateTimePicker';
import { LocationAutocomplete } from './shared/LocationAutocomplete';
import { PaperAirplaneIcon, MapPinIcon, PlusIcon, MinusIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useFlightTracking } from '../../hooks/useFlightTracking';
import { ToggleButton } from './shared/ToggleButton';
import { bookingDebugger } from '../../utils/debugLogger';
import { Switch } from '@headlessui/react';
import { cityConfigs } from '../../config/cityConfig';
import { SmartDateTime } from './shared/SmartDateTime';
import { LocationInput } from '../../components/ui/LocationInput';
import { FlightInput } from './shared/FlightInput';
import { Location } from '../../types/booking';
import type { FlightDetails } from '../../store/bookingStore';
import { scrollFormToTop } from '../../lib/utils';

// Add the EnhancedFlightStats interface at the top
interface EnhancedFlightStats {
  onTimePerformance: number;
  averageDuration: string;
  baggageClaim: string;
  aircraftType: string;
  flightDistance: string;
  terminalTransferTime: string;
  weatherDeparture: {
    temperature: string;
    condition: string;
  };
  weatherArrival: {
    temperature: string;
    condition: string;
  };
  previousFlightStatus: {
    date: string;
    status: string;
    delay: number;
  }[];
}

// Add debounce utility
const debounce = (fn: Function, ms = 300) => {
  let timeoutId: ReturnType<typeof setTimeout>;
  return function (this: any, ...args: any[]) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn.apply(this, args), ms);
  };
};

export const AirportForm = () => {
  const {
    airportData,
    updateAirportData,
    currentStep,
    setStep
  } = useBookingStore();

  // Initialize dates from stored values
  const [departureDate, setDepartureDate] = useState<Date | null>(
    airportData.departureDate ? new Date(airportData.departureDate) : null
  );
  const [returnDate, setReturnDate] = useState<Date | null>(
    airportData.returnDate ? new Date(airportData.returnDate) : null
  );
  const [departureFlightData, setDepartureFlightData] = useState<FlightDetails | null>(null);
  const [returnFlightData, setReturnFlightData] = useState<FlightDetails | null>(null);

  // Flight tracking hooks
  const {
    flightInfo: departureFlightDataInfo,
    loading: departureLoading,
    error: departureError,
    trackFlight: trackDepartureFlight,
    clearFlightInfo: clearDepartureFlightInfo
  } = useFlightTracking();

  const {
    flightInfo: returnFlightDataInfo,
    loading: returnLoading,
    error: returnError,
    trackFlight: trackReturnFlight,
    clearFlightInfo: clearReturnFlightInfo
  } = useFlightTracking();

  // Sync component state with store on mount
  useEffect(() => {
    bookingDebugger.log('debug', 'AirportForm', 'Component mounted with states:', {
      isRoundTrip: airportData.isRoundTrip,
      departureDate,
      returnDate,
      departureFlightData,
      returnFlightData,
      departureFlightDataInfo,
      returnFlightDataInfo
    });
  }, []);

  // Update local state when store values change
  useEffect(() => {
    if (airportData.departureDate) {
      setDepartureDate(new Date(airportData.departureDate));
    }
    if (airportData.returnDate) {
      setReturnDate(new Date(airportData.returnDate));
    }
  }, [airportData.departureDate, airportData.returnDate]);

  // Update store when flight data changes
  useEffect(() => {
    if (returnFlightDataInfo) {
      const flightData: FlightDetails = {
        airline: returnFlightDataInfo.airline?.name,
        flightNumber: returnFlightDataInfo.flightNumber,
        departureTime: returnFlightDataInfo.departure?.scheduled?.local,
        arrivalTime: returnFlightDataInfo.arrival?.scheduled?.local,
        status: returnFlightDataInfo.status?.text
      };
      updateAirportData({
        returnFlightData: flightData
      });
    }
  }, [returnFlightDataInfo]);

  // Update store when departure flight data changes
  useEffect(() => {
    if (departureFlightDataInfo) {
      const flightData: FlightDetails = {
        airline: departureFlightDataInfo.airline?.name,
        flightNumber: departureFlightDataInfo.flightNumber,
        departureTime: departureFlightDataInfo.departure?.scheduled?.local,
        arrivalTime: departureFlightDataInfo.arrival?.scheduled?.local,
        status: departureFlightDataInfo.status?.text
      };
      updateAirportData({
        departureFlightData: flightData
      });
    }
  }, [departureFlightDataInfo]);

  // Handle departure flight changes
  const handleDepartureFlightChange = (value: string) => {
    const formattedValue = value.toUpperCase().trim();
    setDepartureFlightData({ flightNumber: formattedValue });
    updateAirportData({ departureFlightData: { flightNumber: formattedValue } });

    // Only search if we have a valid flight number format
    const flightNumberPattern = /^[A-Za-z]{2,3}\s*\d{1,4}$/;
    if (flightNumberPattern.test(formattedValue)) {
      trackDepartureFlight(formattedValue, departureDate || undefined);
    }
  };

  // Handle return flight changes
  const handleReturnFlightChange = (value: string) => {
    const formattedValue = value.toUpperCase().trim();
    setReturnFlightData({ flightNumber: formattedValue });
    updateAirportData({ returnFlightData: { flightNumber: formattedValue } });

    // Only search if we have a valid flight number format
    const flightNumberPattern = /^[A-Za-z]{2,3}\s*\d{1,4}$/;
    if (flightNumberPattern.test(formattedValue)) {
      trackReturnFlight(formattedValue, returnDate || undefined);
    }
  };

  // Clear functions
  const clearDepartureFlight = () => {
    setDepartureFlightData(null);
    updateAirportData({
      departureFlightData: null
    });
    clearDepartureFlightInfo();
  };

  const clearReturnFlight = () => {
    setReturnFlightData(null);
    updateAirportData({ returnFlightData: null });
    clearReturnFlightInfo();
  };

  // Handle date changes
  const handleDepartureDateChange = (date: Date) => {
    setDepartureDate(date);
    updateAirportData({
      departureDate: date.toISOString(),
      departureTime: date.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      })
    });
    // Refresh flight tracking with new date if flight number exists
    if (airportData.departureFlightData?.flightNumber) {
      trackDepartureFlight(airportData.departureFlightData.flightNumber, date);
    }
    bookingDebugger.log('info', 'Date', 'Departure date changed');
  };

  const handleReturnDateChange = (date: Date) => {
    setReturnDate(date);
    updateAirportData({
      returnDate: date.toISOString(),
      returnTime: date.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      })
    });
    // Refresh flight tracking with new date if flight number exists
    if (airportData.returnFlightData?.flightNumber) {
      trackReturnFlight(airportData.returnFlightData.flightNumber, date);
    }
    bookingDebugger.log('info', 'Date', 'Return date changed');
  };

  // Handle round trip toggle
  const handleRoundTripToggle = (checked: boolean) => {
    updateAirportData({
      isRoundTrip: checked,
      ...(checked ? {} : {
        returnDate: null,
        returnTime: null,
        returnFlightData: null
      })
    });
  };

  // Handle car seats change
  const handleCarSeatsChange = (seatType: string, newValue: number) => {
    updateAirportData({
      [seatType]: newValue
    });
  };

  const formRef = useRef<HTMLDivElement>(null);

  const handleNextStep = () => {
    console.log('AirportForm - handleNextStep called');
    
    // If form is out of view, scroll it into view
    if (formRef.current) {
      // Get the form's position
      const rect = formRef.current.getBoundingClientRect();
      const offset = 100; // Higher offset for WordPress headers
      
      // Calculate absolute position
      const absolutePosition = window.pageYOffset + rect.top - offset;
      
      console.log('Scrolling to position:', absolutePosition);
      
      // Scroll to position
      window.scrollTo(0, absolutePosition);
    }
    
    // Proceed to next step
    setStep(currentStep + 1);
  };

  return (
    <div ref={formRef} className="space-y-6 w-full">
      {/* Row 1: Date/Time and Flight Number */}
      <div className="flex gap-4 relative z-[100]">
        {/* Pickup Date */}
        <div className="w-1/2">
          <h3 className="text-theme-primary text-base mb-2">Pickup Date & Time:</h3>
          <DateTimePicker
            date={departureDate}
            onChange={handleDepartureDateChange}
            minDate={new Date()}
          />
        </div>

        {/* Flight Number */}
        <div className="w-1/2">
          <h3 className="text-theme-primary text-base mb-2">Flight Number:</h3>
          <div className="relative">
            <input
              type="text"
              value={departureFlightData?.flightNumber || ''}
              onChange={(e) => handleDepartureFlightChange(e.target.value)}
              placeholder="AA123"
              className="w-full h-[46px] bg-surface text-theme-primary px-4 rounded-lg pl-10 pr-10 focus:outline-none focus:ring-2 focus:ring-primary/50"
              maxLength={6}
            />
            <PaperAirplaneIcon className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-primary" />
            {departureFlightData && (
              <button
                onClick={clearDepartureFlight}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-primary hover:text-primary/70 transition-colors"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Flight Details */}
      {departureFlightData && (
        <div className="bg-surface rounded-lg p-4">
          {/* Top Row with Flight Info and Status */}
          <div className="flex items-center justify-between mb-4 md:mb-0">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                <PaperAirplaneIcon className="w-5 h-5 text-primary" />
              </div>
              <div>
                <div className="text-theme-muted text-xs">Departure Trip</div>
                <div className="text-theme-primary text-sm font-medium">{departureFlightData.airline} {departureFlightData.flightNumber}</div>
              </div>
            </div>

            {/* Flight Status */}
            <div className="px-3 py-1 rounded-full text-xs" style={{ backgroundColor: departureFlightData.status }}>
              {departureFlightData.status}
            </div>
          </div>

          {/* Flight Route Container - Grid on Mobile, Flex on Desktop */}
          <div className="grid grid-cols-2 gap-8 md:hidden">
            {/* Departure */}
            <div>
              <div className="text-theme-muted text-xs mb-1">From</div>
              <div className="text-theme-primary text-lg font-medium">{departureFlightData.departureTime}</div>
            </div>

            {/* Arrival */}
            <div>
              <div className="text-theme-muted text-xs mb-1">To</div>
              <div className="text-theme-primary text-lg font-medium">{departureFlightData.arrivalTime}</div>
            </div>
          </div>

          {/* Desktop Layout */}
          <div className="hidden md:flex md:items-center gap-6 mt-4">
            {/* Departure */}
            <div className="flex-1">
              <div className="text-theme-muted text-xs">From</div>
              <div className="flex items-baseline gap-2">
                <span className="text-theme-primary text-lg font-medium">{departureFlightData.departureTime}</span>
              </div>
            </div>

            {/* Flight Path */}
            <div className="flex-shrink-0 px-2">
              <div className="w-16 h-px bg-primary relative top-4">
                <PaperAirplaneIcon className="absolute -top-2 left-1/2 -translate-x-1/2 w-4 h-4 text-primary transform rotate-90" />
              </div>
            </div>

            {/* Arrival */}
            <div className="flex-1">
              <div className="text-theme-muted text-xs">To</div>
              <div className="flex items-baseline gap-2">
                <span className="text-theme-primary text-lg font-medium">{departureFlightData.arrivalTime}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Locations */}
      <div className="space-y-4">
        <div className="relative z-[90]">
          <h3 className="text-theme-primary text-base mb-2">Pickup Location:</h3>
          <LocationAutocomplete
            value={airportData.pickupLocation?.address || ''}
            onChange={(location) => updateAirportData({ pickupLocation: location })}
            placeholder="Enter pickup address"
            className="w-full"
            cityConfig={cityConfigs.worldwide}
            isAirportSearch={true}
          />
        </div>

        <div className="relative z-[85]">
          <h3 className="text-theme-primary text-base mb-2">Drop-off Location:</h3>
          <LocationAutocomplete
            value={airportData.dropoffLocation?.address || ''}
            onChange={(location) => updateAirportData({ dropoffLocation: location })}
            placeholder="Enter drop-off address"
            className="w-full"
            cityConfig={cityConfigs.worldwide}
            isAirportSearch={true}
          />
        </div>
      </div>

      {/* Return Trip Section */}
      <div className="grid grid-cols-12 gap-4">
        {/* Return Trip Toggle */}
        <div className="col-span-3">
          <h3 className="text-theme-primary text-base mb-2">Return Trip:</h3>
          <div className="h-[46px] flex items-center justify-between bg-surface rounded-lg px-4">
            <ToggleButton
              checked={airportData.isRoundTrip}
              onChange={handleRoundTripToggle}
            />
          </div>
        </div>

        {airportData.isRoundTrip && (
          <>
            {/* Return Date & Time */}
            <div className="col-span-5">
              <h3 className="text-theme-primary text-base mb-2">Return Date/Time:</h3>
              <DateTimePicker
                date={returnDate}
                onChange={handleReturnDateChange}
                minDate={departureDate || new Date()}
              />
            </div>

            {/* Return Flight Number */}
            <div className="col-span-4">
              <h3 className="text-theme-primary text-base mb-2">Return Flight #:</h3>
              <div className="relative">
                <input
                  type="text"
                  value={returnFlightData?.flightNumber || ''}
                  onChange={(e) => handleReturnFlightChange(e.target.value)}
                  placeholder="AA123"
                  maxLength={6}
                  className="w-full h-[46px] bg-surface text-theme-primary px-4 rounded-lg pl-10 pr-10 focus:outline-none focus:ring-2 focus:ring-primary/50"
                />
                <PaperAirplaneIcon className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-primary" />
                {returnFlightData && (
                  <button
                    onClick={clearReturnFlight}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-theme-muted hover:text-theme-primary"
                  >
                    <XMarkIcon className="w-5 h-5" />
                  </button>
                )}
              </div>
            </div>
          </>
        )}
      </div>

      {/* Return Flight Details */}
      {returnFlightData && airportData.isRoundTrip && (
        <div className="bg-surface rounded-lg p-4">
          {/* Top Row with Flight Info and Status */}
          <div className="flex items-center justify-between mb-4 md:mb-0">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                <PaperAirplaneIcon className="w-5 h-5 text-primary" />
              </div>
              <div>
                <div className="text-theme-muted text-xs">Return Trip</div>
                <div className="text-theme-primary text-sm font-medium">{returnFlightData.airline} {returnFlightData.flightNumber}</div>
              </div>
            </div>

            {/* Flight Status */}
            <div className="px-3 py-1 rounded-full text-xs" style={{ backgroundColor: returnFlightData.status }}>
              {returnFlightData.status}
            </div>
          </div>

          {/* Flight Route Container - Grid on Mobile, Flex on Desktop */}
          <div className="grid grid-cols-2 gap-8 md:hidden">
            {/* Departure */}
            <div>
              <div className="text-theme-muted text-xs mb-1">From</div>
              <div className="text-theme-primary text-lg font-medium">{returnFlightData.departureTime}</div>
            </div>

            {/* Arrival */}
            <div>
              <div className="text-theme-muted text-xs mb-1">To</div>
              <div className="text-theme-primary text-lg font-medium">{returnFlightData.arrivalTime}</div>
            </div>
          </div>

          {/* Desktop Layout */}
          <div className="hidden md:flex md:items-center gap-6 mt-4">
            {/* Departure */}
            <div className="flex-1">
              <div className="text-theme-muted text-xs">From</div>
              <div className="flex items-baseline gap-2">
                <span className="text-theme-primary text-lg font-medium">{returnFlightData.departureTime}</span>
              </div>
            </div>

            {/* Flight Path */}
            <div className="flex-shrink-0 px-2">
              <div className="w-16 h-px bg-primary relative top-4">
                <PaperAirplaneIcon className="absolute -top-2 left-1/2 -translate-x-1/2 w-4 h-4 text-primary transform rotate-90" />
              </div>
            </div>

            {/* Arrival */}
            <div className="flex-1">
              <div className="text-theme-muted text-xs">To</div>
              <div className="flex items-baseline gap-2">
                <span className="text-theme-primary text-lg font-medium">{returnFlightData.arrivalTime}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Passengers and Car Seats */}
      <div className="grid grid-cols-10 gap-4">
        {/* Adults Counter */}
        <div className="col-span-4">
          <h3 className="text-theme-primary text-base mb-2">Adults:</h3>
          <div className="flex items-center bg-surface rounded-lg">
            <button
              onClick={() => updateAirportData({
                adults: Math.max(1, (airportData.adults || 1) - 1)
              })}
              className="px-4 py-3 text-primary hover:text-primary/70 transition-colors"
              disabled={airportData.adults <= 1}
            >
              <MinusIcon className="w-5 h-5" />
            </button>
            <div className="flex-1 text-center">
              <span className="text-theme-primary font-medium">{airportData.adults}</span>
            </div>
            <button
              onClick={() => updateAirportData({
                adults: (airportData.adults || 1) + 1
              })}
              className="px-4 py-3 text-primary hover:text-primary/70 transition-colors"
            >
              <PlusIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Children Counter */}
        <div className="col-span-4">
          <h3 className="text-theme-primary text-base mb-2">Children:</h3>
          <div className="flex items-center bg-surface rounded-lg">
            <button
              onClick={() => updateAirportData({
                children: Math.max(0, (airportData.children || 0) - 1)
              })}
              className="px-4 py-3 text-primary hover:text-primary/70 transition-colors"
              disabled={airportData.children <= 0}
            >
              <MinusIcon className="w-5 h-5" />
            </button>
            <div className="flex-1 text-center">
              <span className="text-theme-primary font-medium">{airportData.children}</span>
            </div>
            <button
              onClick={() => updateAirportData({
                children: (airportData.children || 0) + 1
              })}
              className="px-4 py-3 text-primary hover:text-primary/70 transition-colors"
            >
              <PlusIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Car Seats Toggle */}
        {airportData.children > 0 && (
          <div className="col-span-2">
            <h3 className="text-theme-primary text-base mb-2">Car Seats</h3>
            <div className="h-[46px] flex items-center justify-between bg-surface rounded-lg px-4">
              <ToggleButton
                checked={airportData.needCarSeats}
                onChange={(checked) => updateAirportData({ needCarSeats: checked })}
              />
            </div>
          </div>
        )}
      </div>

      {/* Car Seats Section */}
      {airportData.needCarSeats && (
        <div className="grid grid-cols-3 gap-4">
          {/* Infant Seats */}
          <div className="bg-surface rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-base text-theme-primary font-medium">Infant Seats</h4>
                <p className="text-xs text-theme-muted">(0-1 year)</p>
              </div>
            </div>
            <div className="flex items-center justify-between mt-4">
              <button
                onClick={() => handleCarSeatsChange('infantSeats', Math.max(0, airportData.infantSeats - 1))}
                className="w-10 h-10 rounded-full bg-surface-dark text-theme-primary flex items-center justify-center text-xl hover:bg-surface-light transition-colors"
              >
                -
              </button>
              <span className="text-base font-medium text-theme-primary">{airportData.infantSeats}</span>
              <button
                onClick={() => handleCarSeatsChange('infantSeats', airportData.infantSeats + 1)}
                className="w-10 h-10 rounded-full bg-surface-dark text-theme-primary flex items-center justify-center text-xl hover:bg-surface-light transition-colors"
              >
                <PlusIcon className="w-5 h-5 text-primary" />
              </button>
            </div>
          </div>

          {/* Toddler Seats */}
          <div className="bg-surface rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-theme-primary font-medium">Toddler Seats</h4>
                <p className="text-xs text-theme-muted">(1-3 years)</p>
              </div>
            </div>
            <div className="flex items-center justify-between mt-4">
              <button
                onClick={() => handleCarSeatsChange('toddlerSeats', Math.max(0, airportData.toddlerSeats - 1))}
                className="w-10 h-10 rounded-full bg-surface-dark text-theme-primary flex items-center justify-center text-xl hover:bg-surface-light transition-colors"
              >
                -
              </button>
              <span className="text-base font-medium text-theme-primary">{airportData.toddlerSeats}</span>
              <button
                onClick={() => handleCarSeatsChange('toddlerSeats', airportData.toddlerSeats + 1)}
                className="w-10 h-10 rounded-full bg-surface-dark text-theme-primary flex items-center justify-center text-xl hover:bg-surface-light transition-colors"
              >
                <PlusIcon className="w-5 h-5 text-primary" />
              </button>
            </div>
          </div>

          {/* Booster Seats */}
          <div className="bg-surface rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-theme-primary font-medium">Booster Seats</h4>
                <p className="text-xs text-theme-muted">(4-7 years)</p>
              </div>
            </div>
            <div className="flex items-center justify-between mt-4">
              <button
                onClick={() => handleCarSeatsChange('boosterSeats', Math.max(0, airportData.boosterSeats - 1))}
                className="w-10 h-10 rounded-full bg-surface-dark text-theme-primary flex items-center justify-center text-xl hover:bg-surface-light transition-colors"
              >
                -
              </button>
              <span className="text-base font-medium text-theme-primary">{airportData.boosterSeats}</span>
              <button
                onClick={() => handleCarSeatsChange('boosterSeats', airportData.boosterSeats + 1)}
                className="w-10 h-10 rounded-full bg-surface-dark text-theme-primary flex items-center justify-center text-xl hover:bg-surface-light transition-colors"
              >
                <PlusIcon className="w-5 h-5 text-primary" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Next Button */}
      <div className="flex justify-end pt-6">
        <button
          onClick={handleNextStep}
          className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-light transition-colors"
        >
          Next: Select Vehicle
        </button>
      </div>
    </div>
  );
};
