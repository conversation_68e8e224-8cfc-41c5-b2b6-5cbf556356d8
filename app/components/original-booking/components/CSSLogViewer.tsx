import React, { useState, useEffect } from 'react';
import { cssLogger } from '../utils/cssLogger';

interface CSSLogViewerProps {
  filter?: {
    componentId?: string;
    property?: string;
    selector?: string;
    timeRange?: [number, number];
    source?: 'inline' | 'class' | 'stylesheet' | 'theme';
  };
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  width?: string;
  height?: string;
}

/**
 * CSSLogViewer - A component to display and analyze CSS logs
 * 
 * This provides a debug interface for examining CSS usage and performance.
 */
export const CSSLogViewer: React.FC<CSSLogViewerProps> = ({ 
  filter,
  position = 'bottom-right',
  width = '80%',
  height = '80vh'
}) => {
  const [logs, setLogs] = useState<any[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const [activeTab, setActiveTab] = useState<'logs' | 'analysis'>('logs');
  const [analysis, setAnalysis] = useState<Record<string, any>>({});

  // Position styling
  const getPositionStyles = () => {
    switch (position) {
      case 'top-right':
        return { top: '20px', right: '20px' };
      case 'top-left':
        return { top: '20px', left: '20px' };
      case 'bottom-left':
        return { bottom: '20px', left: '20px' };
      case 'bottom-right':
      default:
        return { bottom: '20px', right: '20px' };
    }
  };

  // Update logs periodically
  useEffect(() => {
    if (!isVisible) return;

    const updateLogs = () => {
      setLogs(cssLogger.getLogs(filter));
    };

    // Update logs immediately and then every 2 seconds
    updateLogs();
    const interval = setInterval(updateLogs, 2000);
    
    return () => clearInterval(interval);
  }, [isVisible, filter]);

  // Run analysis when tab changes to analysis
  useEffect(() => {
    if (activeTab === 'analysis') {
      const results = cssLogger.analyzePerformance();
      setAnalysis(results);
    }
  }, [activeTab]);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        style={{
          position: 'fixed',
          ...getPositionStyles(),
          zIndex: 9999,
          padding: '8px 16px',
          background: '#1a1a1a',
          color: '#fff',
          border: '1px solid #333',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '14px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.2)'
        }}
      >
        CSS Logs ({logs.length})
      </button>
    );
  }

  return (
    <div
      style={{
        position: 'fixed',
        ...getPositionStyles(),
        width,
        maxWidth: '90vw',
        maxHeight: height,
        background: '#1a1a1a',
        color: '#ddd',
        padding: '0',
        zIndex: 9999,
        borderRadius: '6px',
        boxShadow: '0 0 20px rgba(0,0,0,0.5)',
        fontFamily: 'monospace',
        fontSize: '12px',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}
    >
      {/* Header bar */}
      <div 
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          padding: '10px 15px',
          borderBottom: '1px solid #333',
          background: '#222'
        }}
      >
        <h3 style={{ margin: 0 }}>CSS Logger</h3>
        <button
          onClick={() => setIsVisible(false)}
          style={{
            background: 'none',
            border: 'none',
            color: '#999',
            cursor: 'pointer',
            fontSize: '20px',
            lineHeight: '1',
            padding: 0
          }}
        >
          ×
        </button>
      </div>

      {/* Tab navigation */}
      <div 
        style={{
          display: 'flex',
          borderBottom: '1px solid #333'
        }}
      >
        <button
          onClick={() => setActiveTab('logs')}
          style={{
            background: activeTab === 'logs' ? '#333' : 'transparent',
            border: 'none',
            padding: '10px 15px',
            color: activeTab === 'logs' ? '#fff' : '#999',
            cursor: 'pointer',
            flex: 1,
            textAlign: 'center',
            borderRight: '1px solid #333'
          }}
        >
          Logs ({logs.length})
        </button>
        <button
          onClick={() => setActiveTab('analysis')}
          style={{
            background: activeTab === 'analysis' ? '#333' : 'transparent',
            border: 'none',
            padding: '10px 15px',
            color: activeTab === 'analysis' ? '#fff' : '#999',
            cursor: 'pointer',
            flex: 1,
            textAlign: 'center'
          }}
        >
          Analysis
        </button>
      </div>

      {/* Action buttons */}
      <div 
        style={{
          display: 'flex',
          padding: '10px 15px',
          gap: '10px',
          background: '#222', 
          borderBottom: '1px solid #333'
        }}
      >
        <button
          onClick={() => cssLogger.clear()}
          style={{
            background: '#444',
            border: 'none',
            padding: '6px 12px',
            borderRadius: '3px',
            color: 'white',
            cursor: 'pointer'
          }}
        >
          Clear Logs
        </button>

        <button
          onClick={() => {
            // Run analysis and switch to analysis tab
            const results = cssLogger.analyzePerformance();
            setAnalysis(results);
            setActiveTab('analysis');
          }}
          style={{
            background: '#444',
            border: 'none',
            padding: '6px 12px',
            borderRadius: '3px',
            color: 'white',
            cursor: 'pointer'
          }}
        >
          Analyze
        </button>

        <button
          onClick={() => {
            // Export logs to JSON file
            const data = cssLogger.exportLogs();
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `css-logs-${new Date().toISOString().slice(0, 10)}.json`;
            document.body.appendChild(a);
            a.click();
            
            // Clean up
            setTimeout(() => {
              document.body.removeChild(a);
              URL.revokeObjectURL(url);
            }, 100);
          }}
          style={{
            background: '#444',
            border: 'none',
            padding: '6px 12px',
            borderRadius: '3px',
            color: 'white',
            cursor: 'pointer'
          }}
        >
          Export
        </button>
      </div>

      {/* Tab content */}
      <div style={{ overflow: 'auto', flex: 1, padding: '15px' }}>
        {activeTab === 'logs' ? (
          // Logs view
          <>
            {logs.length === 0 ? (
              <div style={{ padding: '20px', textAlign: 'center', color: '#666' }}>
                No CSS logs captured yet.<br />
                Add the useCSSLogger hook to your components to start logging.
              </div>
            ) : (
              logs.map((log, index) => (
                <div 
                  key={index}
                  style={{
                    borderBottom: '1px solid #333',
                    padding: '10px 0',
                    marginBottom: '10px',
                    fontSize: '11px'
                  }}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <strong style={{ color: '#6cf' }}>{log.componentId}</strong>
                    <span style={{ color: '#999' }}>
                      {new Date(log.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  
                  <div style={{ color: '#aaa', marginTop: '4px' }}>
                    {log.selector} ({log.source})
                    {log.applyTime && (
                      <span style={{ 
                        color: log.applyTime > 5 ? '#f66' : '#6f6',
                        marginLeft: '8px'
                      }}>
                        {log.applyTime.toFixed(2)} ms
                      </span>
                    )}
                  </div>
                  
                  {log.properties && Object.keys(log.properties).length > 0 && (
                    <details style={{ marginTop: '8px' }}>
                      <summary style={{ cursor: 'pointer', color: '#999' }}>
                        {Object.keys(log.properties).length} properties
                      </summary>
                      <div style={{ 
                        padding: '8px', 
                        background: '#222', 
                        marginTop: '8px',
                        maxHeight: '200px',
                        overflow: 'auto',
                        borderRadius: '4px'
                      }}>
                        {Object.entries(log.properties).map(([prop, value], idx) => (
                          <div key={idx} style={{ fontSize: '10px', marginBottom: '2px' }}>
                            <span style={{ color: '#6ac' }}>{prop}:</span>{' '}
                            <span>{String(value).substring(0, 100)}</span>
                            {String(value).length > 100 && '...'}
                          </div>
                        ))}
                      </div>
                    </details>
                  )}
                </div>
              ))
            )}
          </>
        ) : (
          // Analysis view
          <>
            {Object.keys(analysis).length === 0 ? (
              <div style={{ textAlign: 'center', padding: '20px', color: '#666' }}>
                Run analysis to see CSS performance metrics
              </div>
            ) : (
              <div>
                {/* Overview statistics */}
                <h4 style={{ margin: '0 0 15px 0', color: '#6cf' }}>Performance Summary</h4>
                <div style={{ 
                  display: 'grid', 
                  gridTemplateColumns: 'repeat(2, 1fr)',
                  gap: '10px',
                  background: '#222',
                  padding: '10px',
                  borderRadius: '4px',
                  marginBottom: '20px'
                }}>
                  {Object.entries(analysis).filter(([key, value]) => 
                    typeof value !== 'object' && key !== 'topChangedProperties' && key !== 'topChangedComponents'
                  ).map(([key, value], idx) => (
                    <div key={idx}>
                      <span style={{ color: '#999' }}>
                        {key.replace(/([A-Z])/g, ' $1')
                          .replace(/^./, str => str.toUpperCase())
                          .replace(/([A-Z])/g, match => ` ${match.toLowerCase()}`)}:
                      </span>{' '}
                      <strong>{value}</strong>
                    </div>
                  ))}
                </div>

                {/* Top changed components */}
                {analysis.topChangedComponents && (
                  <div style={{ marginBottom: '20px' }}>
                    <h4 style={{ margin: '0 0 10px 0', color: '#6cf' }}>Top Changed Components</h4>
                    <div style={{ 
                      background: '#222',
                      padding: '10px',
                      borderRadius: '4px'
                    }}>
                      {analysis.topChangedComponents.map((item: any, idx: number) => (
                        <div key={idx} style={{ 
                          display: 'flex',
                          justifyContent: 'space-between',
                          borderBottom: idx < analysis.topChangedComponents.length - 1 ? '1px solid #333' : 'none',
                          padding: '6px 0'
                        }}>
                          <div style={{ fontWeight: 'bold' }}>{item.component}</div>
                          <div>{item.count} changes</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Top changed properties */}
                {analysis.topChangedProperties && (
                  <div>
                    <h4 style={{ margin: '0 0 10px 0', color: '#6cf' }}>Most Changed CSS Properties</h4>
                    <div style={{ 
                      background: '#222',
                      padding: '10px',
                      borderRadius: '4px'
                    }}>
                      {analysis.topChangedProperties.map((item: any, idx: number) => (
                        <div key={idx} style={{ 
                          display: 'flex',
                          justifyContent: 'space-between',
                          borderBottom: idx < analysis.topChangedProperties.length - 1 ? '1px solid #333' : 'none',
                          padding: '6px 0'
                        }}>
                          <div style={{ fontWeight: 'bold', color: '#6ac' }}>{item.property}</div>
                          <div>{item.count} changes</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}; 