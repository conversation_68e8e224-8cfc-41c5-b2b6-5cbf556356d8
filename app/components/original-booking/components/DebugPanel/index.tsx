import React, { useEffect, useState } from 'react';
import { bookingDebugger } from '../../utils/debugLogger';
import { ClipboardIcon } from '@heroicons/react/24/outline';
import type { LogEntry } from '../../utils/debugLogger';

export const DebugPanel: React.FC = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [logs, setLogs] = useState<LogEntry[]>(bookingDebugger.getLogHistory());
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  useEffect(() => {
    const unsubscribe = bookingDebugger.subscribe((newLog) => {
      setLogs((prevLogs) => [...prevLogs, newLog] as LogEntry[]);
    });
    return () => {
      unsubscribe();
    };
  }, []);

  const copyLog = async (entry: any, index: number) => {
    const logText = `[${entry.level.toUpperCase()}] ${entry.category}: ${entry.message}\n${entry.data ? JSON.stringify(entry.data, null, 2) : ''}`;
    await navigator.clipboard.writeText(logText);
    setCopiedIndex(index);
    setTimeout(() => setCopiedIndex(null), 2000);
  };

  return (
    <div className="fixed bottom-4 right-4" style={{ zIndex: 99999 }}>
      <button
        onClick={() => setIsCollapsed(!isCollapsed)}
        className="absolute -top-8 right-0 bg-[#8B5E34] text-white px-4 py-1 rounded-t-lg text-sm font-medium shadow-lg"
      >
        {isCollapsed ? 'Show Debug' : 'Hide Debug'}
      </button>

      {!isCollapsed && (
        <div className="w-[500px] h-[600px] bg-black/95 text-white rounded-lg border border-[#8B5E34] shadow-2xl flex flex-col">
          <div className="p-3 border-b border-[#8B5E34] flex items-center justify-between sticky top-0 bg-black/95">
            <h3 className="text-[#8B5E34] font-bold">Debug Console</h3>
            <div className="space-x-2">
              <button
                onClick={() => bookingDebugger.log('info', 'Test', 'Test log message')}
                className="px-2 py-1 text-xs bg-[#8B5E34] text-white rounded hover:bg-[#8B5E34]/80"
              >
                Test
              </button>
              <button
                onClick={() => bookingDebugger.clearLogs()}
                className="px-2 py-1 text-xs bg-gray-700 text-white rounded hover:bg-gray-600"
              >
                Clear
              </button>
            </div>
          </div>

          <div className="flex-1 overflow-auto p-3">
            {logs.length === 0 ? (
              <div className="text-gray-500 text-center py-4">No logs yet</div>
            ) : (
              <div className="space-y-2">
                {logs.map((entry, index) => (
                  <div
                    key={`${entry.timestamp}-${index}`}
                    className="text-xs border-b border-gray-800/50 pb-2 group hover:bg-white/5 rounded p-2"
                  >
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex items-start gap-2 text-gray-400 flex-1">
                        <span className="whitespace-nowrap">{new Date(entry.timestamp).toLocaleTimeString()}</span>
                        <span className={`font-medium ${entry.level === 'error' ? 'text-red-400' : 'text-[#8B5E34]'}`}>
                          [{entry.category}]
                        </span>
                        <span className="text-white/90 break-words min-w-0 flex-1">{entry.message}</span>
                      </div>
                      <button
                        onClick={() => copyLog(entry, index)}
                        className="opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0"
                        title="Copy log"
                      >
                        {copiedIndex === index ? (
                          <span className="text-green-500 text-xs">Copied!</span>
                        ) : (
                          <ClipboardIcon className="w-4 h-4 text-gray-400 hover:text-white" />
                        )}
                      </button>
                    </div>
                    {entry.data && (
                      <pre className="mt-2 pl-4 text-gray-500 text-[10px] bg-black/30 p-2 rounded overflow-x-auto whitespace-pre-wrap">
                        {JSON.stringify(entry.data, null, 2)}
                      </pre>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}; 