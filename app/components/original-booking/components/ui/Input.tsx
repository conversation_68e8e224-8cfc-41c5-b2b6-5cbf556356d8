import React from 'react';
import { TextInput as MantineTextInput, Textarea as MantineTextarea, Box, TextInputProps, TextareaProps } from '@mantine/core';
import { cn } from '../../../lib/utils';

interface BaseProps {
  className?: string;
  icon?: React.ReactNode;
  label?: string;
  error?: string;
}

interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>, BaseProps {
  multiline?: false;
  type?: React.InputHTMLAttributes<HTMLInputElement>['type'];
}

interface TextAreaProps extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'size'>, BaseProps {
  multiline: true;
  type?: never;
}

type Props = InputProps | TextAreaProps;

export const Input = React.forwardRef<HTMLInputElement | HTMLTextAreaElement, Props>(
  ({ className, type = 'text', multiline, icon, label, error, ...props }, ref) => {
    const darkStyles = {
      input: {
        backgroundColor: 'var(--mantine-color-dark-6)',
        borderColor: 'var(--mantine-color-dark-4)',
        color: 'var(--mantine-color-white)'
      },
      label: {
        color: 'var(--mantine-color-gray-4)'
      }
    };

    // Filter out HTML size attribute to avoid conflicts with Mantine's size prop
    const { size: _, ...restProps } = props as any;

    if (multiline) {
      return (
        <MantineTextarea
          ref={ref as React.Ref<HTMLTextAreaElement>}
          className={className}
          label={label}
          error={error}
          styles={darkStyles}
          leftSection={icon}
          autosize
          minRows={3}
          {...restProps}
        />
      );
    }

    return (
      <MantineTextInput
        ref={ref as React.Ref<HTMLInputElement>}
        className={className}
        type={type}
        label={label}
        error={error}
        styles={darkStyles}
        leftSection={icon}
        {...restProps}
      />
    );
  }
);

Input.displayName = 'Input';
