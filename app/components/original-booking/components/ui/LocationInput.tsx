import { forwardRef, useEffect, useRef, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import { Input } from './Input';
import { MapPinIcon } from '@heroicons/react/24/outline';
import { cn } from '../../../lib/utils';

type LocationInputProps = {
  label: string;
  value: string;
  onChange: (location: { address: string; coordinates: [number, number] }) => void;
  error?: string;
};

export const LocationInput = ({
  label,
  value,
  onChange,
  error,
}: LocationInputProps) => {
  const [inputValue, setInputValue] = useState(value);

  useEffect(() => {
    setInputValue(value);
  }, [value]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    onChange({
      address: value,
      coordinates: [0, 0] // We'll implement real geocoding later
    });
  };

  return (
    <Input
      label={label}
      value={inputValue}
      onChange={handleInputChange}
      icon={<MapPinIcon className="w-5 h-5" />}
      error={error}
    />
  );
};
