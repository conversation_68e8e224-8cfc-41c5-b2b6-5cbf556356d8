import { config } from '../config/env';
import { bookingDebugger } from '../utils/debugLogger';

// Add version tracking
const MAPBOX_API_VERSION = "1.0.1";
console.log(`[Mapbox API] Loading Mapbox API service version ${MAPBOX_API_VERSION}`);

interface MapboxReverseGeocodingResponse {
  type: string;
  query: number[];
  features: Array<{
    id: string;
    type: string;
    place_type: string[];
    relevance: number;
    properties: {
      accuracy?: string;
      address?: string;
      category?: string;
      maki?: string;
      wikidata?: string;
    };
    text: string;
    place_name: string;
    center: number[];
    geometry: {
      type: string;
      coordinates: number[];
    };
    address?: string;
    context: Array<{
      id: string;
      text: string;
      wikidata?: string;
      short_code?: string;
    }>;
  }>;
  attribution: string;
}

/**
 * Get city name from coordinates using Mapbox API
 * @version 2.0.0 - Fixed coordinate validation and auto-correction
 * @param coordinates [longitude, latitude] array
 * @returns City name or null if not found
 */
export const getCityFromCoordinates = async (coordinates: [number, number]): Promise<string | null> => {
  console.log('[Mapbox API] Version 2.0.0 - getCityFromCoordinates called with:', coordinates);
  
  try {
    // Validate coordinates
    if (!coordinates || !Array.isArray(coordinates) || coordinates.length !== 2) {
      console.error('[Mapbox API] Invalid coordinates format:', coordinates);
      return null;
    }
    
    // Extract longitude and latitude
    let [longitude, latitude] = coordinates;
    
    // Check if coordinates might be in the wrong order (latitude, longitude)
    // Longitude should be between -180 and 180, latitude between -90 and 90
    if (Math.abs(longitude) > 90 && Math.abs(latitude) <= 180) {
      // Coordinates are likely in the wrong order, swap them
      console.log('[Mapbox API] Coordinates appear to be in wrong order, auto-correcting');
      [longitude, latitude] = [latitude, longitude];
    }
    
    // Final validation check
    if (Math.abs(longitude) > 180 || Math.abs(latitude) > 90) {
      console.error('[Mapbox API] Coordinates out of valid range after correction:', { longitude, latitude });
      return null;
    }
    
    console.log('[Mapbox API] Using coordinates:', { longitude, latitude });
    
    // Get Mapbox API key from environment
    const mapboxApiKey = process.env.NEXT_PUBLIC_MAPBOX_TOKEN;
    
    if (!mapboxApiKey) {
      console.error('[Mapbox API] API key not found');
      return null;
    }
    
    // Construct the URL for the Mapbox API
    const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${mapboxApiKey}&types=place`;
    
    console.log('[Mapbox API] Making API request...');
    
    // Make the request
    const response = await fetch(url);
    
    if (!response.ok) {
      console.error('[Mapbox API] API request failed:', { status: response.status, statusText: response.statusText });
      return null;
    }
    
    const data = await response.json();
    
    // Check if we have features in the response
    if (!data.features || data.features.length === 0) {
      console.log('[Mapbox API] No features found in response');
      return null;
    }
    
    // Find the place feature (city)
    const placeFeature = data.features.find((feature: any) => 
      feature.place_type && feature.place_type.includes('place')
    );
    
    if (placeFeature) {
      const cityName = placeFeature.text;
      console.log('[Mapbox API] City found:', cityName);
      return cityName;
    }
    
    // If no place feature found, try to extract from context
    const firstFeature = data.features[0];
    if (firstFeature.context) {
      const placeContext = firstFeature.context.find((ctx: any) => 
        ctx.id && ctx.id.startsWith('place.')
      );
      
      if (placeContext) {
        const cityName = placeContext.text;
        console.log('[Mapbox API] City found from context:', cityName);
        return cityName;
      }
    }
    
    console.log('[Mapbox API] No city found in response');
    return null;
  } catch (error) {
    console.error('[Mapbox API] Error getting city from coordinates:', error);
    return null;
  }
};

/**
 * Format a date for display in email subjects
 * @param dateString ISO date string
 * @returns Formatted date string (e.g., "Feb 28, 2025")
 */
export const formatDateForDisplay = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return dateString; // Return original if invalid
    }
    
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  } catch (error) {
    console.error('[Mapbox API] Error formatting date:', error);
    return dateString;
  }
}; 