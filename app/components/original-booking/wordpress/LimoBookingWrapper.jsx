/**
 * WordPress Plugin Integration Wrapper Component
 * 
 * This component serves as the main entry point for the WordPress plugin integration.
 * It initializes theme settings from WordPress configuration and provides proper CSS namespacing.
 */

import React, { useEffect, useState } from 'react';
import { BookingForm } from '../components/BookingForm';

// Import theme store if available
import { useThemeStore } from '../store/themeStore';

/**
 * LimoBookingWrapper component for WordPress integration
 * 
 * @param {Object} props - Component props
 * @param {string} props.theme - Theme name ('dark' or 'light')
 * @param {Object} props.config - WordPress configuration
 */
const LimoBookingWrapper = ({ theme = 'dark', config, ...props }) => {
  const [initialized, setInitialized] = useState(false);
  const [error, setError] = useState(null);
  
  // Get theme store functions if available
  const themeStore = useThemeStore ? useThemeStore() : null;
  
  // Initialize from WordPress configuration
  useEffect(() => {
    try {
      // Check for global WordPress configuration
      const wpConfig = window.limoBookingConfig || config;
      
      if (wpConfig) {
        console.log('[LimoBookingWrapper] Initializing with WordPress config:', wpConfig);
        
        // Initialize theme from WordPress if theme store is available
        if (themeStore && themeStore.initializeFromWordPress) {
          themeStore.initializeFromWordPress(wpConfig);
        }
        
        setInitialized(true);
      } else {
        console.log('[LimoBookingWrapper] No WordPress config found, using default theme:', theme);
        // Set theme manually if no WordPress config is available
        if (themeStore && themeStore.setColorScheme) {
          themeStore.setColorScheme(theme);
        }
        
        setInitialized(true);
      }
    } catch (err) {
      console.error('[LimoBookingWrapper] Error initializing:', err);
      setError('Failed to initialize booking form. Please try again.');
    }
  }, []);
  
  // Show loading state
  if (!initialized && !error) {
    return (
      <div className="limo-booking-plugin limo-loading" data-theme={theme}>
        <div className="limo-loading-spinner"></div>
        <p className="limo-text-muted">Loading booking form...</p>
      </div>
    );
  }
  
  // Show error state
  if (error) {
    return (
      <div className="limo-booking-plugin limo-error" data-theme={theme}>
        <p className="limo-text-error">{error}</p>
        <button className="limo-button limo-button-primary" onClick={() => window.location.reload()}>
          Retry
        </button>
      </div>
    );
  }
  
  return (
    <div className="limo-booking-plugin" data-theme={theme || 'dark'}>
      {/* Main booking form with WordPress configuration */}
      <BookingForm {...props} />
    </div>
  );
};

export default LimoBookingWrapper;

/**
 * Usage in WordPress:
 * 
 * 1. Import this component in your WordPress integration
 * 2. Use it to wrap the booking form
 * 3. Pass the theme prop to switch between light and dark themes
 * 
 * Example:
 * <LimoBookingWrapper theme="light" />
 */