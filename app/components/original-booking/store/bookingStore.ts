import { create } from 'zustand';
import { scrollFormToTop } from '../../lib/utils';
import { type BookingType } from '../../types/booking';
import { BookingData } from '../components/BookingForm/types';

interface Location {
  address: string;
  coordinates: [number, number];
}

interface Stop {
  id: string;
  location: Location | null;
  isMoving?: boolean;
}

interface BaseBookingData {
  pickupLocation: Location | null;
  pickupDate: string | null;
  pickupTime: string | null;
  adults: number;
  children: number;
  needCarSeats: boolean;
  infantSeats: number;
  toddlerSeats: number;
  boosterSeats: number;
  selectedVehicle: string | null;
}

interface PointToPointData extends BaseBookingData {
  dropoffLocation: Location | null;
  stops: Stop[];
  isRoundTrip: boolean;
  returnDate: string | null;
}

interface HourlyData extends BaseBookingData {
  hours: number;
  days: number;
  stops: Stop[];
  dropoffLocation: Location | null;
}

interface CarSeats {
  infantSeats?: number;
  toddlerSeats?: number;
  boosterSeats?: number;
}

export interface FlightDetails {
  airline?: string;
  flightNumber?: string;
  departureTime?: string;
  arrivalTime?: string;
  status?: string;
}

export interface AirportData extends BaseBookingData {
  dropoffLocation: Location;
  departureFlight?: string;
  departureDate?: string;
  departureTime?: string;
  departureFlightData?: FlightDetails;
  returnFlight?: string;
  returnDate?: string;
  returnTime?: string;
  returnFlightData?: FlightDetails;
  isRoundTrip?: boolean;
  flightInfo?: {
    airline?: string;
    flightNumber?: string;
  };
  carSeats?: CarSeats;
}

interface DayData {
  id: string;
  pickupLocation: Location | null;
  dropoffLocation: Location | null;
  pickupDate: string | null;
  pickupTime: string | null;
  stops: Stop[];
  isCollapsed: boolean;
}

interface MultiDayData extends BaseBookingData {
  days: DayData[];
  totalDays: number;
  startDate: string | null;
  startTime: string | null;
}

export type { BookingType };

export interface BookingState {
  activeTab: BookingType;
  currentStep: number;
  formType: BookingType;
  selectedVehicle: string | null;
  pointToPointData: PointToPointData;
  hourlyData: HourlyData;
  airportData: AirportData;
  multiDayData: MultiDayData;
  bookingData: BookingData;
}

interface BookingActions {
  setActiveTab: (tab: BookingType) => void;
  setStep: (step: number) => void;
  setFormType: (type: BookingType) => void;
  setSelectedVehicle: (vehicle: string) => void;
  updatePointToPointData: (data: Partial<PointToPointData>) => void;
  updateHourlyData: (data: Partial<HourlyData>) => void;
  updateAirportData: (data: Partial<AirportData>) => void;
  updateMultiDayData: (data: Partial<MultiDayData>) => void;
  resetForm: () => void;
  setBookingData: (data: Partial<BookingData>) => void;
  reset: () => void;
}

const initialState: BookingState = {
  activeTab: 'point-to-point',
  currentStep: 1,
  formType: 'point-to-point',
  selectedVehicle: null,
  pointToPointData: {
    pickupLocation: null,
    dropoffLocation: null,
    pickupDate: null,
    pickupTime: null,
    adults: 1,
    children: 0,
    stops: [],
    selectedVehicle: null,
    infantSeats: 0,
    toddlerSeats: 0,
    boosterSeats: 0,
    needCarSeats: false,
    isRoundTrip: false,
    returnDate: null
  },
  hourlyData: {
    pickupLocation: null,
    pickupDate: null,
    pickupTime: null,
    hours: 4,
    days: 1,
    adults: 1,
    children: 0,
    stops: [],
    selectedVehicle: null,
    infantSeats: 0,
    toddlerSeats: 0,
    boosterSeats: 0,
    needCarSeats: false,
    dropoffLocation: null
  },
  airportData: {
    pickupLocation: null,
    dropoffLocation: null,
    pickupDate: null,
    pickupTime: null,
    adults: 1,
    children: 0,
    selectedVehicle: null,
    infantSeats: 0,
    toddlerSeats: 0,
    boosterSeats: 0,
    needCarSeats: false,
    departureFlight: null,
    departureDate: null,
    departureTime: null,
    returnFlight: null,
    returnDate: null,
    returnTime: null,
    isRoundTrip: false,
    carSeats: {
      infantSeats: 0,
      toddlerSeats: 0,
      boosterSeats: 0
    },
    departureFlightData: null,
    returnFlightData: null
  },
  multiDayData: {
    days: [{
      id: '1',
      pickupLocation: null,
      dropoffLocation: null,
      pickupDate: null,
      pickupTime: null,
      stops: [],
      isCollapsed: false
    }],
    totalDays: 1,
    adults: 1,
    children: 0,
    selectedVehicle: null,
    infantSeats: 0,
    toddlerSeats: 0,
    boosterSeats: 0,
    needCarSeats: false,
    startDate: null,
    startTime: null,
    pickupLocation: null,
    pickupDate: null,
    pickupTime: null
  },
  bookingData: {
    pickupLocation: '',
    dropoffLocation: '',
    pickupDate: '',
    pickupTime: '',
    vehicleId: '',
    name: '',
    email: '',
    phone: '',
    specialInstructions: ''
  }
};

export const useBookingStore = create<BookingState & BookingActions>((set) => ({
  ...initialState,
  setActiveTab: (tab: BookingType) => 
    set({ activeTab: tab, formType: tab, currentStep: 1 }),
  setStep: (step: number) => {
    setTimeout(() => {
      scrollFormToTop();
    }, 10);
    
    return set({ currentStep: step });
  },
  setFormType: (type: BookingType) => 
    set({ formType: type, activeTab: type, currentStep: 1 }),
  setSelectedVehicle: (vehicle: string) => set({ selectedVehicle: vehicle }),
  updatePointToPointData: (data) => 
    set((state) => ({
      pointToPointData: {
        ...state.pointToPointData,
        ...data
      }
    })),
  updateHourlyData: (data) => 
    set((state) => ({
      hourlyData: {
        ...state.hourlyData,
        ...data
      }
    })),
  updateAirportData: (data) => 
    set((state) => ({
      airportData: {
        ...state.airportData,
        ...data
      }
    })),
  updateMultiDayData: (data) => 
    set((state) => ({
      multiDayData: {
        ...state.multiDayData,
        ...data
      }
    })),
  resetForm: () => set(initialState),
  setBookingData: (data) => set((state) => ({
    bookingData: { ...state.bookingData, ...data }
  })),
  reset: () => set(initialState)
}));
