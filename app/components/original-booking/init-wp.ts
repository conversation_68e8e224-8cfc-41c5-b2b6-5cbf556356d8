import React from 'react';
import { createRoot } from 'react-dom/client';
import { BookingForm } from './components/BookingForm';
import { themeService } from './services/themeService';

declare global {
    interface Window {
        limoBookingSettings: {
            apiKeys: {
                here: string;
                mapbox: string;
                resend: string;
                salesmate: string;
            };
            theme: {
                primaryColor: string;
                backgroundColor: string;
                surfaceColor: string;
                textPrimaryColor: string;
            };
            vehicleImages: Record<string, string>;
            ajaxUrl: string;
            nonce: string;
        };
    }
}

const init = () => {
    const container = document.getElementById('limo-booking-form');
    if (!container) return;

    // Initialize theme service with WordPress settings
    themeService.saveTheme({
        color: {
            base: window.limoBookingSettings.theme.primaryColor,
            light: window.limoBookingSettings.theme.primaryColor,
            dark: window.limoBookingSettings.theme.primaryColor
        },
        background: {
            main: window.limoBookingSettings.theme.backgroundColor,
            surface: window.limoBookingSettings.theme.surfaceColor,
            surfaceDark: window.limoBookingSettings.theme.surfaceColor
        },
        font: {
            primary: window.limoBookingSettings.theme.textPrimaryColor,
            secondary: window.limoBookingSettings.theme.textPrimaryColor,
            muted: window.limoBookingSettings.theme.textPrimaryColor,
            disabled: window.limoBookingSettings.theme.textPrimaryColor
        }
    });

    const root = createRoot(container);
    root.render(
        React.createElement(React.StrictMode, null,
            React.createElement(BookingForm, {
                apiKeys: window.limoBookingSettings.apiKeys,
                nonce: window.limoBookingSettings.nonce,
                ajaxUrl: window.limoBookingSettings.ajaxUrl,
                vehicleImages: window.limoBookingSettings.vehicleImages
            })
        )
    );
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
} else {
    init();
} 