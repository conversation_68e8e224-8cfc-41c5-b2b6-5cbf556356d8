"use client"

import { useState, useEffect } from "react"
import { use<PERSON>outer, useSearchParams } from "next/navigation"
import { getSupabaseClient } from '@/lib/supabase'
import { SupabaseClient } from '@supabase/supabase-js'
import { Input } from "@/app/components/ui/input"
import { Button } from "@/app/components/ui/button"
import { Label } from "@/app/components/ui/label"
import { Loader2 } from "lucide-react"
import { Alert, AlertDescription } from "@/app/components/ui/alert"
import Link from "next/link"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { toast } from 'sonner'
import AuthErrorHandler from '../features/auth/auth-error-handler'

interface AuthError extends Error {
  message: string;
}

interface DashboardRoutes {
  event_manager: string
  admin: string
  affiliate: string
  customer: string
  [key: string]: string
}

interface TestUser {
  role: string
  email: string
  password: string
}

interface AuthFormProps {
  testUsers?: TestUser[]
}

const validRoles = ['ADMIN', 'CLIENT', 'AFFILIATE', 'CLIENT_COORDINATOR', 'AFFILIATE_DISPATCHER', 'PASSENGER', 'DRIVER'] as const;
type UserRole = typeof validRoles[number];

export function AuthForm({ testUsers }: AuthFormProps) {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isRegistering, setIsRegistering] = useState(true)
  const [selectedRole, setSelectedRole] = useState<UserRole>('CLIENT')
  const [supabase, setSupabase] = useState<SupabaseClient | null>(null);

  useEffect(() => {
    const client = getSupabaseClient();
    setSupabase(client);
  }, []);

  useEffect(() => {
    const errorParam = searchParams.get("error");
    const errorDescriptionParam = searchParams.get("error_description");

    if (errorParam) {
      console.error('AuthForm URL Error:', errorParam, errorDescriptionParam);
      toast.error(errorDescriptionParam || decodeURIComponent(errorParam) || "An unknown authentication error occurred.");
      router.replace(window.location.pathname, { scroll: false }); 
    }
  }, [searchParams, router]);

  const fillCredentials = (user: TestUser) => {
    setEmail(user.email)
    setPassword(user.password)
    setConfirmPassword(user.password)
    setError(null)
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    if (!supabase) {
      toast.error('Authentication service is not available. Please try again later.');
      setError('Authentication service is not available.');
      setLoading(false);
      return;
    }

    if (isRegistering && password !== confirmPassword) {
      toast.error('Passwords do not match');
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    try {
      let response;
      if (isRegistering) {
        console.log('AuthForm: Attempting to register user:', email, 'with role:', selectedRole);
        
        // Map UI role "ADMIN" to backend role "SUPER_ADMIN"
        const roleToRegister = selectedRole === 'ADMIN' ? 'SUPER_ADMIN' : selectedRole;

        response = await supabase!.auth.signUp({
          email,
          password,
          options: {
            emailRedirectTo: `${typeof window !== 'undefined' ? window.location.origin : ''}/auth/callback`,
            data: { 
              roles: [roleToRegister], // Ensure 'roles' is an array
              email_verified: true // Auto-verify email for development
            }
          },
        })
        console.log('AuthForm: Registration response:', response);
      } else {
        console.log('AuthForm: Attempting to sign in user:', email);
        response = await supabase!.auth.signInWithPassword({
          email,
          password,
        })
        console.log('AuthForm: Sign-in response:', response);
      }

      if (response.error) {
        console.error(`AuthForm: ${isRegistering ? 'Registration' : 'Sign-in'} error:`, response.error.message);
        toast.error(response.error.message) 
        setError(response.error.message)
      } else {
        if (isRegistering && response.data.user) {
          toast.success('Registration successful! Please check your email to confirm your account (auto-verified for dev).')
          console.log('AuthForm: Registration successful for user:', response.data.user.id);
          // For development, Supabase should auto-redirect to the emailRedirectTo URL (/auth/callback)
          // due to email_verified: true. No explicit client-side redirect here needed for that part.
          // We will let the auth state change redirect the user to their dashboard after callback.
        } else if (!isRegistering && response.data.session) {
          toast.success('Sign-in successful! Redirecting...')
          console.log('AuthForm: Sign-in successful for user:', response.data.user?.id);
          console.log('AuthForm: User session data:', response.data.session);
          console.log('AuthForm: User metadata:', response.data.user?.user_metadata);
          
          // After sign-in, redirect to a common place and let AuthProvider/middleware handle role-based redirect.
          router.push('/'); // Or a specific dashboard if you prefer, but root is safer.
        }
      }
    } catch (err: any) {
      console.error('AuthForm: Unexpected error during auth process:', err);
      toast.error(err.message || 'An unexpected error occurred.')
      setError(err.message || 'An unexpected error occurred.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex">
      {/* Left Side - Professional Image/Branding */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>

        {/* Content */}
        <div className="relative z-10 flex flex-col justify-center px-12 py-16 text-white">
          <div className="max-w-md">
            {/* Logo */}
            <div className="flex items-center mb-8">
              <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center mr-4">
                <svg className="w-7 h-7 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div>
                <h1 className="text-2xl font-bold">TransFlow</h1>
                <p className="text-gray-300 text-sm">Transportation Management</p>
              </div>
            </div>

            {/* Dynamic Content Based on Mode */}
            <div className="space-y-6">
              {isRegistering ? (
                <>
                  <h2 className="text-3xl font-bold leading-tight">
                    Join Our Transportation Network
                  </h2>
                  <p className="text-xl text-gray-300 leading-relaxed">
                    Connect with clients and affiliates in our comprehensive transportation ecosystem.
                  </p>

                  <div className="space-y-4 pt-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                      <span className="text-gray-300">Access to verified partners</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                      <span className="text-gray-300">Streamlined booking process</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                      <span className="text-gray-300">Professional support</span>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <h2 className="text-3xl font-bold leading-tight">
                    Streamline Your Transportation Operations
                  </h2>
                  <p className="text-xl text-gray-300 leading-relaxed">
                    Manage fleets, track trips, and coordinate with affiliates all in one powerful platform.
                  </p>

                  <div className="space-y-4 pt-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                      <span className="text-gray-300">Real-time fleet tracking</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                      <span className="text-gray-300">Automated quote management</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                      <span className="text-gray-300">Comprehensive reporting</span>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Auth Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-50">
        <div className="w-full max-w-md space-y-8">
          {/* Mobile Logo */}
          <div className="lg:hidden text-center">
            <div className="mx-auto w-16 h-16 bg-gray-900 rounded-xl flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-900">TransFlow</h1>
            <p className="text-gray-600 text-sm">Transportation Management Platform</p>
          </div>

          {/* Auth Form */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-8 space-y-6">
            <div className="text-center space-y-2">
              <h2 className="text-2xl font-bold text-gray-900">{
                isRegistering ? 'Create Your Account' : 'Welcome Back'
              }</h2>
              <p className="text-gray-600">{
                isRegistering ? 'Join our transportation network' : 'Sign in to your account to continue'
              }</p>
            </div>
            <form onSubmit={handleSubmit} className="space-y-5">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-medium text-gray-700">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  placeholder="Enter your email"
                  className="h-12 px-4 rounded-lg border-gray-300 focus:border-gray-900 focus:ring-gray-900"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password" className="text-sm font-medium text-gray-700">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  placeholder="Enter your password"
                  className="h-12 px-4 rounded-lg border-gray-300 focus:border-gray-900 focus:ring-gray-900"
                />
              </div>
              {isRegistering && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">Confirm Password</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      required
                      placeholder="Confirm your password"
                      className="h-12 px-4 rounded-lg border-gray-300 focus:border-gray-900 focus:ring-gray-900"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="role" className="text-sm font-medium text-gray-700">Account Type</Label>
                    <Select value={selectedRole} onValueChange={(value) => setSelectedRole(value as UserRole)}>
                      <SelectTrigger id="role" className="h-12 rounded-lg border-gray-300 focus:border-gray-900 focus:ring-gray-900">
                        <SelectValue placeholder="Select your account type" />
                      </SelectTrigger>
                      <SelectContent>
                        {validRoles.map(role => (
                          <SelectItem key={role} value={role}>
                            {role === 'CLIENT' ? 'Client - Request Transportation' :
                             role === 'AFFILIATE' ? 'Affiliate - Provide Transportation' :
                             role === 'SUPER_ADMIN' ? 'Administrator - System Management' :
                             role.charAt(0).toUpperCase() + role.slice(1).toLowerCase().replace(/_/g, ' ')}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </>
              )}
              <Button
                type="submit"
                className="w-full h-12 bg-gray-900 hover:bg-gray-800 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
                disabled={loading}
              >
                {loading ? (
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                ) : isRegistering ? (
                  <>
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                    </svg>
                    Create Account
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013 3v1" />
                    </svg>
                    Sign In
                  </>
                )}
              </Button>
            </form>

            {error && (
              <div className="p-4 rounded-lg bg-red-50 border border-red-200">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            )}

            <div className="text-center">
              <p className="text-sm text-gray-600">
                {isRegistering ? 'Already have an account?' : 'Don\'t have an account?'}{' '}
                <button
                  type="button"
                  onClick={() => {
                    setIsRegistering(!isRegistering);
                    setError(null);
                    setEmail('');
                    setPassword('');
                    setConfirmPassword('');
                  }}
                  className="text-gray-900 hover:text-gray-700 font-medium"
                >
                  {isRegistering ? 'Sign in here' : 'Create account'}
                </button>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
