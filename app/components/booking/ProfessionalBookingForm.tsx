"use client"

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { Textarea } from '@/app/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs'
import { Badge } from '@/app/components/ui/badge'
import { Separator } from '@/app/components/ui/separator'
import { Calendar, Clock, MapPin, Users, Car, Plane, CalendarDays, ChevronRight, CheckCircle } from 'lucide-react'
import { useAuth } from '@/lib/auth/context'
import { toast } from '@/app/components/ui/use-toast'
import { cn } from '@/lib/utils'

interface ProfessionalBookingFormProps {
  onQuoteSubmit?: (quoteData: any) => void
  defaultTab?: 'point-to-point' | 'hourly' | 'airport' | 'multi-day'
  className?: string
  embedded?: boolean
}

interface QuoteFormData {
  bookingType: string
  pickupLocation: string
  dropoffLocation: string
  pickupDate: string
  pickupTime: string
  adults: number
  children: number
  specialInstructions: string
  // Service-specific fields
  flightNumber?: string
  airline?: string
  duration?: number
  endDate?: string
}

const steps = [
  { id: 1, name: 'Trip Details', icon: MapPin },
  { id: 2, name: 'Review', icon: CheckCircle }
]

export function ProfessionalBookingForm({ 
  onQuoteSubmit, 
  defaultTab = 'point-to-point',
  className = '',
  embedded = false
}: ProfessionalBookingFormProps) {
  const router = useRouter()
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState(defaultTab)
  const [currentStep, setCurrentStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState<QuoteFormData>({
    bookingType: defaultTab,
    pickupLocation: '',
    dropoffLocation: '',
    pickupDate: '',
    pickupTime: '',
    adults: 1,
    children: 0,
    specialInstructions: ''
  })

  // Update booking type when tab changes
  useEffect(() => {
    setFormData(prev => ({ ...prev, bookingType: activeTab }))
  }, [activeTab])

  const handleInputChange = (field: keyof QuoteFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const validateStep1 = (): boolean => {
    if (!formData.pickupLocation.trim()) {
      toast({ title: "Error", description: "Pickup location is required", variant: "destructive" })
      return false
    }
    
    if (activeTab !== 'hourly' && !formData.dropoffLocation.trim()) {
      toast({ title: "Error", description: "Drop-off location is required", variant: "destructive" })
      return false
    }
    
    if (!formData.pickupDate) {
      toast({ title: "Error", description: "Pickup date is required", variant: "destructive" })
      return false
    }
    
    if (!formData.pickupTime) {
      toast({ title: "Error", description: "Pickup time is required", variant: "destructive" })
      return false
    }

    return true
  }

  const handleNext = () => {
    if (currentStep === 1 && validateStep1()) {
      setCurrentStep(2)
    }
  }

  const handleSubmit = async () => {
    if (!validateStep1()) return

    setIsSubmitting(true)
    
    try {
      // Prepare quote data for our existing API
      const quoteData = {
        pickup_location: formData.pickupLocation,
        dropoff_location: formData.dropoffLocation,
        pickup_date: formData.pickupDate,
        pickup_time: formData.pickupTime,
        passenger_count: formData.adults + formData.children,
        adults: formData.adults,
        children: formData.children,
        service_type: activeTab,
        special_instructions: formData.specialInstructions,
        // Additional fields based on booking type
        ...(activeTab === 'airport' && {
          flight_number: formData.flightNumber,
          airline: formData.airline
        }),
        ...(activeTab === 'hourly' && {
          duration_hours: formData.duration || 4
        }),
        ...(activeTab === 'multi-day' && {
          end_date: formData.endDate
        })
      }

      // Submit to our existing quote API
      const response = await fetch('/api/quotes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(quoteData)
      })

      if (!response.ok) {
        throw new Error('Failed to submit quote request')
      }

      const result = await response.json()
      
      toast({
        title: "Quote Request Submitted!",
        description: `Quote ${result.reference_number} created successfully.`,
      })

      // Call the callback if provided
      if (onQuoteSubmit) {
        onQuoteSubmit(result)
      } else {
        // Default behavior - redirect to quotes page
        router.push('/event-manager/quotes')
      }

    } catch (error) {
      console.error('Quote submission error:', error)
      toast({
        title: "Error",
        description: "Failed to submit quote request. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const getTodayDate = () => {
    const today = new Date()
    return today.toISOString().split('T')[0]
  }

  const containerClasses = cn(
    "w-full mx-auto bg-white rounded-lg overflow-hidden",
    embedded ? "border-0 shadow-none" : "max-w-6xl shadow-lg border",
    className
  )

  return (
    <div className={containerClasses}>
      {/* Progress Steps */}
      {!embedded && (
        <div className="bg-gray-50 px-6 py-4 border-b">
          <div className="flex items-center justify-between max-w-md mx-auto">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium",
                  currentStep >= step.id 
                    ? "bg-primary text-white" 
                    : "bg-gray-200 text-gray-600"
                )}>
                  {currentStep > step.id ? (
                    <CheckCircle className="w-4 h-4" />
                  ) : (
                    step.id
                  )}
                </div>
                <span className={cn(
                  "ml-2 text-sm font-medium",
                  currentStep >= step.id ? "text-primary" : "text-gray-500"
                )}>
                  {step.name}
                </span>
                {index < steps.length - 1 && (
                  <ChevronRight className="w-4 h-4 mx-4 text-gray-400" />
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="p-6">
        {currentStep === 1 && (
          <>
            {/* Service Type Tabs */}
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="mb-6">
              <TabsList className="grid w-full grid-cols-4 mb-6">
                <TabsTrigger value="point-to-point" className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  <span className="hidden sm:inline">Point to Point</span>
                  <span className="sm:hidden">P2P</span>
                </TabsTrigger>
                <TabsTrigger value="hourly" className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span className="hidden sm:inline">Hourly</span>
                  <span className="sm:hidden">Hourly</span>
                </TabsTrigger>
                <TabsTrigger value="airport" className="flex items-center gap-2">
                  <Plane className="h-4 w-4" />
                  <span className="hidden sm:inline">Airport</span>
                  <span className="sm:hidden">Airport</span>
                </TabsTrigger>
                <TabsTrigger value="multi-day" className="flex items-center gap-2">
                  <CalendarDays className="h-4 w-4" />
                  <span className="hidden sm:inline">Multi-Day</span>
                  <span className="sm:hidden">Multi</span>
                </TabsTrigger>
              </TabsList>

              {/* Form Content */}
              <div className="space-y-6">
                {/* Location Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="pickup">Pickup Location *</Label>
                    <Input
                      id="pickup"
                      placeholder="Enter pickup address"
                      value={formData.pickupLocation}
                      onChange={(e) => handleInputChange('pickupLocation', e.target.value)}
                      required
                    />
                  </div>
                  {activeTab !== 'hourly' && (
                    <div>
                      <Label htmlFor="dropoff">
                        {activeTab === 'airport' ? 'Airport/Destination' : 'Drop-off Location'} *
                      </Label>
                      <Input
                        id="dropoff"
                        placeholder={activeTab === 'airport' ? 'Enter airport or destination' : 'Enter destination address'}
                        value={formData.dropoffLocation}
                        onChange={(e) => handleInputChange('dropoffLocation', e.target.value)}
                        required
                      />
                    </div>
                  )}
                </div>

                {/* Service-Specific Fields */}
                {activeTab === 'hourly' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="duration">Duration (Hours)</Label>
                      <Select 
                        value={formData.duration?.toString() || '4'} 
                        onValueChange={(value) => handleInputChange('duration', parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select duration" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="2">2 Hours</SelectItem>
                          <SelectItem value="3">3 Hours</SelectItem>
                          <SelectItem value="4">4 Hours</SelectItem>
                          <SelectItem value="6">6 Hours</SelectItem>
                          <SelectItem value="8">8 Hours</SelectItem>
                          <SelectItem value="12">12 Hours</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}

                {activeTab === 'airport' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="flight">Flight Number</Label>
                      <Input
                        id="flight"
                        placeholder="e.g., AA1234"
                        value={formData.flightNumber || ''}
                        onChange={(e) => handleInputChange('flightNumber', e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="airline">Airline</Label>
                      <Input
                        id="airline"
                        placeholder="e.g., American Airlines"
                        value={formData.airline || ''}
                        onChange={(e) => handleInputChange('airline', e.target.value)}
                      />
                    </div>
                  </div>
                )}

                {activeTab === 'multi-day' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="end-date">End Date</Label>
                      <Input
                        id="end-date"
                        type="date"
                        min={getTodayDate()}
                        value={formData.endDate || ''}
                        onChange={(e) => handleInputChange('endDate', e.target.value)}
                      />
                    </div>
                  </div>
                )}

                {/* Date and Time */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="date">Pickup Date *</Label>
                    <Input
                      id="date"
                      type="date"
                      min={getTodayDate()}
                      value={formData.pickupDate}
                      onChange={(e) => handleInputChange('pickupDate', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="time">Pickup Time *</Label>
                    <Input
                      id="time"
                      type="time"
                      value={formData.pickupTime}
                      onChange={(e) => handleInputChange('pickupTime', e.target.value)}
                      required
                    />
                  </div>
                </div>

                {/* Passenger Count */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="adults">Adults</Label>
                    <Select 
                      value={formData.adults.toString()} 
                      onValueChange={(value) => handleInputChange('adults', parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {[1,2,3,4,5,6,7,8,9,10].map(num => (
                          <SelectItem key={num} value={num.toString()}>{num}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="children">Children</Label>
                    <Select 
                      value={formData.children.toString()} 
                      onValueChange={(value) => handleInputChange('children', parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {[0,1,2,3,4,5].map(num => (
                          <SelectItem key={num} value={num.toString()}>{num}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Special Instructions */}
                <div>
                  <Label htmlFor="instructions">Special Instructions</Label>
                  <Textarea
                    id="instructions"
                    placeholder="Any special requirements or instructions..."
                    value={formData.specialInstructions}
                    onChange={(e) => handleInputChange('specialInstructions', e.target.value)}
                    rows={3}
                  />
                </div>

                {/* Next Button */}
                <div className="flex justify-end">
                  <Button onClick={handleNext} className="min-w-[120px]">
                    Next
                    <ChevronRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </div>
            </Tabs>
          </>
        )}

        {currentStep === 2 && (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">Review Your Request</h3>
              <p className="text-gray-600">Please review your transportation details before submitting</p>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Car className="h-5 w-5" />
                  Trip Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Service Type</Label>
                    <p className="capitalize">{activeTab.replace('-', ' ')}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Passengers</Label>
                    <p>{formData.adults} Adults{formData.children > 0 && `, ${formData.children} Children`}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Pickup Location</Label>
                    <p>{formData.pickupLocation}</p>
                  </div>
                  {formData.dropoffLocation && (
                    <div>
                      <Label className="text-sm font-medium text-gray-500">Drop-off Location</Label>
                      <p>{formData.dropoffLocation}</p>
                    </div>
                  )}
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Date & Time</Label>
                    <p>{formData.pickupDate} at {formData.pickupTime}</p>
                  </div>
                  {formData.duration && (
                    <div>
                      <Label className="text-sm font-medium text-gray-500">Duration</Label>
                      <p>{formData.duration} hours</p>
                    </div>
                  )}
                </div>
                
                {formData.specialInstructions && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Special Instructions</Label>
                    <p className="text-sm">{formData.specialInstructions}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <div className="flex justify-between">
              <Button variant="outline" onClick={() => setCurrentStep(1)}>
                Back
              </Button>
              <Button 
                onClick={handleSubmit} 
                disabled={isSubmitting}
                className="min-w-[150px]"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Quote Request'}
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
