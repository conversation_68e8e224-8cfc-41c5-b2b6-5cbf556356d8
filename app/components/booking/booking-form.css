/* Booking Form Styles */
.booking-form-wrapper {
  @apply w-full max-w-4xl mx-auto;
}

.booking-form-tabs {
  @apply w-full;
}

.booking-form-tab-trigger {
  @apply flex items-center gap-2 px-4 py-2 text-sm font-medium transition-colors;
}

.booking-form-tab-trigger[data-state="active"] {
  @apply bg-primary text-primary-foreground;
}

.booking-form-tab-content {
  @apply mt-4 space-y-4;
}

.booking-form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.booking-form-field {
  @apply space-y-2;
}

.booking-form-label {
  @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
}

.booking-form-input {
  @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
}

.booking-form-textarea {
  @apply flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
}

.booking-form-select-trigger {
  @apply flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
}

.booking-form-button {
  @apply inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
}

.booking-form-button-primary {
  @apply bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2;
}

.booking-form-button-secondary {
  @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 h-10 px-4 py-2;
}

.booking-form-button-outline {
  @apply border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .booking-form-grid {
    @apply grid-cols-1;
  }
  
  .booking-form-tabs {
    @apply text-sm;
  }
  
  .booking-form-tab-trigger {
    @apply px-2 py-1 text-xs;
  }
}

/* Animation classes */
.booking-form-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.booking-form-slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Loading states */
.booking-form-loading {
  @apply opacity-50 pointer-events-none;
}

.booking-form-spinner {
  @apply animate-spin rounded-full h-4 w-4 border-b-2 border-primary;
}

/* Success states */
.booking-form-success {
  @apply bg-green-50 border-green-200 text-green-800 p-4 rounded-md;
}

.booking-form-error {
  @apply bg-red-50 border-red-200 text-red-800 p-4 rounded-md;
}

/* Focus states */
.booking-form-input:focus,
.booking-form-textarea:focus,
.booking-form-select-trigger:focus {
  @apply ring-2 ring-primary ring-offset-2;
}

/* Disabled states */
.booking-form-input:disabled,
.booking-form-textarea:disabled,
.booking-form-select-trigger:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* Card styling for embedded forms */
.booking-form-card {
  @apply bg-card text-card-foreground rounded-lg border shadow-sm;
}

.booking-form-card-header {
  @apply flex flex-col space-y-1.5 p-6;
}

.booking-form-card-content {
  @apply p-6 pt-0;
}

.booking-form-card-title {
  @apply text-2xl font-semibold leading-none tracking-tight;
}

.booking-form-card-description {
  @apply text-sm text-muted-foreground;
}
