"use client"

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { Textarea } from '@/app/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs'
import { Badge } from '@/app/components/ui/badge'
import { Separator } from '@/app/components/ui/separator'
import { Calendar, Clock, MapPin, Users, Car, Plane, CalendarDays } from 'lucide-react'
import { useAuth } from '@/lib/auth/context'
import { toast } from '@/app/components/ui/use-toast'

interface BookingFormWrapperProps {
  onQuoteSubmit?: (quoteData: any) => void
  defaultTab?: 'point-to-point' | 'hourly' | 'airport' | 'multi-day'
  className?: string
}

interface QuoteFormData {
  bookingType: string
  pickupLocation: string
  dropoffLocation: string
  pickupDate: string
  pickupTime: string
  adults: number
  children: number
  specialInstructions: string
  // Airport specific
  flightNumber?: string
  airline?: string
  // Hourly specific
  duration?: number
  // Multi-day specific
  endDate?: string
}

export function BookingFormWrapper({ 
  onQuoteSubmit, 
  defaultTab = 'point-to-point',
  className = '' 
}: BookingFormWrapperProps) {
  const router = useRouter()
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState(defaultTab)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState<QuoteFormData>({
    bookingType: defaultTab,
    pickupLocation: '',
    dropoffLocation: '',
    pickupDate: '',
    pickupTime: '',
    adults: 1,
    children: 0,
    specialInstructions: ''
  })

  // Update booking type when tab changes
  useEffect(() => {
    setFormData(prev => ({ ...prev, bookingType: activeTab }))
  }, [activeTab])

  const handleInputChange = (field: keyof QuoteFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const validateForm = (): boolean => {
    if (!formData.pickupLocation.trim()) {
      toast({ title: "Error", description: "Pickup location is required", variant: "destructive" })
      return false
    }
    
    if (activeTab !== 'hourly' && !formData.dropoffLocation.trim()) {
      toast({ title: "Error", description: "Drop-off location is required", variant: "destructive" })
      return false
    }
    
    if (!formData.pickupDate) {
      toast({ title: "Error", description: "Pickup date is required", variant: "destructive" })
      return false
    }
    
    if (!formData.pickupTime) {
      toast({ title: "Error", description: "Pickup time is required", variant: "destructive" })
      return false
    }

    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setIsSubmitting(true)
    
    try {
      // Prepare quote data for our existing API
      const quoteData = {
        pickup_location: formData.pickupLocation,
        dropoff_location: formData.dropoffLocation,
        pickup_date: formData.pickupDate,
        pickup_time: formData.pickupTime,
        passenger_count: formData.adults + formData.children,
        adults: formData.adults,
        children: formData.children,
        service_type: activeTab,
        special_instructions: formData.specialInstructions,
        // Additional fields based on booking type
        ...(activeTab === 'airport' && {
          flight_number: formData.flightNumber,
          airline: formData.airline
        }),
        ...(activeTab === 'hourly' && {
          duration_hours: formData.duration || 4
        }),
        ...(activeTab === 'multi-day' && {
          end_date: formData.endDate
        })
      }

      // Submit to our existing quote API
      const response = await fetch('/api/quotes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(quoteData)
      })

      if (!response.ok) {
        throw new Error('Failed to submit quote request')
      }

      const result = await response.json()
      
      toast({
        title: "Quote Request Submitted!",
        description: "We'll send you available options shortly.",
      })

      // Call the callback if provided
      if (onQuoteSubmit) {
        onQuoteSubmit(result)
      } else {
        // Default behavior - redirect to quotes page
        router.push('/event-manager/quotes')
      }

    } catch (error) {
      console.error('Quote submission error:', error)
      toast({
        title: "Error",
        description: "Failed to submit quote request. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const getTodayDate = () => {
    const today = new Date()
    return today.toISOString().split('T')[0]
  }

  const getCurrentTime = () => {
    const now = new Date()
    const hours = now.getHours().toString().padStart(2, '0')
    const minutes = now.getMinutes().toString().padStart(2, '0')
    return `${hours}:${minutes}`
  }

  return (
    <Card className={`w-full max-w-4xl mx-auto ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Car className="h-6 w-6" />
          Request Transportation Quote
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Service Type Tabs */}
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="point-to-point" className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Point to Point
              </TabsTrigger>
              <TabsTrigger value="hourly" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Hourly
              </TabsTrigger>
              <TabsTrigger value="airport" className="flex items-center gap-2">
                <Plane className="h-4 w-4" />
                Airport
              </TabsTrigger>
              <TabsTrigger value="multi-day" className="flex items-center gap-2">
                <CalendarDays className="h-4 w-4" />
                Multi-Day
              </TabsTrigger>
            </TabsList>

            {/* Point to Point Form */}
            <TabsContent value="point-to-point" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="pickup">Pickup Location *</Label>
                  <Input
                    id="pickup"
                    placeholder="Enter pickup address"
                    value={formData.pickupLocation}
                    onChange={(e) => handleInputChange('pickupLocation', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="dropoff">Drop-off Location *</Label>
                  <Input
                    id="dropoff"
                    placeholder="Enter destination address"
                    value={formData.dropoffLocation}
                    onChange={(e) => handleInputChange('dropoffLocation', e.target.value)}
                    required
                  />
                </div>
              </div>
            </TabsContent>

            {/* Hourly Form */}
            <TabsContent value="hourly" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="pickup-hourly">Pickup Location *</Label>
                  <Input
                    id="pickup-hourly"
                    placeholder="Enter pickup address"
                    value={formData.pickupLocation}
                    onChange={(e) => handleInputChange('pickupLocation', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="duration">Duration (Hours)</Label>
                  <Select 
                    value={formData.duration?.toString() || '4'} 
                    onValueChange={(value) => handleInputChange('duration', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select duration" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="2">2 Hours</SelectItem>
                      <SelectItem value="3">3 Hours</SelectItem>
                      <SelectItem value="4">4 Hours</SelectItem>
                      <SelectItem value="6">6 Hours</SelectItem>
                      <SelectItem value="8">8 Hours</SelectItem>
                      <SelectItem value="12">12 Hours</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            {/* Airport Form */}
            <TabsContent value="airport" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="pickup-airport">Pickup Location *</Label>
                  <Input
                    id="pickup-airport"
                    placeholder="Enter pickup address"
                    value={formData.pickupLocation}
                    onChange={(e) => handleInputChange('pickupLocation', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="dropoff-airport">Airport/Destination *</Label>
                  <Input
                    id="dropoff-airport"
                    placeholder="Enter airport or destination"
                    value={formData.dropoffLocation}
                    onChange={(e) => handleInputChange('dropoffLocation', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="flight">Flight Number</Label>
                  <Input
                    id="flight"
                    placeholder="e.g., AA1234"
                    value={formData.flightNumber || ''}
                    onChange={(e) => handleInputChange('flightNumber', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="airline">Airline</Label>
                  <Input
                    id="airline"
                    placeholder="e.g., American Airlines"
                    value={formData.airline || ''}
                    onChange={(e) => handleInputChange('airline', e.target.value)}
                  />
                </div>
              </div>
            </TabsContent>

            {/* Multi-Day Form */}
            <TabsContent value="multi-day" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="pickup-multiday">Primary Pickup Location *</Label>
                  <Input
                    id="pickup-multiday"
                    placeholder="Enter primary pickup address"
                    value={formData.pickupLocation}
                    onChange={(e) => handleInputChange('pickupLocation', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="dropoff-multiday">Primary Destination *</Label>
                  <Input
                    id="dropoff-multiday"
                    placeholder="Enter primary destination"
                    value={formData.dropoffLocation}
                    onChange={(e) => handleInputChange('dropoffLocation', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="end-date">End Date</Label>
                  <Input
                    id="end-date"
                    type="date"
                    min={getTodayDate()}
                    value={formData.endDate || ''}
                    onChange={(e) => handleInputChange('endDate', e.target.value)}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <Separator />

          {/* Common Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="date">Pickup Date *</Label>
              <Input
                id="date"
                type="date"
                min={getTodayDate()}
                value={formData.pickupDate}
                onChange={(e) => handleInputChange('pickupDate', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="time">Pickup Time *</Label>
              <Input
                id="time"
                type="time"
                value={formData.pickupTime}
                onChange={(e) => handleInputChange('pickupTime', e.target.value)}
                required
              />
            </div>
          </div>

          {/* Passenger Count */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="adults">Adults</Label>
              <Select 
                value={formData.adults.toString()} 
                onValueChange={(value) => handleInputChange('adults', parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {[1,2,3,4,5,6,7,8,9,10].map(num => (
                    <SelectItem key={num} value={num.toString()}>{num}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="children">Children</Label>
              <Select 
                value={formData.children.toString()} 
                onValueChange={(value) => handleInputChange('children', parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {[0,1,2,3,4,5].map(num => (
                    <SelectItem key={num} value={num.toString()}>{num}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Special Instructions */}
          <div>
            <Label htmlFor="instructions">Special Instructions</Label>
            <Textarea
              id="instructions"
              placeholder="Any special requirements or instructions..."
              value={formData.specialInstructions}
              onChange={(e) => handleInputChange('specialInstructions', e.target.value)}
              rows={3}
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="min-w-[150px]"
            >
              {isSubmitting ? 'Submitting...' : 'Request Quote'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
