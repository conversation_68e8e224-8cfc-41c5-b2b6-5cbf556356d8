'use client'

import React from 'react'
import { Button } from '@/app/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { AlertTriangle, RefreshCw } from 'lucide-react'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
  errorCount: number
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeouts: NodeJS.Timeout[] = []

  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      errorCount: 0
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorCount: 0
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    
    // Check for infinite loop patterns
    if (error.message.includes('Maximum update depth exceeded') || 
        error.message.includes('Too many re-renders')) {
      console.error('Infinite loop detected, preventing further renders')
      
      // Clear any pending timeouts
      this.retryTimeouts.forEach(timeout => clearTimeout(timeout))
      this.retryTimeouts = []
      
      // Update state to show error
      this.setState(prevState => ({
        hasError: true,
        error,
        errorInfo,
        errorCount: prevState.errorCount + 1
      }))
      
      return
    }

    this.setState({
      hasError: true,
      error,
      errorInfo,
      errorCount: this.state.errorCount + 1
    })
  }

  componentWillUnmount() {
    // Clear any pending timeouts
    this.retryTimeouts.forEach(timeout => clearTimeout(timeout))
  }

  handleRetry = () => {
    // Prevent too many rapid retries
    if (this.state.errorCount > 3) {
      console.warn('Too many errors, reloading page instead of retrying')
      window.location.reload()
      return
    }

    console.log('Retrying after error...')
    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined
    })
  }

  handleReload = () => {
    window.location.reload()
  }

  handleClearCache = () => {
    // Clear browser cache and reload
    if (typeof window !== 'undefined') {
      localStorage.clear()
      sessionStorage.clear()
      
      // Clear service worker cache if available
      if ('serviceWorker' in navigator && 'caches' in window) {
        caches.keys().then(cacheNames => {
          return Promise.all(
            cacheNames.map(cacheName => caches.delete(cacheName))
          )
        }).then(() => {
          window.location.reload()
        }).catch(() => {
          window.location.reload()
        })
      } else {
        window.location.reload()
      }
    }
  }

  render() {
    if (this.state.hasError) {
      const { error, errorCount } = this.state
      const { fallback: Fallback } = this.props

      if (Fallback) {
        return <Fallback error={error!} retry={this.handleRetry} />
      }

      return (
        <div className="min-h-screen flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardHeader>
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-destructive" />
                <CardTitle>Something went wrong</CardTitle>
              </div>
              <CardDescription>
                {error?.message.includes('Maximum update depth') || error?.message.includes('Too many re-renders')
                  ? 'An infinite loop was detected and prevented.'
                  : 'An unexpected error occurred.'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {error && (
                <div className="text-sm text-muted-foreground bg-muted p-3 rounded">
                  <strong>Error:</strong> {error.message}
                </div>
              )}
              
              <div className="text-sm text-muted-foreground">
                Error count: {errorCount}
              </div>

              <div className="flex flex-col gap-2">
                {errorCount <= 3 && (
                  <Button onClick={this.handleRetry} variant="default">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Try Again
                  </Button>
                )}
                
                <Button onClick={this.handleReload} variant="outline">
                  Reload Page
                </Button>
                
                <Button onClick={this.handleClearCache} variant="outline">
                  Clear Cache & Reload
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}

// Hook version for functional components
export function useErrorBoundary() {
  const [error, setError] = React.useState<Error | null>(null)

  const resetError = React.useCallback(() => {
    setError(null)
  }, [])

  const captureError = React.useCallback((error: Error) => {
    setError(error)
  }, [])

  React.useEffect(() => {
    if (error) {
      throw error
    }
  }, [error])

  return { captureError, resetError }
}
