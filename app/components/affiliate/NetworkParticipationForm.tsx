"use client"

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Badge } from "@/app/components/ui/badge"
import { Switch } from "@/app/components/ui/switch"
import { Label } from "@/app/components/ui/label"
import { Textarea } from "@/app/components/ui/textarea"
import { Alert, AlertDescription } from "@/app/components/ui/alert"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/app/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import { 
  Globe, 
  Building2, 
  Users, 
  TrendingUp, 
  Shield,
  Info,
  CheckCircle,
  AlertTriangle
} from "lucide-react"
import { toast } from "@/app/components/ui/use-toast"

const networkParticipationSchema = z.object({
  participateInGlobalNetwork: z.boolean(),
  exclusiveToBrands: z.array(z.string()).optional(),
  rateVisibility: z.enum(['transparent', 'brand-specific']),
  preferredQuoteVolume: z.enum(['maximum', 'selective']),
  notes: z.string().optional(),
})

type NetworkParticipationFormData = z.infer<typeof networkParticipationSchema>

interface NetworkParticipationFormProps {
  affiliateId: string
  initialData?: {
    global_network: boolean
    exclusive_brands: string[]
    rate_sharing: string
    preferred_volume: string
  }
  onSave: (data: NetworkParticipationFormData) => Promise<void>
  isLoading?: boolean
}

export function NetworkParticipationForm({
  affiliateId,
  initialData,
  onSave,
  isLoading = false
}: NetworkParticipationFormProps) {
  const [isSaving, setIsSaving] = useState(false)
  const [availableBrands, setAvailableBrands] = useState([
    { id: 'limo123', name: 'LIMO123', description: 'Premium luxury transportation' },
    { id: 'dallaslimo', name: 'DallasLimo', description: 'Dallas metropolitan area' },
    { id: 'houstonexec', name: 'HoustonExec', description: 'Executive transportation' },
  ])

  const form = useForm<NetworkParticipationFormData>({
    resolver: zodResolver(networkParticipationSchema),
    defaultValues: {
      participateInGlobalNetwork: initialData?.global_network || false,
      exclusiveToBrands: initialData?.exclusive_brands || [],
      rateVisibility: (initialData?.rate_sharing as 'transparent' | 'brand-specific') || 'transparent',
      preferredQuoteVolume: (initialData?.preferred_volume as 'maximum' | 'selective') || 'maximum',
      notes: '',
    },
  })

  const participateInGlobal = form.watch('participateInGlobalNetwork')
  const exclusiveBrands = form.watch('exclusiveToBrands')

  const handleSubmit = async (data: NetworkParticipationFormData) => {
    setIsSaving(true)
    try {
      await onSave(data)
      toast({
        title: "Network preferences updated",
        description: "Your affiliate network participation has been saved successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update network preferences. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Network Participation Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Network Participation Choice
          </CardTitle>
          <CardDescription>
            Choose how you want to participate in the TransFlow affiliate network
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Building2 className="h-4 w-4 text-blue-600" />
                <h4 className="font-medium">Brand Exclusive</h4>
              </div>
              <p className="text-sm text-muted-foreground mb-3">
                Serve only specific brands with potentially higher rates and focused service
              </p>
              <div className="space-y-1 text-xs">
                <div className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-600" />
                  <span>Higher commission rates</span>
                </div>
                <div className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-600" />
                  <span>Brand-focused service standards</span>
                </div>
                <div className="flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3 text-yellow-600" />
                  <span>Limited quote volume</span>
                </div>
              </div>
            </div>

            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Globe className="h-4 w-4 text-green-600" />
                <h4 className="font-medium">Global Network</h4>
              </div>
              <p className="text-sm text-muted-foreground mb-3">
                Access the full TransFlow network for maximum quote volume and reach
              </p>
              <div className="space-y-1 text-xs">
                <div className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-600" />
                  <span>Maximum quote volume</span>
                </div>
                <div className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-600" />
                  <span>Network effects & growth</span>
                </div>
                <div className="flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3 text-yellow-600" />
                  <span>Competitive pricing</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Configuration Form */}
      <Card>
        <CardHeader>
          <CardTitle>Network Configuration</CardTitle>
          <CardDescription>
            Configure your network participation preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              {/* Global Network Participation */}
              <FormField
                control={form.control}
                name="participateInGlobalNetwork"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        Join Global TransFlow Network
                      </FormLabel>
                      <FormDescription>
                        Receive quotes from all TransFlow clients and brands
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {/* Brand Selection */}
              {!participateInGlobal && (
                <FormField
                  control={form.control}
                  name="exclusiveToBrands"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Exclusive Brand Selection</FormLabel>
                      <FormDescription>
                        Choose which brands you want to serve exclusively
                      </FormDescription>
                      <div className="grid grid-cols-1 gap-3">
                        {availableBrands.map((brand) => (
                          <div key={brand.id} className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id={brand.id}
                              checked={field.value?.includes(brand.id) || false}
                              onChange={(e) => {
                                const currentBrands = field.value || []
                                if (e.target.checked) {
                                  field.onChange([...currentBrands, brand.id])
                                } else {
                                  field.onChange(currentBrands.filter(b => b !== brand.id))
                                }
                              }}
                              className="rounded border-gray-300"
                            />
                            <Label htmlFor={brand.id} className="flex-1">
                              <div>
                                <div className="font-medium">{brand.name}</div>
                                <div className="text-sm text-muted-foreground">
                                  {brand.description}
                                </div>
                              </div>
                            </Label>
                          </div>
                        ))}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Rate Visibility */}
              <FormField
                control={form.control}
                name="rateVisibility"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Rate Visibility</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select rate visibility preference" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="transparent">
                          Transparent - Same rates across all brands
                        </SelectItem>
                        <SelectItem value="brand-specific">
                          Brand-specific - Different rates per brand
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      How your rates will be displayed across different brands
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Quote Volume Preference */}
              <FormField
                control={form.control}
                name="preferredQuoteVolume"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quote Volume Preference</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select volume preference" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="maximum">
                          Maximum - Receive all eligible quotes
                        </SelectItem>
                        <SelectItem value="selective">
                          Selective - Quality over quantity
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Your preference for quote volume vs. selectivity
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Additional Notes */}
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Additional Notes (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Any specific requirements or preferences..."
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Share any additional information about your network preferences
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Impact Summary */}
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <strong>Impact Summary:</strong> {participateInGlobal ? (
                    <>You'll receive quotes from all TransFlow brands and clients. Your rates will be visible across the entire network.</>
                  ) : (
                    <>You'll only receive quotes from {exclusiveBrands?.length || 0} selected brand(s). This may result in higher rates but lower volume.</>
                  )}
                </AlertDescription>
              </Alert>

              <div className="flex justify-end gap-3">
                <Button type="submit" disabled={isSaving || isLoading}>
                  {isSaving ? "Saving..." : "Save Network Preferences"}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
