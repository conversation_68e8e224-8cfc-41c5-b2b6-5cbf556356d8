"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Building2 } from "lucide-react"

// ComprehensiveCompanyForm component temporarily disabled due to missing dependencies
// TODO: Implement proper form with available UI components

interface ComprehensiveCompanyFormProps {
  initialData?: any
  onSubmit: (data: any) => Promise<void>
  onAutoSave?: (data: any) => Promise<void>
  isLoading?: boolean
  initialStep?: number
}

export function ComprehensiveCompanyForm({
  initialData,
  onSubmit,
  onAutoSave,
  isLoading = false,
  initialStep = 0
}: ComprehensiveCompanyFormProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          Company Profile Setup
        </CardTitle>
        <CardDescription>
          This comprehensive form is temporarily unavailable. Please use the basic company form instead.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground">
          The comprehensive company form requires additional UI components that are not yet available.
          Please use the standard company profile form in the meantime.
        </p>
      </CardContent>
    </Card>
  )
}
