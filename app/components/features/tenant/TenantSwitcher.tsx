'use client'

import { useState } from 'react'
import { useTenant } from '@/app/contexts/TenantContext'
import { Button } from '@/app/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/app/components/ui/dropdown-menu'
import { Badge } from '@/app/components/ui/badge'
import { ChevronDown, Building2, Globe, Crown } from 'lucide-react'

interface TenantSwitcherProps {
  className?: string
}

export function TenantSwitcher({ className }: TenantSwitcherProps) {
  const { currentTenant, availableTenants, switchTenant, isLoading } = useTenant()
  const [isOpen, setIsOpen] = useState(false)

  const getTenantIcon = (tenantType: string) => {
    switch (tenantType) {
      case 'shared':
        return <Globe className="h-4 w-4" />
      case 'segregated':
        return <Building2 className="h-4 w-4" />
      case 'white_label':
        return <Crown className="h-4 w-4" />
      default:
        return <Building2 className="h-4 w-4" />
    }
  }

  const getTenantTypeLabel = (tenantType: string) => {
    switch (tenantType) {
      case 'shared':
        return 'Shared SaaS'
      case 'segregated':
        return 'TNC Network'
      case 'white_label':
        return 'White Label'
      default:
        return 'Unknown'
    }
  }

  const getTenantTypeBadgeVariant = (tenantType: string) => {
    switch (tenantType) {
      case 'shared':
        return 'default'
      case 'segregated':
        return 'secondary'
      case 'white_label':
        return 'outline'
      default:
        return 'outline'
    }
  }

  if (isLoading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="animate-pulse bg-gray-200 h-8 w-32 rounded"></div>
      </div>
    )
  }

  if (!currentTenant) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Badge variant="destructive">No Tenant</Badge>
      </div>
    )
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className={`flex items-center gap-2 ${className}`}
          disabled={isLoading}
        >
          {getTenantIcon(currentTenant.tenant_type)}
          <span className="font-medium">{currentTenant.name}</span>
          <Badge 
            variant={getTenantTypeBadgeVariant(currentTenant.tenant_type)}
            className="text-xs"
          >
            {getTenantTypeLabel(currentTenant.tenant_type)}
          </Badge>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64">
        <DropdownMenuLabel>Switch Network</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {availableTenants.map((tenant) => (
          <DropdownMenuItem
            key={tenant.id}
            onClick={() => switchTenant(tenant.id)}
            className="flex items-center gap-3 p-3"
          >
            <div className="flex items-center gap-2 flex-1">
              {getTenantIcon(tenant.tenant_type)}
              <div className="flex flex-col">
                <span className="font-medium">{tenant.name}</span>
                {tenant.domain && (
                  <span className="text-xs text-muted-foreground">{tenant.domain}</span>
                )}
              </div>
            </div>
            <Badge 
              variant={getTenantTypeBadgeVariant(tenant.tenant_type)}
              className="text-xs"
            >
              {getTenantTypeLabel(tenant.tenant_type)}
            </Badge>
            {currentTenant.id === tenant.id && (
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            )}
          </DropdownMenuItem>
        ))}
        
        {availableTenants.length === 0 && (
          <DropdownMenuItem disabled>
            No other networks available
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
