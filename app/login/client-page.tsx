'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { getSupabaseClient } from '@/lib/supabase'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { Loader2, RefreshCw } from 'lucide-react'
import { forceRefreshAuthState } from '@/lib/auth-utils'
import { useToast } from '@/app/components/ui/use-toast'
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/app/components/ui/tabs"
import { SupabaseClient, Session, AuthError, AuthTokenResponsePassword } from '@supabase/supabase-js'
// import debounce from 'lodash/debounce'
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Static loading UI used during initial render/before hydration
const LoadingUI = () => (
  <div className="flex items-center justify-center min-h-screen bg-background">
    <div className="w-full max-w-md p-8 space-y-6 bg-card rounded-lg shadow-lg">
      <div className="space-y-2 text-center">
        <h1 className="text-3xl font-bold">Welcome Back</h1>
        <p className="text-muted-foreground">Loading...</p>
      </div>
      <div className="opacity-50 space-y-4">
        <div className="space-y-2">
          <div className="text-sm font-medium">Email</div>
          <div className="h-10 w-full rounded-md border border-input bg-background/40"></div>
        </div>
        <div className="space-y-2">
          <div className="text-sm font-medium">Password</div>
          <div className="h-10 w-full rounded-md border border-input bg-background/40"></div>
        </div>
        <div className="h-10 w-full rounded-md bg-primary/40 text-primary-foreground flex items-center justify-center">
          Please wait...
        </div>
      </div>
    </div>
  </div>
);

// Demo accounts component
const DemoAccounts = () => {
  const { toast } = useToast()
  const router = useRouter()
  const demoAccounts = [
    // System Admin account
    { role: 'System Admin', email: '<EMAIL>', password: 'password123', tenant: 'Global System Admin' },
    
    // Client accounts
    { role: 'Client Admin', email: '<EMAIL>', password: 'password123', tenant: 'Global Events Inc.' },
    { role: 'Client', email: '<EMAIL>', password: 'password123', tenant: 'Default Client' },
    { role: 'Client', email: '<EMAIL>', password: 'password123', tenant: 'Tech Conference Group' },
    
    // Passenger accounts (managed by client admins)
    { role: 'Passenger', email: '<EMAIL>', password: 'password123', tenant: 'Client Passenger' },
    
    // Client Coordinator (previously event manager)
    { role: 'Client Coordinator', email: '<EMAIL>', password: 'password123', tenant: 'Client Coordinator' },
    { role: 'Client Coordinator', email: '<EMAIL>', password: 'password123', tenant: 'Global Events Inc.' },
    
    // Affiliate accounts (transportation providers)
    { role: 'Affiliate Admin', email: '<EMAIL>', password: 'password123', tenant: 'Default Transportation' },
    { role: 'Affiliate Admin', email: '<EMAIL>', password: 'password123', tenant: 'Corporate Travel Solutions' },
    
    // Affiliate staff accounts (managed by affiliate admins)
    { role: 'Dispatcher', email: '<EMAIL>', password: 'password123', tenant: 'Affiliate Staff' },
    { role: 'Driver', email: '<EMAIL>', password: 'password123', tenant: 'Affiliate Driver' }
  ];

  const handleDemoLogin = async (email: string, password: string) => {
    const supabase = getSupabaseClient();
    // First, clear any existing session
    await supabase?.auth.signOut({ scope: 'local' });

    try {
      console.log(`Attempting demo login for: ${email}`);

      const result = await supabase?.auth.signInWithPassword({
        email,
        password
      });

      if (!result) return;

      const { data, error } = result;

      if (error) {
        console.error('Demo login error:', error);
        toast({
          title: 'Login Error',
          description: error.message,
          variant: 'destructive'
        });
        return;
      }

      console.log('Demo login successful:', data.user?.id);
      
      toast({
        title: 'Login Successful',
        description: `Logged in as ${email}`,
        variant: 'default'
      });
      
      // Determine redirect based on role (especially for System Admin)
      setTimeout(() => {
        // ALWAYS redirect to root and let middleware handle role-based dashboard
        const redirectPath = '/'; 
        
        console.log(`Demo login successful, redirecting to: ${redirectPath} for middleware to handle.`);
        router.push(redirectPath);
      }, 1500); // Increased delay to ensure auth state is fully processed
      
    } catch (err) {
      console.error('Unexpected error during demo login:', err);
      toast({
        title: 'Login Error',
        description: 'An unexpected error occurred',
        variant: 'destructive'
      });
    }
  };

  return (
    <div className="mt-6 p-4 border rounded-lg">
      <h3 className="font-semibold mb-3">Demo Accounts</h3>
      <div className="text-xs text-muted-foreground mb-3">
        Click on any account to login instantly.
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
        {demoAccounts.map((account) => (
          <Button
            key={account.email}
            variant="outline"
            className="text-left flex flex-col items-start h-auto py-2"
            onClick={() => handleDemoLogin(account.email, account.password)}
          >
            <span className="font-bold">{account.role}</span>
            <span className="text-xs text-muted-foreground truncate w-full">
              {account.email}
            </span>
            <span className="text-xs text-primary truncate w-full">
              {account.tenant}
            </span>
          </Button>
        ))}
      </div>
    </div>
  );
};

// The actual login form with full interactivity
const LoginForm = () => {
  const { push } = useRouter()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showRefreshButton, setShowRefreshButton] = useState(false);
  const { toast } = useToast()
  const [supabase, setSupabase] = useState<SupabaseClient | null>(null);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  
  useEffect(() => {
    const client = getSupabaseClient();
    setSupabase(client);
  }, []);

  const redirectIfAuthenticated = useCallback(
    debounce(async () => {
      if (!supabase) return;
      console.log('LoginPage: redirectIfAuthenticated - Checking for existing session...');
      const { data, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) {
        console.error('LoginPage: redirectIfAuthenticated - Error getting session:', sessionError.message);
        return;
      }
      if (data.session) {
        console.log('LoginPage: redirectIfAuthenticated - Session found, redirecting...');
        const redirectTo = searchParams.get('redirectTo') || '/';
        push(redirectTo);
      }
    }, 300),
    [push, searchParams, supabase]
  );

  useEffect(() => {
    redirectIfAuthenticated();
    // return () => redirectIfAuthenticated.cancel();
  }, [redirectIfAuthenticated]);
  
  // Check for needs-revalidate cookie on mount
  useEffect(() => {
    const needsRevalidate = document.cookie.includes('needs-revalidate=true');
    if (needsRevalidate) {
      setShowRefreshButton(true);
      
      // Remove the cookie
      document.cookie = 'needs-revalidate=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    }
  }, []);
  
  // Function to manually refresh auth state
  const handleAuthRefresh = async () => {
    setIsLoading(true);
    try {
      // Use the centralized force refresh function
      const success = await forceRefreshAuthState();
      
      if (!success) {
        setError('Unable to refresh your session. Please log in again.');
        setIsLoading(false);
      }
      // No need to set isLoading to false on success as the page will refresh
    } catch (err) {
      console.error('Error refreshing auth:', err);
      setError('An error occurred while refreshing your session. Please log in again.');
      setIsLoading(false);
  }
  };
  
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    setIsLoading(true)
    setError(null)
    
    const formData = new FormData(event.currentTarget)
    const email = formData.get('email') as string
    const password = formData.get('password') as string
    
    // First, clear any existing session to ensure a clean login
    try {
      await supabase?.auth.signOut({ scope: 'local' });
    } catch (e) {
      // Ignore errors during signout
      console.warn('Error during pre-login signout:', e);
    }
    
    try {
      console.log('Attempting login...')
      
      const result = await supabase?.auth.signInWithPassword({
        email,
        password
      })

      if (!result) return;

      const { data, error } = result;
      
      if (error) {
        console.error('Login error:', error)
        setIsLoading(false)
        setError(error.message)
        return
      }
      
      console.log('Login successful')
      console.log('User ID:', data.user?.id)
      
      setTimeout(() => {
        // Prevent redirect loops by checking the current path
        if (window.location.pathname !== '/login') {
          console.log('Already navigated away from login page, skipping redirect');
          return;
        }
        
        // Store login success in localStorage to prevent redirect loops
        localStorage.setItem('loginSuccess', 'true')
        localStorage.setItem('loginTime', Date.now().toString())
        
        // ALWAYS redirect to root and let middleware handle role-based dashboard
        const targetRedirect = '/';
        
        console.log('Login form successful, redirecting to:', targetRedirect, 'for middleware to handle.');
        
        // Use window.location for a full page navigation to avoid hydration issues
        push(targetRedirect)
      }, 800)
      
    } catch (err) {
      console.error('Login exception:', err)
      setIsLoading(false)
      setError('An unexpected error occurred. Please try again.')
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo/Brand Section */}
        <div className="text-center mb-8">
          <div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg mb-4">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-900">TransFlow</h1>
          <p className="text-gray-600 text-sm">Transportation Management Platform</p>
        </div>

        {/* Main Login Card */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 space-y-6">
          <div className="space-y-2 text-center">
            <h2 className="text-2xl font-bold text-gray-900">Welcome Back</h2>
            <p className="text-gray-600">Sign in to your account to continue</p>
          </div>
          {showRefreshButton && (
            <div className="p-4 rounded-xl bg-blue-50 border border-blue-200">
              <p className="text-sm text-blue-800 mb-3">Your session may be active but needs to be refreshed.</p>
              <Button
                variant="outline"
                className="w-full border-blue-300 text-blue-700 hover:bg-blue-50"
                onClick={handleAuthRefresh}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Refresh Session
              </Button>
            </div>
          )}
          <form onSubmit={handleSubmit} className="space-y-5">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium text-gray-700">Email Address</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="Enter your email"
                required
                disabled={isLoading}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="h-12 px-4 rounded-xl border-gray-200 focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm font-medium text-gray-700">Password</Label>
              <Input
                id="password"
                name="password"
                type="password"
                placeholder="Enter your password"
                required
                disabled={isLoading}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="h-12 px-4 rounded-xl border-gray-200 focus:border-blue-500 focus:ring-blue-500"
              />
            </div>

            {error && (
              <div className="p-4 rounded-xl bg-red-50 border border-red-200">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            )}

            <div className="flex items-center justify-between text-sm">
              <label className="flex items-center space-x-2 cursor-pointer">
                <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                <span className="text-gray-600">Remember me</span>
              </label>
              <a href="#" className="text-blue-600 hover:text-blue-700 font-medium">Forgot password?</a>
            </div>

            <Button
              type="submit"
              className="w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-5 w-5 animate-spin mr-2" />
              ) : (
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013 3v1" />
                </svg>
              )}
              {isLoading ? 'Signing In...' : 'Sign In'}
            </Button>
          </form>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-200"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-4 bg-white text-gray-500">Or continue with</span>
            </div>
          </div>

          <DemoAccounts />

          <div className="text-center">
            <p className="text-sm text-gray-600">
              Don't have an account?{' '}
              <a href="/register" className="text-blue-600 hover:text-blue-700 font-medium">
                Sign up here
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function ClientLoginPage() {
  const [isMounted, setIsMounted] = useState(false);
  
  // Wait until component is mounted on client before showing interactive components
  useEffect(() => {
    setIsMounted(true);
  }, []);
  
  // Use loading UI during server-side rendering and initial client render
  if (!isMounted) {
    return <LoadingUI />;
  }
  
  // Once mounted on client, show the actual login form
  return <LoginForm />;
} 