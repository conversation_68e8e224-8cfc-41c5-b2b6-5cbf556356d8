import { bookingDebugger } from '@core/utils/debugLogger';

// Log environment variables loading
bookingDebugger.log('info', 'Config', 'Loading environment variables...');
bookingDebugger.log('debug', 'Config', `MAPBOX_ACCESS_TOKEN: ${import.meta.env.VITE_MAPBOX_ACCESS_TOKEN ? 'present' : 'missing'}`);

// Get configuration from WordPress if available
const wpConfig = typeof window !== 'undefined' && 'limoBookingConfig' in window 
  ? (window as any).limoBookingConfig || {}
  : {
      hereApiKey: '',
      apiBaseUrl: '',
      restUrl: '',
      nonce: '',
      mapboxToken: '',
      theme: {},
      isWordPress: false
    };

export const config = {
  weatherApiKey: import.meta.env.VITE_OPENWEATHER_API_KEY || '',
  mapboxApiKey: import.meta.env.VITE_MAPBOX_API_KEY || '',
  mapboxToken: wpConfig.mapboxToken || import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || '',
  hereApiKey: wpConfig.hereApiKey || import.meta.env.VITE_HERE_API_KEY || '',
  aviationApiKey: import.meta.env.VITE_AVIATION_API_KEY || '',
  stripePublicKey: import.meta.env.VITE_STRIPE_PUBLIC_KEY || '',
  apiUrl: wpConfig.apiBaseUrl || import.meta.env.VITE_API_URL || '',
  appMode: import.meta.env.VITE_APP_MODE || 'standalone',
  appTitle: import.meta.env.VITE_APP_TITLE || 'Limo Booking',
  wpApiUrl: wpConfig.restUrl || '',
  wpNonce: wpConfig.nonce || '',
  isDebug: import.meta.env.DEV || false,
  isWordPress: wpConfig.isWordPress || false,
} as const;

// Log config object
bookingDebugger.log('debug', 'Config', `Loaded config: ${JSON.stringify(config, null, 2)}`);

// Type for the config
export type Config = typeof config; 