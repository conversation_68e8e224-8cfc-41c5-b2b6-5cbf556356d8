import { InputHTMLAttributes, forwardRef } from 'react';
import { cn } from '@core/lib/utils';

interface TextInputProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  leftSection?: React.ReactNode;
  rightSection?: React.ReactNode;
}

export const TextInput = forwardRef<HTMLInputElement, TextInputProps>(
  ({ className, label, error, leftSection, rightSection, ...props }, ref) => {
    return (
      <div className="space-y-1">
        {label && (
          <label className="block text-sm font-medium text-theme-primary">
            {label}
          </label>
        )}
        <div className="relative">
          {leftSection && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-theme-muted">
              {leftSection}
            </div>
          )}
          <input
            ref={ref}
            className={cn(
              'w-full h-[46px] bg-surface text-theme-primary border border-border rounded-lg px-4 py-2 transition-colors',
              'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary',
              'disabled:opacity-50 disabled:cursor-not-allowed',
              leftSection && 'pl-10',
              rightSection && 'pr-10',
              error && 'border-red-400 focus:border-red-400 focus:ring-red-400/50',
              className
            )}
            {...props}
          />
          {rightSection && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-theme-muted">
              {rightSection}
            </div>
          )}
        </div>
        {error && (
          <p className="text-sm text-red-400">{error}</p>
        )}
      </div>
    );
  }
);

TextInput.displayName = 'TextInput';
