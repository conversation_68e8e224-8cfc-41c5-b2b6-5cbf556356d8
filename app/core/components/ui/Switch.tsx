import { InputHTMLAttributes, forwardRef } from 'react';
import { cn } from '@core/lib/utils';

interface SwitchProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type' | 'size'> {
  label?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const Switch = forwardRef<HTMLInputElement, SwitchProps>(
  ({ className, label, size = 'md', checked, onChange, ...props }, ref) => {
    const sizeClasses = {
      sm: 'h-5 w-9',
      md: 'h-6 w-11',
      lg: 'h-7 w-13'
    };

    const thumbSizeClasses = {
      sm: 'h-3 w-3',
      md: 'h-4 w-4',
      lg: 'h-5 w-5'
    };

    return (
      <label className={cn('flex items-center gap-2 cursor-pointer', className)}>
        <div className="relative">
          <input
            ref={ref}
            type="checkbox"
            checked={checked}
            onChange={onChange}
            className="sr-only"
            {...props}
          />
          <div
            className={cn(
              'relative inline-flex items-center rounded-full transition-colors duration-200',
              sizeClasses[size],
              checked ? 'bg-primary' : 'bg-text-disabled'
            )}
          >
            <div
              className={cn(
                'absolute bg-white rounded-full transition-transform duration-200',
                thumbSizeClasses[size],
                checked 
                  ? size === 'sm' ? 'translate-x-4' : size === 'md' ? 'translate-x-5' : 'translate-x-6'
                  : 'translate-x-1'
              )}
            />
          </div>
        </div>
        {label && (
          <span className="text-sm text-theme-primary">{label}</span>
        )}
      </label>
    );
  }
);

Switch.displayName = 'Switch';
