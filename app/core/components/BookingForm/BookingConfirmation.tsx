import React, { useState } from 'react';
import { BookingData } from './types';
import { useBookingStore } from '../../store/bookingStore';
import { cn } from '@core/lib/utils';
import { TextInput } from '@core/components/ui/TextInput';
import { Textarea } from '@core/components/ui/Textarea';
import { Button } from '@core/components/ui/Button';
import { Switch } from '@core/components/ui/Switch';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { ThankYouPage } from './ThankYouPage';
import { createSalesmateLead, sendConfirmationEmail } from '../../services/api';
import { scrollFormToTop } from '@core/lib/utils';
import {
  UserCircleIcon,
  ShieldCheckIcon,
  ClockIcon,
  CreditCardIcon,
  BellAlertIcon,
  UserPlusIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { CheckCircleIcon } from '@heroicons/react/24/solid';

// Form Data Interfaces
interface ContactFormData {
  fullName: string;
  email: string;
  phone: string;
  specialRequests?: string;
}

interface BookingConfirmationProps {
  apiKeys: {
    here: string;
    mapbox: string;
    resend: string;
    salesmate: string;
  };
  nonce: string;
  onSubmit: (data: BookingData) => Promise<void>;
}

const CancellationPolicy = () => (
  <Box>
    <Alert color="green" mb="md" icon={<ClockIcon className="w-5 h-5" />}>
      <Text size="sm">100% refund if cancelled 72 hours before</Text>
    </Alert>
    <Alert color="yellow" mb="md" icon={<ClockIcon className="w-5 h-5" />}>
      <Text size="sm">50% refund if cancelled 48 hours before</Text>
    </Alert>
    <Alert color="red" mb="md" icon={<ClockIcon className="w-5 h-5" />}>
      <Text size="sm">Full charge if cancelled within 24 hours</Text>
    </Alert>
  </Box>
);

const DriverInfo = () => (
  <Card withBorder p="md" style={{ backgroundColor: 'var(--mantine-color-dark-6)' }}>
    <Group gap="md">
      <div className="w-16 h-16 rounded-full bg-surface-dark flex items-center justify-center">
        <UserCircleIcon className="w-10 h-10 text-disabled" />
      </div>
      <Box style={{ flex: 1 }}>
        <Text fw={500} c="primary">Your Chauffeur</Text>
        <Text size="sm" c="dimmed">Details will be provided 24 hours before pickup</Text>
      </Box>
      <ClockIcon className="w-6 h-6 text-primary" />
    </Group>
  </Card>
);

const ServiceGuarantees = () => (
  <Grid>
    <Grid.Col span={6}>
      <Card withBorder p="md" style={{ backgroundColor: 'var(--mantine-color-dark-6)' }}>
        <Group gap="sm">
          <ShieldCheckIcon className="w-5 h-5 text-primary" />
          <Box>
            <Text size="sm" c="primary">Insured Service</Text>
            <Text size="xs" c="dimmed">Full coverage for your journey</Text>
          </Box>
        </Group>
      </Card>
    </Grid.Col>
    <Grid.Col span={6}>
      <Card withBorder p="md" style={{ backgroundColor: 'var(--mantine-color-dark-6)' }}>
        <Group gap="sm">
          <CreditCardIcon className="w-5 h-5 text-primary" />
          <Box>
            <Text size="sm" c="primary">Secure Payment</Text>
            <Text size="xs" c="dimmed">Your payment information is safe</Text>
          </Box>
        </Group>
      </Card>
    </Grid.Col>
  </Grid>
);

// Configuration Error Component
const ConfigurationError = ({ message }: { message: string }) => (
  <Alert
    color="red"
    variant="filled"
    title="Configuration Error"
    icon={<ExclamationTriangleIcon className="h-5 w-5" />}
    mb="lg"
  >
    <Text>{message}</Text>
    <Text size="xs" mt="xs">
      This is a website configuration issue. Please contact the site administrator.
    </Text>
  </Alert>
);

export const BookingConfirmation: React.FC<BookingConfirmationProps> = ({
  apiKeys,
  nonce,
  onSubmit
}) => {
  const [createAccount, setCreateAccount] = useState(false);
  const [showThankYou, setShowThankYou] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [configError, setConfigError] = useState<string | null>(null);
  const { currentStep, setStep, formType, pointToPointData, hourlyData, airportData, multiDayData } = useBookingStore();

  const { register, handleSubmit, formState: { errors } } = useForm<ContactFormData>();

  const handleBack = () => {
    setStep(currentStep - 1);
    setTimeout(() => scrollFormToTop(), 10);
  };

  const onFormSubmit = async (formData: ContactFormData) => {
    setIsSubmitting(true);
    setConfigError(null);

    try {
      // Get the active booking data
      let activeBookingData;
      switch (formType) {
        case 'point-to-point':
          activeBookingData = pointToPointData;
          break;
        case 'hourly':
          activeBookingData = hourlyData;
          break;
        case 'airport':
          activeBookingData = airportData;
          break;
        case 'multi-day':
          activeBookingData = multiDayData;
          break;
      }

      if (!activeBookingData) {
        throw new Error('Please complete the booking form before proceeding.');
      }

      // Validate required fields
      if (!activeBookingData.pickupLocation?.address) {
        throw new Error('Pickup location is required.');
      }

      if (!activeBookingData.pickupDate) {
        throw new Error('Pickup date is required.');
      }

      if (!activeBookingData.selectedVehicle) {
        throw new Error('Please select a vehicle.');
      }

      // Construct the booking data
      const bookingData: any = {
        ...formData,
        createAccount,
        bookingDetails: {
          formType,
          [formType === 'point-to-point' ? 'pointToPointData' :
            formType === 'hourly' ? 'hourlyData' :
              formType === 'airport' ? 'airportData' : 'multiDayData']: activeBookingData
        }
      };

      console.log('Submitting booking data:', bookingData);

      // Create lead in Salesmate CRM
      const leadResult = await createSalesmateLead(bookingData);
      console.log('Lead created successfully:', leadResult);

      // Update bookingData with dealId from Salesmate
      const updatedBookingData = {
        ...bookingData,
        dealId: leadResult.dealId
      };

      // Send confirmation email
      const emailResult = await sendConfirmationEmail(updatedBookingData);
      console.log('Confirmation email sent:', emailResult);

      // Show success message
      toast.success('Your booking request has been submitted successfully!');

      // Show thank you page
      setTimeout(() => {
        setShowThankYou(true);
      }, 100);
    } catch (error) {
      console.error('Submission error:', error);
      
      // Check for configuration errors
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      if (errorMessage.includes('API Key is missing') || 
          errorMessage.includes('administrator to set up') ||
          errorMessage.includes('administrator to verify')) {
        // This is a configuration error - display it prominently
        setConfigError(errorMessage);
      } else {
        // Regular error - show as toast
        toast.error(errorMessage || 'There was an error submitting your booking request. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (showThankYou) {
    return <ThankYouPage />;
  }

  return (
    <Box className={cn("booking-confirmation-container")}>
      <Box pos="relative">
        <LoadingOverlay visible={isSubmitting} />
        <Title order={2} mb="md" c="primary">Request a Quote</Title>
        
        {configError && <ConfigurationError message={configError} />}
        
        <Paper p="lg" radius="md" withBorder mb="xl" style={{ backgroundColor: 'var(--mantine-color-dark-7)' }}>
          <form onSubmit={handleSubmit(onFormSubmit)}>
            <Grid gutter="md" mb="md">
              <Grid.Col span={{ base: 12, md: 6 }}>
                <TextInput
                  label="Full Name"
                  placeholder="Enter your full name"
                  {...register('fullName', { required: true })}
                  error={errors.fullName && 'Full name is required'}
                  required
                  styles={{
                    input: {
                      backgroundColor: 'var(--mantine-color-dark-6)',
                      borderColor: 'var(--mantine-color-dark-4)',
                      color: 'var(--mantine-color-white)'
                    }
                  }}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <TextInput
                  label="Email"
                  placeholder="Enter your email address"
                  type="email"
                  {...register('email', { required: true, pattern: /^\S+@\S+$/i })}
                  error={errors.email && 'Valid email is required'}
                  required
                  styles={{
                    input: {
                      backgroundColor: 'var(--mantine-color-dark-6)',
                      borderColor: 'var(--mantine-color-dark-4)',
                      color: 'var(--mantine-color-white)',
                      // Hide browser autofill icons
                      backgroundImage: 'none !important',
                      paddingRight: '12px !important'
                    }
                  }}
                />
              </Grid.Col>
            </Grid>
            
            <Grid mb="md">
              <Grid.Col span={{ base: 12, md: 8 }}>
                <TextInput
                  label="Phone Number"
                  placeholder="Enter your phone number"
                  type="tel"
                  {...register('phone', { required: true })}
                  error={errors.phone && 'Phone number is required'}
                  required
                  styles={{
                    input: {
                      backgroundColor: 'var(--mantine-color-dark-6)',
                      borderColor: 'var(--mantine-color-dark-4)',
                      color: 'var(--mantine-color-white)'
                    }
                  }}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 4 }} style={{ display: 'flex', alignItems: 'flex-end', paddingBottom: '8px' }}>
                <Group gap="sm" align="center">
                  <Switch
                    checked={createAccount}
                    onChange={(event) => setCreateAccount(event.currentTarget.checked)}
                    color="primary"
                    size="sm"
                  />
                  <Group gap="xs" align="center">
                    <UserPlusIcon className="w-4 h-4 text-gray-400" />
                    <Text size="sm" c="dimmed">Create Account</Text>
                  </Group>
                </Group>
              </Grid.Col>
            </Grid>
            
            <Textarea
              label="Special Requests"
              placeholder="Any special requests or instructions"
              autosize
              minRows={3}
              mb="lg"
              {...register('specialRequests')}
              styles={{
                input: {
                  backgroundColor: 'var(--mantine-color-dark-6)',
                  borderColor: 'var(--mantine-color-dark-4)',
                  color: 'var(--mantine-color-white)'
                }
              }}
            />
            
            <Accordion variant="separated" mb="xl">
              <Accordion.Item value="cancellation">
                <Accordion.Control>Cancellation Policy</Accordion.Control>
                <Accordion.Panel>
                  <CancellationPolicy />
                </Accordion.Panel>
              </Accordion.Item>
              <Accordion.Item value="driver">
                <Accordion.Control>Driver Information</Accordion.Control>
                <Accordion.Panel>
                  <DriverInfo />
                </Accordion.Panel>
              </Accordion.Item>
              <Accordion.Item value="guarantees">
                <Accordion.Control>Service Guarantees</Accordion.Control>
                <Accordion.Panel>
                  <ServiceGuarantees />
                </Accordion.Panel>
              </Accordion.Item>
            </Accordion>
            
            <Divider my="lg" />
            
            <Group justify="space-between">
              <Button 
                variant="subtle" 
                onClick={handleBack}
                disabled={isSubmitting}
              >
                Back
              </Button>
              <Button
                type="submit"
                loading={isSubmitting}
                color="primary"
              >
                Request Quote
              </Button>
            </Group>
          </form>
        </Paper>
      </Box>
    </Box>
  );
};
