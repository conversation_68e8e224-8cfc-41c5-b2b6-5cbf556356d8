import { useEffect } from 'react';
import { useBookingStore } from '../../store/bookingStore';
import { Button } from '../ui/Button';
import { CheckCircleIcon } from '@heroicons/react/24/solid';
import confetti from 'canvas-confetti';

export const ThankYouPage = () => {
  const { resetForm } = useBookingStore();

  useEffect(() => {
    // Trigger confetti animation
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 }
    });
  }, []);

  const handleStartNew = () => {
    resetForm();
  };

  return (
    <div className="min-h-[60vh] flex flex-col items-center justify-center text-center px-4">
      <div className="space-y-6 max-w-lg">
        <CheckCircleIcon className="w-20 h-20 text-primary mx-auto" />

        <div className="space-y-2">
          <h1 className="text-3xl font-light text-white">
            Quote Request Received!
          </h1>
          <p className="text-neutral-400">
            Thank you for your interest in our services. Our team will review your request
            and get back to you soon with a detailed quote.
          </p>
        </div>

        <div className="bg-surface rounded-xl p-6 text-left space-y-4">
          <h2 className="text-lg font-medium text-white">What's Next?</h2>
          <ul className="space-y-3 text-sm text-neutral-300">
            <li className="flex items-start gap-3">
              <span className="flex-shrink-0 w-6 h-6 rounded-full bg-primary/20 text-primary flex items-center justify-center">1</span>
              <span>Our team will review your trip details and prepare a customized quote</span>
            </li>
            <li className="flex items-start gap-3">
              <span className="flex-shrink-0 w-6 h-6 rounded-full bg-primary/20 text-primary flex items-center justify-center">2</span>
              <span>You'll receive an email with your quote and booking instructions</span>
            </li>
            <li className="flex items-start gap-3">
              <span className="flex-shrink-0 w-6 h-6 rounded-full bg-primary/20 text-primary flex items-center justify-center">3</span>
              <span>Review the quote and confirm your booking when ready</span>
            </li>
          </ul>
        </div>

        <div className="pt-4">
          <Button onClick={handleStartNew}>
            Request Another Quote
          </Button>
        </div>
      </div>
    </div>
  );
};
