import { useState, useEffect, useRef } from 'react';
import { useBookingStore } from '../../../store/bookingStore';
import { DateTimePicker } from './shared/DateTimePicker';
import { LocationAutocomplete } from './shared/LocationAutocomplete';
import { ToggleButton } from './shared/ToggleButton';
import {
  MapPinIcon,
  PlusIcon,
  MinusIcon,
  XMarkIcon,
  ChevronUpIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import { Switch } from '@headlessui/react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { cn, scrollFormToTop } from '@core/lib/utils';
import { cityConfigs } from '@core/config/cityConfig';
import { MapComponent } from '../MapComponent';

interface CustomLocation {
  address: string;
  coordinates: [number, number];
}

interface Stop {
  id: string;
  location: CustomLocation | null;
  isMoving?: boolean;
}

// Add at the top of the file
const generateId = () => {
  return Math.random().toString(36).substr(2, 9);
};

// Add these constants at the top
const MIN_HOURS = 2;
const MAX_HOURS = 12;
const HOUR_INCREMENTS = [2, 3, 4, 5, 6, 8, 10, 12];
const MAX_DAYS = 7;

export const HourlyForm = () => {
  const {
    hourlyData,
    updateHourlyData,
    currentStep,
    setStep
  } = useBookingStore();
  const [stops, setStops] = useState<Stop[]>(hourlyData.stops || []);
  const [pickupDate, setPickupDate] = useState(new Date());
  const [isMultiDay, setIsMultiDay] = useState(false);
  const [isStopsExpanded, setIsStopsExpanded] = useState(false);
  const formRef = useRef<HTMLDivElement>(null);

  // Add useEffect to sync stops with store
  useEffect(() => {
    updateHourlyData({ stops });
  }, [stops]);

  // Initialize default values for adults and children only
  useEffect(() => {
    if (hourlyData.adults === undefined) {
      updateHourlyData({ adults: 1 });
    }
    if (hourlyData.children === undefined) {
      updateHourlyData({ children: 0 });
    }
  }, []);

  // Next Button handler
  const handleNextStep = () => {
    console.log('HourlyForm - handleNextStep called');
    
    // If form is out of view, scroll it into view
    if (formRef.current) {
      // Get the form's position
      const rect = formRef.current.getBoundingClientRect();
      const offset = 100; // Higher offset for WordPress headers
      
      // Calculate absolute position
      const absolutePosition = window.pageYOffset + rect.top - offset;
      
      console.log('Scrolling to position:', absolutePosition);
      
      // Scroll to position
      window.scrollTo(0, absolutePosition);
    }
    
    // Proceed to next step
    setStep(currentStep + 1);
  };

  // Handle stops
  const addStop = () => {
    const newStop = {
      id: generateId(),
      location: null,
      isMoving: false
    };
    setStops(prevStops => [...prevStops, newStop]);
  };

  const removeStop = (stopId: string) => {
    const updatedStops = stops.filter(stop => stop.id !== stopId);
    setStops(updatedStops);
  };

  const updateStop = (stopId: string, location: CustomLocation) => {
    const updatedStops = stops.map(stop =>
      stop.id === stopId ? { ...stop, location } : stop
    );
    setStops(updatedStops);
  };

  const moveStop = async (fromIndex: number, toIndex: number) => {
    if (toIndex < 0 || toIndex >= stops.length) return;

    const newStops = [...stops];
    newStops[fromIndex] = { ...newStops[fromIndex], isMoving: true };
    setStops(newStops);

    await new Promise(resolve => setTimeout(resolve, 200));

    const [removed] = newStops.splice(fromIndex, 1);
    newStops.splice(toIndex, 0, { ...removed, isMoving: false });
    setStops(newStops);
    updateHourlyData({ stops: newStops });
  };

  const handleDateTimeChange = (date: Date) => {
    const timeString = date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit'
    });
    updateHourlyData({
      pickupTime: timeString,
      pickupDate: date.toISOString()
    });
  };

  const handleCarSeatsChange = (type: 'infantSeats' | 'toddlerSeats' | 'boosterSeats', value: number) => {
    updateHourlyData({ [type]: value });
  };

  const handleHourChange = (increment: boolean) => {
    const currentHours = hourlyData.hours || MIN_HOURS;
    let newHours;

    if (increment) {
      const nextIncrement = HOUR_INCREMENTS.find(h => h > currentHours);
      newHours = nextIncrement || currentHours;
    } else {
      const prevIncrement = [...HOUR_INCREMENTS].reverse().find(h => h < currentHours);
      newHours = prevIncrement || currentHours;
    }

    updateHourlyData({ hours: newHours });
  };

  const handleDaysChange = (increment: boolean) => {
    const currentDays = hourlyData.days || 1;
    const newDays = increment
      ? Math.min(currentDays + 1, MAX_DAYS)
      : Math.max(1, currentDays - 1);
    updateHourlyData({ days: newDays });
  };

  return (
    <div ref={formRef} className="space-y-6 w-full">
      {/* Row 1: Date + Hours/Days */}
      <div className="grid grid-cols-2 gap-4 relative z-[100]">
        {/* Pickup Date & Time */}
        <div>
          <h3 className="text-theme-primary text-base mb-2">Pickup Date & Time:</h3>
          <DateTimePicker
            date={hourlyData.pickupDate ? new Date(hourlyData.pickupDate) : pickupDate}
            onChange={(date) => {
              setPickupDate(date);
              handleDateTimeChange(date);
            }}
          />
        </div>

        {/* Service Duration */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-theme-primary text-base">Duration:</h3>
            <div className="flex items-center gap-2">
              <span className="text-sm text-theme-muted">Multi-days</span>
              <Switch
                checked={isMultiDay}
                onChange={(checked) => {
                  setIsMultiDay(checked);
                  updateHourlyData({
                    days: checked ? 1 : undefined,
                    hours: checked ? 24 : MIN_HOURS
                  });
                }}
                className={`toggle-button ${isMultiDay ? 'bg-primary' : ''}`}
              >
                <span className="sr-only">Enable multi-day service</span>
                <span className="toggle-thumb" />
              </Switch>
            </div>
          </div>

          {isMultiDay ? (
            // Days selector
            <div className="space-y-2">
              <div className="flex items-center bg-surface rounded-lg h-[46px]">
                <button
                  onClick={() => handleDaysChange(false)}
                  disabled={hourlyData.days === 1}
                  className="px-4 py-3 text-primary hover:text-primary/70 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <MinusIcon className="w-5 h-5" />
                </button>
                <div className="flex-1 text-center">
                  <span className="text-theme-primary font-medium">{hourlyData.days || 1}</span>
                  <span className="text-theme-muted ml-1">days</span>
                </div>
                <button
                  onClick={() => handleDaysChange(true)}
                  disabled={hourlyData.days === MAX_DAYS}
                  className="px-4 py-3 text-primary hover:text-primary/70 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <PlusIcon className="w-5 h-5" />
                </button>
              </div>
              <div className="flex items-center justify-between px-1">
                <span className="text-xs text-theme-muted">24-hour service</span>
                <span className="text-xs text-theme-muted">Up to {MAX_DAYS} days</span>
              </div>
            </div>
          ) : (
            // Hours selector
            <div className="space-y-2">
              <div className="flex items-center bg-surface rounded-lg h-[46px]">
                <button
                  onClick={() => handleHourChange(false)}
                  disabled={hourlyData.hours === MIN_HOURS}
                  className="px-4 py-3 text-primary hover:text-primary/70 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <MinusIcon className="w-5 h-5" />
                </button>
                <div className="flex-1 text-center">
                  <span className="text-theme-primary font-medium">{hourlyData.hours || MIN_HOURS}</span>
                  <span className="text-theme-muted ml-1">hours</span>
                </div>
                <button
                  onClick={() => handleHourChange(true)}
                  disabled={hourlyData.hours === MAX_HOURS}
                  className="px-4 py-3 text-primary hover:text-primary/70 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <PlusIcon className="w-5 h-5" />
                </button>
              </div>
              <div className="flex items-center justify-between px-1">
                <span className="text-xs text-theme-muted">Minimum {MIN_HOURS}h</span>
                <span className="text-xs text-theme-muted">Maximum {MAX_HOURS}h</span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Pickup Location */}
      <div className="relative z-[90]">
        <h3 className="text-theme-primary text-base mb-2">Pickup Location:</h3>
        <LocationAutocomplete
          value={hourlyData.pickupLocation?.address || ''}
          onChange={(location) => updateHourlyData({ pickupLocation: location })}
          placeholder="Enter pickup address"
          className="w-full"
          cityConfig={cityConfigs.worldwide}
        />
      </div>

      {/* Stops Section - Middle z-index */}
      <div className="relative z-[80]">
        <button
          onClick={() => setIsStopsExpanded(!isStopsExpanded)}
          className="w-full flex items-center justify-between p-4 bg-surface rounded-lg text-theme-primary hover:bg-surface-light transition-colors"
        >
          <div className="flex items-center gap-3">
            <h3 className="text-sm text-theme-primary">Stops</h3>
            <span className="text-xs text-theme-muted">{stops.length} stop{stops.length !== 1 ? 's' : ''}</span>
          </div>
          <ChevronDownIcon
            className={cn(
              "w-5 h-5 text-primary transition-transform duration-200",
              isStopsExpanded ? "rotate-180" : ""
            )}
          />
        </button>

        <div className={cn(
          "overflow-hidden transition-all duration-200",
          isStopsExpanded ? "max-h-[500px] opacity-100" : "max-h-0 opacity-0"
        )}>
          <div className="bg-surface rounded-b-lg p-6 space-y-4 border-t border-border">
            <div className="flex items-center justify-end">
              <button
                onClick={addStop}
                className="flex items-center space-x-2 text-primary hover:text-primary/70 transition-colors"
              >
                <PlusIcon className="w-5 h-5" />
                <span>Add Stop</span>
              </button>
            </div>

            <div className="space-y-2">
              {stops.map((stop, index) => (
                <div
                  key={stop.id || index}
                  className="relative z-[70] flex items-start space-x-3 bg-surface-light p-4 rounded-lg transition-all duration-300 hover:bg-surface-dark"
                >
                  <div className="flex-shrink-0 w-5 h-5 rounded-full border border-primary/60 bg-surface flex items-center justify-center mt-2">
                    <span className="text-xs text-primary">{index + 1}</span>
                  </div>
                  <div className="flex-grow">
                    <LocationAutocomplete
                      value={stop.location?.address || ''}
                      onChange={(location) => updateStop(stop.id, location)}
                      placeholder={`Stop ${index + 1}`}
                      className="w-full"
                      cityConfig={cityConfigs.worldwide}
                    />
                  </div>
                  <button
                    onClick={() => removeStop(stop.id)}
                    className="text-primary hover:text-primary/70 transition-colors"
                  >
                    <XMarkIcon className="w-5 h-5" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Row 3: Passengers and Car Seats */}
      <div className="grid grid-cols-3 gap-4">
        {/* Adults Counter (1/3) */}
        <div>
          <h3 className="text-theme-primary text-base mb-2">Adults:</h3>
          <div className="flex items-center bg-surface rounded-lg">
            <button
              onClick={() => updateHourlyData({
                adults: Math.max(1, (hourlyData.adults || 1) - 1)
              })}
              className="px-4 py-3 text-primary hover:text-primary/70 transition-colors"
            >
              <MinusIcon className="w-5 h-5" />
            </button>
            <div className="flex-1 text-center text-theme-primary">
              {hourlyData.adults || 1}
            </div>
            <button
              onClick={() => updateHourlyData({
                adults: (hourlyData.adults || 1) + 1
              })}
              className="px-4 py-3 text-primary hover:text-primary/70 transition-colors"
            >
              <PlusIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Children Counter (1/3) */}
        <div>
          <h3 className="text-theme-primary text-base mb-2">Children:</h3>
          <div className="flex items-center bg-surface rounded-lg">
            <button
              onClick={() => updateHourlyData({
                children: Math.max(0, (hourlyData.children || 0) - 1)
              })}
              className="px-4 py-3 text-primary hover:text-primary/70 transition-colors"
            >
              <MinusIcon className="w-5 h-5" />
            </button>
            <div className="flex-1 text-center text-theme-primary">
              {hourlyData.children || 0}
            </div>
            <button
              onClick={() => updateHourlyData({
                children: (hourlyData.children || 0) + 1
              })}
              className="px-4 py-3 text-primary hover:text-primary/70 transition-colors"
            >
              <PlusIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Car Seats Toggle (1/3) */}
        {(hourlyData.children || 0) > 0 && (
          <div>
            <h3 className="text-theme-primary text-base mb-2">Car Seats</h3>
            <div className="h-[46px] flex items-center justify-between bg-surface rounded-lg px-4">
              <ToggleButton
                checked={hourlyData.needCarSeats}
                onChange={(checked) => updateHourlyData({ needCarSeats: checked })}
              />
            </div>
          </div>
        )}
      </div>

      {/* Car Seats Options - Shows when toggle is on */}
      {(hourlyData.children || 0) > 0 && hourlyData.needCarSeats && (
        <div className="grid grid-cols-3 gap-4 mt-4">
          {/* Infant Seats */}
          <div className="bg-surface p-4 rounded-lg">
            <div className="text-theme-primary mb-1">Infant Seats</div>
            <div className="text-sm text-theme-muted mb-3">0-1 years</div>
            <div className="flex items-center justify-between">
              <button
                onClick={() => handleCarSeatsChange('infantSeats', Math.max(0, (hourlyData.infantSeats || 0) - 1))}
                className="text-primary hover:text-primary/70 transition-colors"
              >
                <MinusIcon className="w-5 h-5" />
              </button>
              <span className="text-theme-primary">{hourlyData.infantSeats || 0}</span>
              <button
                onClick={() => handleCarSeatsChange('infantSeats', (hourlyData.infantSeats || 0) + 1)}
                className="text-primary hover:text-primary/70 transition-colors"
              >
                <PlusIcon className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Toddler Seats */}
          <div className="bg-surface p-4 rounded-lg">
            <div className="text-theme-primary mb-1">Toddler Seats</div>
            <div className="text-sm text-theme-muted mb-3">1-3 years</div>
            <div className="flex items-center justify-between">
              <button
                onClick={() => handleCarSeatsChange('toddlerSeats', Math.max(0, (hourlyData.toddlerSeats || 0) - 1))}
                className="text-primary hover:text-primary/70 transition-colors"
              >
                <MinusIcon className="w-5 h-5" />
              </button>
              <span className="text-theme-primary">{hourlyData.toddlerSeats || 0}</span>
              <button
                onClick={() => handleCarSeatsChange('toddlerSeats', (hourlyData.toddlerSeats || 0) + 1)}
                className="text-primary hover:text-primary/70 transition-colors"
              >
                <PlusIcon className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Booster Seats */}
          <div className="bg-surface p-4 rounded-lg">
            <div className="text-theme-primary mb-1">Booster Seats</div>
            <div className="text-sm text-theme-muted mb-3">4-7 years</div>
            <div className="flex items-center justify-between">
              <button
                onClick={() => handleCarSeatsChange('boosterSeats', Math.max(0, (hourlyData.boosterSeats || 0) - 1))}
                className="text-primary hover:text-primary/70 transition-colors"
              >
                <MinusIcon className="w-5 h-5" />
              </button>
              <span className="text-theme-primary">{hourlyData.boosterSeats || 0}</span>
              <button
                onClick={() => handleCarSeatsChange('boosterSeats', (hourlyData.boosterSeats || 0) + 1)}
                className="text-primary hover:text-primary/70 transition-colors"
              >
                <PlusIcon className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add back Drop-off Location */}
      <div className="relative z-20">
        <h3 className="text-theme-primary text-base mb-2">Drop-off Location:</h3>
        <LocationAutocomplete
          value={hourlyData.dropoffLocation?.address || ''}
          onChange={(location) => updateHourlyData({ dropoffLocation: location })}
          placeholder="Enter drop-off address"
          className="w-full"
          cityConfig={cityConfigs.worldwide}
        />
      </div>

      {/* Next Button */}
      <div className="flex justify-end pt-6">
        <button
          onClick={handleNextStep}
          className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-light transition-colors"
        >
          Next: Select Vehicle
        </button>
      </div>
    </div>
  );
};
