// src/components/BookingForm/Forms/shared/PassengerCounter.tsx
import { MinusIcon, PlusIcon } from '@heroicons/react/24/outline';

interface PassengerCounterProps {
  label: string;
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
}

export const PassengerCounter = ({ label, value, onChange, min = 0, max = 99 }: PassengerCounterProps) => {
  const handleIncrement = () => {
    if (value < max) onChange(value + 1);
  };

  const handleDecrement = () => {
    if (value > min) onChange(value - 1);
  };

  return (
    <div>
      <h3 className="text-white text-base mb-2">{label}</h3>
      <div className="flex items-center bg-[#1A1A1A] rounded-lg">
        <button
          onClick={handleDecrement}
          disabled={value <= min}
          className="p-3 text-white/70 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <MinusIcon className="w-5 h-5" />
        </button>
        <div className="flex-1 text-center text-white text-lg font-medium">
          {value}
        </div>
        <button
          onClick={handleIncrement}
          disabled={value >= max}
          className="p-3 text-white/70 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <PlusIcon className="w-5 h-5" />
        </button>
      </div>
    </div>
  );
};