.datepicker-popper {
  z-index: 9999 !important;
  margin-top: 8px !important;
}

/* Create a portal container */
#datepicker-portal {
  position: relative;
  z-index: 9999;
}

/* Base calendar container */
.react-datepicker {
  font-family: inherit !important;
  border-radius: 8px !important;
  border: 1px solid var(--border) !important;
  background-color: var(--surface) !important;
  color: var(--text-primary) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  isolation: isolate !important;
}

/* Header styling */
.react-datepicker__header {
  background-color: var(--surface) !important;
  border-bottom: 1px solid var(--border) !important;
  padding: 1rem !important;
}

.react-datepicker__current-month,
.react-datepicker-time__header,
.react-datepicker__day-name {
  color: var(--text-primary) !important;
}

.react-datepicker__current-month,
.react-datepicker-time__header {
  font-size: 1rem !important;
  font-weight: 500 !important;
}

.react-datepicker__day-names {
  margin-top: 0.5rem !important;
  background-color: var(--surface) !important;
}

.react-datepicker__day-name {
  width: 2rem !important;
  line-height: 2rem !important;
  margin: 0.166rem !important;
  color: var(--text-muted) !important;
}

/* Navigation buttons */
.react-datepicker__navigation {
  top: 1rem !important;
}

.react-datepicker__navigation-icon::before {
  border-color: var(--text-muted) !important;
  border-width: 2px 2px 0 0 !important;
  height: 8px !important;
  width: 8px !important;
}

.react-datepicker__navigation:hover *::before {
  border-color: var(--text-primary) !important;
}

/* Month container */
.react-datepicker__month {
  margin: 0 !important;
  padding: 0 1rem 1rem !important;
  background-color: var(--surface) !important;
}

.react-datepicker__month-container {
  background-color: var(--surface) !important;
}

/* Week container */
.react-datepicker__week {
  background-color: var(--surface) !important;
}

/* Days styling */
.react-datepicker__day {
  color: var(--text-primary) !important;
  width: 2rem !important;
  line-height: 2rem !important;
  margin: 0.166rem !important;
  border-radius: 9999px !important;
  background-color: var(--surface) !important;
  transition: background-color 0.2s ease, color 0.2s ease !important;
}

.react-datepicker__day:not(.react-datepicker__day--selected):hover {
  background-color: var(--primary-transparent) !important;
  color: white !important;
}

.react-datepicker__day.react-datepicker__day--selected {
  background-color: var(--primary) !important;
  color: white !important;
  font-weight: 500 !important;
}

.react-datepicker__day.react-datepicker__day--selected:hover {
  background-color: var(--primary) !important;
  color: white !important;
}

.react-datepicker__day.react-datepicker__day--keyboard-selected:not(.react-datepicker__day--selected) {
  background-color: var(--primary-transparent) !important;
  color: white !important;
}

.react-datepicker__day--disabled,
.react-datepicker__day--outside-month {
  color: var(--text-disabled) !important;
  background-color: var(--surface) !important;
}

/* Time container */
.react-datepicker__time-container {
  border-left: 1px solid var(--border) !important;
  width: 85px !important;
  background-color: var(--surface) !important;
}

.react-datepicker__time {
  background-color: var(--surface) !important;
}

.react-datepicker__header--time {
  padding: 0.5rem !important;
  background-color: var(--surface) !important;
}

.react-datepicker__time-list {
  height: 200px !important;
  overflow-y: auto !important;
  background-color: var(--surface) !important;
}

.react-datepicker__time-list-item {
  color: var(--text-primary) !important;
  padding: 0.5rem !important;
  height: auto !important;
  line-height: 1.25 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: var(--surface) !important;
  transition: background-color 0.2s ease, color 0.2s ease !important;
}

.react-datepicker__time-list-item:not(.react-datepicker__time-list-item--selected):hover {
  background-color: var(--primary-transparent) !important;
  color: white !important;
}

.react-datepicker__time-list-item.react-datepicker__time-list-item--selected {
  background-color: var(--primary) !important;
  color: white !important;
  font-weight: 500 !important;
}

.react-datepicker__time-list-item.react-datepicker__time-list-item--selected:hover {
  background-color: var(--primary) !important;
  color: white !important;
}

/* Today's date */
.react-datepicker__day--today {
  font-weight: 500 !important;
  position: relative !important;
  background-color: var(--surface) !important;
  border: 1px solid var(--primary-transparent) !important;
}

.react-datepicker__day--today::after {
  content: "" !important;
  position: absolute !important;
  bottom: 4px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 4px !important;
  height: 4px !important;
  border-radius: 50% !important;
  background-color: var(--primary) !important;
}

/* Scrollbar styling */
.react-datepicker__time-list::-webkit-scrollbar {
  width: 6px !important;
}

.react-datepicker__time-list::-webkit-scrollbar-track {
  background: var(--surface) !important;
}

.react-datepicker__time-list::-webkit-scrollbar-thumb {
  background-color: var(--border) !important;
  border-radius: 3px !important;
}

/* Hide triangle pointer */
.react-datepicker__triangle {
  display: none !important;
}

/* Input styling */
.datepicker-wrapper input {
  width: 100% !important;
  background-color: var(--surface) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--border) !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem 1rem 0.75rem 2.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  outline: none !important;
}

.datepicker-wrapper input:focus {
  border-color: var(--primary) !important;
  box-shadow: 0 0 0 2px var(--primary-transparent) !important;
}

/* Dark theme overrides */
.dark-theme .react-datepicker {
  background-color: var(--surface-dark) !important;
  border-color: var(--border) !important;
}

.dark-theme .react-datepicker__header {
  background-color: var(--surface-dark) !important;
  border-color: var(--border) !important;
}

.dark-theme .react-datepicker__day {
  background-color: var(--surface-dark) !important;
  color: var(--text-primary) !important;
}

.dark-theme .react-datepicker__day-name {
  color: var(--text-muted) !important;
}

.dark-theme .react-datepicker__current-month,
.dark-theme .react-datepicker-time__header {
  color: var(--text-primary) !important;
}

.dark-theme .react-datepicker__month,
.dark-theme .react-datepicker__month-container,
.dark-theme .react-datepicker__week,
.dark-theme .react-datepicker__day-names {
  background-color: var(--surface-dark) !important;
}

.dark-theme .react-datepicker__time-container,
.dark-theme .react-datepicker__time,
.dark-theme .react-datepicker__header--time,
.dark-theme .react-datepicker__time-list {
  background-color: var(--surface-dark) !important;
}

.dark-theme .react-datepicker__time-list-item {
  background-color: var(--surface-dark) !important;
  color: var(--text-primary) !important;
}

.dark-theme .react-datepicker__day--disabled,
.dark-theme .react-datepicker__day--outside-month {
  color: var(--text-disabled) !important;
  background-color: var(--surface-dark) !important;
}

.dark-theme .react-datepicker__day:not(.react-datepicker__day--selected):hover {
  background-color: var(--primary-transparent) !important;
  color: white !important;
}

.dark-theme .react-datepicker__day.react-datepicker__day--selected {
  background-color: var(--primary) !important;
  color: white !important;
}

.dark-theme .react-datepicker__day.react-datepicker__day--selected:hover {
  background-color: var(--primary) !important;
  color: white !important;
}

.dark-theme .react-datepicker__time-list-item:not(.react-datepicker__time-list-item--selected):hover {
  background-color: var(--primary-transparent) !important;
  color: white !important;
}

.dark-theme .react-datepicker__navigation-icon::before {
  border-color: var(--text-muted) !important;
}

.dark-theme .react-datepicker__navigation:hover *::before {
  border-color: var(--text-primary) !important;
}

.dark-theme .react-datepicker__day--today {
  background-color: var(--surface-dark) !important;
  border: 1px solid var(--primary-transparent) !important;
}

.dark-theme .react-datepicker__day--today::after {
  background-color: var(--primary) !important;
}

.dark-theme .react-datepicker__time-list::-webkit-scrollbar-track {
  background: var(--surface-dark) !important;
}

.dark-theme .react-datepicker__time-list::-webkit-scrollbar-thumb {
  background-color: var(--border) !important;
}

.dark-theme .react-datepicker__time-list-item.react-datepicker__time-list-item--selected {
  background-color: var(--primary) !important;
  color: white !important;
  font-weight: 500 !important;
}

.dark-theme .react-datepicker__time-list-item.react-datepicker__time-list-item--selected:hover {
  background-color: var(--primary) !important;
  color: white !important;
}

.dark-theme .react-datepicker__day.react-datepicker__day--keyboard-selected:not(.react-datepicker__day--selected) {
  background-color: var(--primary-transparent) !important;
  color: white !important;
}