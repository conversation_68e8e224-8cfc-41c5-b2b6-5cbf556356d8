import { useBookingStore } from '../../store/bookingStore';
import { ChevronLeftIcon } from '@heroicons/react/24/outline';
import { createPortal } from 'react-dom';
import { scrollFormToTop } from '@core/lib/utils';

export const MobileStepper = () => {
  const { currentStep, setStep } = useBookingStore();

  const steps = [
    { id: 1, label: 'Trip Details' },
    { id: 2, label: 'Select Vehicle' },
    { id: 3, label: 'Request Quote' }
  ];

  const handleBack = () => {
    if (currentStep > 1) {
      setStep(currentStep - 1);
      setTimeout(() => scrollFormToTop(), 10);
    }
  };

  const content = (
    <div className="mobile-stepper lg:hidden">
      <div className="max-w-lg mx-auto px-4 py-4">
        {/* Progress Bar */}
        <div className="relative h-1 bg-white/10 rounded-full mb-4">
          <div
            className="absolute left-0 top-0 h-full bg-primary rounded-full transition-all duration-300"
            style={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
          />
        </div>

        <div className="flex items-center justify-between">
          {/* Back Button */}
          <button
            onClick={handleBack}
            className={`p-2 rounded-lg transition-colors ${currentStep === 1
              ? 'text-white/20 cursor-not-allowed'
              : 'text-white hover:bg-white/5'
              }`}
            disabled={currentStep === 1}
          >
            <ChevronLeftIcon className="w-5 h-5" />
          </button>

          {/* Step Indicator */}
          <div className="text-center">
            <p className="text-sm text-neutral-400">
              Step {currentStep} of {steps.length}
            </p>
            <p className="text-white font-medium">
              {steps[currentStep - 1].label}
            </p>
          </div>

          {/* Step Dots */}
          <div className="flex gap-1">
            {steps.map((step) => (
              <div
                key={step.id}
                className={`w-2 h-2 rounded-full transition-colors ${step.id === currentStep
                  ? 'bg-primary'
                  : step.id < currentStep
                    ? 'bg-primary/40'
                    : 'bg-white/20'
                  }`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return createPortal(content, document.body);
};
