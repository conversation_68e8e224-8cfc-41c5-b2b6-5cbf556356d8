/**
 * Diagnostics Service
 * 
 * This file contains diagnostic tools to help identify issues with the application,
 * including duplicate functions, conflicting implementations, and deployment verification.
 */

const DIAGNOSTICS_VERSION = "1.0.0";
console.log(`[Diagnostics] Loading diagnostics service version ${DIAGNOSTICS_VERSION}`);

/**
 * Checks for duplicate functions in the global scope
 * This helps identify if multiple versions of the same function are loaded
 */
export const checkForDuplicateFunctions = () => {
  console.log('[Diagnostics] Running duplicate function check');
  
  // List of critical functions to check
  const functionsToCheck = [
    'sendConfirmationEmail',
    'getPickupCity',
    'getCityFromCoordinates',
    'searchLocations',
    'getPlaceDetails'
  ];
  
  const results: Record<string, { count: number, sources: string[] }> = {};
  
  // Check for functions in the global scope
  functionsToCheck.forEach(funcName => {
    try {
      // @ts-ignore - Dynamically checking global scope
      const globalFuncs = window[funcName] ? [window[funcName]] : [];
      
      // Check for functions in modules that might be loaded
      const moduleFuncs: any[] = [];
      
      // Try to access common module paths where these functions might exist
      const modulePaths = [
        'api',
        'hereApi',
        'services/api',
        'services/hereApi',
        'core/services/api',
        'core/services/hereApi'
      ];
      
      modulePaths.forEach(path => {
        try {
          // @ts-ignore - Dynamically checking modules
          const module = window[path] || {};
          if (module[funcName]) {
            moduleFuncs.push(module[funcName]);
          }
        } catch (e) {
          // Ignore errors when checking modules
        }
      });
      
      // Combine all found implementations
      const allImplementations = [...globalFuncs, ...moduleFuncs];
      
      // Get the source code for each implementation to compare
      const sources = allImplementations.map(func => {
        if (typeof func === 'function') {
          return func.toString().substring(0, 100) + '...'; // First 100 chars as identifier
        }
        return 'not a function';
      });
      
      // Remove duplicates by comparing source code
      const uniqueSources = [...new Set(sources)];
      
      results[funcName] = {
        count: uniqueSources.length,
        sources: uniqueSources
      };
      
      console.log(`[Diagnostics] Function "${funcName}" has ${uniqueSources.length} unique implementations`);
      if (uniqueSources.length > 1) {
        console.warn(`[Diagnostics] ⚠️ DUPLICATE DETECTED: "${funcName}" has multiple implementations!`);
        console.log(`[Diagnostics] Sources for "${funcName}":`, uniqueSources);
      }
    } catch (error) {
      console.error(`[Diagnostics] Error checking function "${funcName}":`, error);
    }
  });
  
  return results;
};

/**
 * Checks the environment and loaded modules
 * This helps identify what version of the code is running
 */
export const checkEnvironment = () => {
  console.log('[Diagnostics] Running environment check');
  
  const environment = {
    timestamp: new Date().toISOString(),
    url: typeof window !== 'undefined' ? window.location.href : 'Not in browser',
    hostname: typeof window !== 'undefined' ? window.location.hostname : 'Not in browser',
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Not in browser',
    isWordPress: false,
    apiEndpoint: '',
    loadedModules: [] as string[]
  };
  
  // Check if we're in WordPress
  if (typeof window !== 'undefined') {
    // Check for WordPress-specific globals
    environment.isWordPress = !!(
      // @ts-ignore - Checking for WordPress globals
      window.wp || 
      document.body.classList.contains('wp-admin') || 
      document.getElementById('wpadminbar')
    );
    
    // Check what API endpoint we're using
    try {
      const apiBaseElement = document.querySelector('meta[name="api-base-url"]');
      if (apiBaseElement) {
        // @ts-ignore - Getting content attribute
        environment.apiEndpoint = apiBaseElement.content;
      } else {
        environment.apiEndpoint = '/wp-json/limo-booking/v1/proxy';
      }
    } catch (e) {
      environment.apiEndpoint = 'Error detecting API endpoint';
    }
    
    // Check for loaded modules
    try {
      // @ts-ignore - Checking loaded scripts
      const scripts = document.querySelectorAll('script[src]');
      scripts.forEach((script: HTMLScriptElement) => {
        if (script.src) {
          environment.loadedModules.push(script.src);
        }
      });
    } catch (e) {
      console.error('[Diagnostics] Error checking loaded scripts:', e);
    }
  }
  
  console.log('[Diagnostics] Environment:', environment);
  return environment;
};

/**
 * Run all diagnostics
 */
export const runAllDiagnostics = () => {
  console.log('[Diagnostics] Running all diagnostics');
  
  const results = {
    environment: checkEnvironment(),
    duplicateFunctions: checkForDuplicateFunctions()
  };
  
  console.log('[Diagnostics] All diagnostics complete:', results);
  return results;
};

// Auto-run diagnostics when this module is loaded
if (typeof window !== 'undefined') {
  // Wait for the page to fully load
  window.addEventListener('load', () => {
    console.log('[Diagnostics] Page loaded, running diagnostics');
    setTimeout(runAllDiagnostics, 1000); // Run after 1 second to ensure everything is loaded
  });
} 