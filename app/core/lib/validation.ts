import * as z from 'zod';

export const contactInfoSchema = z.object({
  name: z.string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters'),
  email: z.string()
    .email('Please enter a valid email address'),
  phone: z.string()
    .regex(/^\+?[1-9]\d{1,14}$/, 'Please enter a valid phone number'),
  specialRequests: z.array(z.object({
    type: z.string(),
    details: z.string()
  })).optional()
});

export const quoteRequestSchema = z.object({
  bookingType: z.enum(['point-to-point', 'by-the-hour', 'airport']),
  pickupDate: z.string().min(1, 'Pickup date is required'),
  pickupTime: z.string().min(1, 'Pickup time is required'),
  pickupLocation: z.object({
    address: z.string().min(1, 'Pickup location is required'),
    coordinates: z.tuple([z.number(), z.number()])
  }),
  dropoffLocation: z.object({
    address: z.string().min(1, 'Dropoff location is required'),
    coordinates: z.tuple([z.number(), z.number()])
  }).optional(),
  adults: z.number().min(1, 'At least 1 adult is required'),
  children: z.number().min(0),
  returnType: z.enum(['oneWay', 'return']),
  selectedVehicle: z.string().min(1, 'Please select a vehicle'),
  contactInfo: contactInfoSchema
});

export type QuoteRequest = z.infer<typeof quoteRequestSchema>;
