export type BookingType = 'point-to-point' | 'hourly' | 'airport' | 'multi-day';

export interface Location {
  address: string;
  coordinates: [number, number];
}

export interface Stop {
  id: string;
  location: Location | null;
}

export interface BaseBookingData {
  pickupLocation: Location | null;
  pickupDate: string | null;
  pickupTime: string | null;
  adults: number;
  children: number;
  stops: Stop[];
  selectedVehicle: string | null;
  infantSeats: number;
  toddlerSeats: number;
  boosterSeats: number;
  needCarSeats: boolean;
}

export interface PointToPointData extends BaseBookingData {
  dropoffLocation: Location | null;
  returnType: string;
}

export interface HourlyData extends BaseBookingData {
  hours: number;
}

export interface AirportData extends BaseBookingData {
  dropoffLocation: Location | null;
  departureFlight: string | null;
  returnFlight: string | null;
  returnDate: string | null;
  returnTime: string | null;
  isRoundTrip: boolean;
}

export interface DayData {
  id: string;
  pickupLocation: Location | null;
  dropoffLocation: Location | null;
  pickupDate: string | null;
  pickupTime: string | null;
  stops: Stop[];
  isCollapsed: boolean;
}

export interface MultiDayData extends Omit<BaseBookingData, 'pickupLocation' | 'pickupDate' | 'pickupTime'> {
  days: DayData[];
  totalDays: number;
  startDate: string | null;
  startTime: string | null;
}

export type FlightInfo = {
  airline?: string;
  flightNumber?: string;
  terminal?: string;
};

export type BookingData = {
  bookingType: BookingType;
  pickupDate: string;
  pickupTime: string;
  pickupLocation?: Location;
  dropoffLocation?: Location;
  adults: number;
  children: number;
  hourlyDuration?: number;
  flightInfo?: FlightInfo;
  selectedVehicle?: string;
};

export type BookingStore = {
  activeTab: BookingType;
  currentStep: number;
  bookingData: BookingData;
  setTab: (tab: BookingType) => void;
  setStep: (step: number) => void;
  updateBookingData: (data: Partial<BookingData>) => void;
  resetForm: () => void;
};
