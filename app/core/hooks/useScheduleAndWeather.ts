import { useState, useEffect } from 'react';
import { config } from '@core/config/env';
import { generateUUID } from '@core/utils/uuid';

interface ScheduledPickup {
  id: string;
  frequency: 'daily' | 'weekly' | 'monthly';
  pickupLocation: string;
  dropoffLocation: string;
  time: string;
  days?: string[];
}

interface WeatherInfo {
  temperature: number;
  condition: string;
  icon: string;
}

export const useScheduleAndWeather = () => {
  const [scheduledPickups, setScheduledPickups] = useState<ScheduledPickup[]>([]);
  const [weather, setWeather] = useState<WeatherInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const addScheduledPickup = (pickup: Omit<ScheduledPickup, 'id'>) => {
    const newPickup = {
      ...pickup,
      id: generateUUID(),
    };
    setScheduledPickups(prev => [...prev, newPickup]);
    localStorage.setItem('scheduled-pickups', JSON.stringify([...scheduledPickups, newPickup]));
  };

  const fetchWeather = async (date: Date, location: string) => {
    if (!config.weatherApiKey) {
      setError('Weather API key is not configured');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(
        `https://api.openweathermap.org/data/2.5/forecast?q=${encodeURIComponent(location)}&appid=${config.weatherApiKey}&units=metric`
      );

      if (!response.ok) {
        throw new Error('Failed to fetch weather data');
      }

      const data = await response.json();
      
      setWeather({
        temperature: data.main.temp,
        condition: data.weather[0].main,
        icon: data.weather[0].icon,
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch weather data');
    } finally {
      setLoading(false);
    }
  };

  // Load scheduled pickups from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('scheduled-pickups');
    if (saved) {
      try {
        setScheduledPickups(JSON.parse(saved));
      } catch (err) {
        console.error('Failed to load scheduled pickups:', err);
      }
    }
  }, []);

  return { 
    scheduledPickups, 
    addScheduledPickup, 
    weather, 
    fetchWeather,
    loading,
    error 
  };
};
