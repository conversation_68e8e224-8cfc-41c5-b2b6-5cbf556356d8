import { useState, useRef, useEffect } from 'react';
import { bookingDebugger } from '@core/utils/debugLogger';
import { format, parseISO, isValid } from 'date-fns';

interface ScheduledTime {
  utc: string;
  local: string;
}

interface FlightDetails {
  flightNumber: string;
  departure: {
    airport: string;
    terminal?: string;
    gate?: string;
    scheduled: ScheduledTime;
  };
  arrival: {
    airport: string;
    terminal?: string;
    gate?: string;
    scheduled: ScheduledTime;
  };
  status: {
    text: string;
    color: string;
  };
  airline: {
    name: string;
    iata: string;
  };
}

interface AeroDataBoxFlight {
  number: string;
  airline: {
    name: string;
    iata: string;
  };
  departure: {
    airport: {
      iata: string;
    };
    terminal?: string;
    gate?: string;
    scheduledTime: ScheduledTime;
  };
  arrival: {
    airport: {
      iata: string;
    };
    terminal?: string;
    gate?: string;
    scheduledTime: ScheduledTime;
  };
  status?: {
    text: string;
  };
}

type AeroDataBoxResponse = AeroDataBoxFlight[];

// Rate limiting configuration
const RATE_LIMIT_DELAY = 5000; // Increased to 5 seconds between requests
const MAX_RETRIES = 3; // Reduced max retries to avoid excessive attempts
const BACKOFF_MULTIPLIER = 2;
const MIN_RETRY_DELAY = 5000; // Minimum 5 seconds
const MAX_RETRY_DELAY = 30000; // Maximum 30 seconds
const DEBOUNCE_DELAY = 1000; // 1 second debounce

export const useFlightTracking = () => {
  const [flightInfo, setFlightInfo] = useState<FlightDetails | null>(null);
  const [loading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const lastRequestTime = useRef<number>(0);
  const pendingRequest = useRef<AbortController | null>(null);
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null);

  // Add debounced track flight function
  const debouncedTrackFlight = (flightNumber: string, date?: Date | null) => {
    // Clear any existing timeout
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }

    // Set new timeout
    debounceTimeout.current = setTimeout(() => {
      trackFlight(flightNumber, date);
    }, DEBOUNCE_DELAY);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }
      if (pendingRequest.current) {
        pendingRequest.current.abort();
      }
    };
  }, []);

  const formatDateForDisplay = (scheduledTime: ScheduledTime | null | undefined): string => {
    if (!scheduledTime) {
      bookingDebugger.log('debug', 'Date Formatting', 'No scheduled time provided');
      return 'Date not available';
    }

    try {
      // Log the raw scheduled time for debugging
      bookingDebugger.log('debug', 'Date Formatting', 'Raw scheduled time', { 
        utc: scheduledTime.utc,
        local: scheduledTime.local
      });

      // Extract the local time without the timezone offset
      // Format: "2025-02-16 08:15-07:00" -> "2025-02-16 08:15"
      const match = scheduledTime.local.match(/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2})/);
      if (!match) {
        bookingDebugger.log('error', 'Date Formatting', 'Invalid local time format', { 
          localTime: scheduledTime.local 
        });
        
        // Try UTC time as fallback
        const utcMatch = scheduledTime.utc.match(/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2})/);
        if (!utcMatch) {
          bookingDebugger.log('error', 'Date Formatting', 'Invalid UTC time format', { 
            utcTime: scheduledTime.utc 
          });
          return 'Date not available';
        }
        
        const utcDateStr = utcMatch[1] + ':00';
        const parsedUtcDate = parseISO(utcDateStr);
        
        if (!isValid(parsedUtcDate)) {
          bookingDebugger.log('error', 'Date Formatting', 'Invalid UTC date after parsing', { 
            utcDateStr 
          });
          return 'Date not available';
        }
        
        const formattedDate = format(parsedUtcDate, 'MMM d, yyyy h:mm a');
        bookingDebugger.log('debug', 'Date Formatting', 'Formatted using UTC time', { 
          original: scheduledTime.utc,
          parsed: utcDateStr,
          formatted: formattedDate 
        });
        return formattedDate;
      }

      const localDateStr = match[1] + ':00';
      const parsedLocalDate = parseISO(localDateStr);
      
      if (!isValid(parsedLocalDate)) {
        bookingDebugger.log('error', 'Date Formatting', 'Invalid local date after parsing', { 
          localDateStr 
        });
        return 'Date not available';
      }

      const formattedDate = format(parsedLocalDate, 'MMM d, yyyy h:mm a');
      bookingDebugger.log('debug', 'Date Formatting', 'Formatted using local time', { 
        original: scheduledTime.local,
        parsed: localDateStr,
        formatted: formattedDate 
      });
      return formattedDate;
    } catch (err) {
      bookingDebugger.log('error', 'Date Formatting', 'Failed to format date', {
        scheduledTime,
        error: err instanceof Error ? err.message : 'Unknown error'
      });
      return 'Date not available';
    }
  };

  const trackFlight = async (flightNumber: string, date?: Date | null) => {
    // Don't proceed if the flight number is too short
    if (flightNumber.length < 3) {
      return;
    }

    // Cancel any pending request
    if (pendingRequest.current) {
      pendingRequest.current.abort();
      pendingRequest.current = null;
    }

    setIsLoading(true);
    setError(null);
    setFlightInfo(null);

    // Create new AbortController
    const controller = new AbortController();
    pendingRequest.current = controller;
    const signal = controller.signal;

    try {
      // Rate limiting check
      const now = Date.now();
      const timeSinceLastRequest = now - lastRequestTime.current;
      if (timeSinceLastRequest < RATE_LIMIT_DELAY) {
        const waitTime = RATE_LIMIT_DELAY - timeSinceLastRequest;
        bookingDebugger.log('debug', 'Flight Tracking', `Waiting ${waitTime}ms before request`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }

      // Clean and validate flight number
      const cleanFlightNumber = flightNumber.trim().toUpperCase();
      const match = cleanFlightNumber.match(/^([A-Z]{2,3})\s*(\d{1,4})$/);
      
      if (!match) {
        throw new Error('Invalid flight number format. Example: AA123');
      }

      const standardizedFlightNumber = `${match[1]}${match[2]}`;
      let retryCount = 0;
      let lastError = null;

      while (retryCount < MAX_RETRIES) {
        try {
          const options = {
            method: 'GET',
            headers: {
              'X-RapidAPI-Key': import.meta.env.VITE_RAPIDAPI_KEY,
              'X-RapidAPI-Host': import.meta.env.VITE_RAPIDAPI_HOST,
              'Accept': 'application/json',
              'Cache-Control': 'no-cache'
            },
            signal
          };

          const formattedDate = date ? format(date, 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd');
          const url = `https://${import.meta.env.VITE_RAPIDAPI_HOST}/flights/number/${standardizedFlightNumber}/${formattedDate}`;

          bookingDebugger.log('debug', 'Flight Tracking', 'Attempting request', {
            attempt: retryCount + 1,
            flightNumber: standardizedFlightNumber,
            date: formattedDate,
            timeSinceLastRequest: Date.now() - lastRequestTime.current
          });

          const response = await fetch(url, options);
          lastRequestTime.current = Date.now();

          if (!response.ok) {
            if (response.status === 429) {
              const retryAfter = response.headers.get('Retry-After');
              const baseDelay = retryAfter ? parseInt(retryAfter) * 1000 : 
                Math.min(
                  Math.max(
                    MIN_RETRY_DELAY * Math.pow(BACKOFF_MULTIPLIER, retryCount),
                    MIN_RETRY_DELAY
                  ),
                  MAX_RETRY_DELAY
                );
              
              const jitter = Math.random() * 1000;
              const delay = baseDelay + jitter;

              bookingDebugger.log('debug', 'Flight Tracking', 'Rate limited, retrying', {
                attempt: retryCount + 1,
                baseDelay,
                actualDelay: delay,
                retryAfter,
                nextAttemptIn: new Date(Date.now() + delay).toISOString()
              });

              await new Promise(resolve => setTimeout(resolve, delay));
              retryCount++;
              continue;
            }

            if (response.status === 404) {
              throw new Error('Flight not found. Please check the flight number and date.');
            }

            const errorText = await response.text();
            throw new Error(`API request failed: ${response.status} - ${errorText || response.statusText}`);
          }

          const responseText = await response.text();
          bookingDebugger.log('debug', 'Flight Tracking', 'Raw API response', { responseText });

          if (!responseText) {
            throw new Error('Empty response from API');
          }

          let flights: AeroDataBoxResponse;
          try {
            flights = JSON.parse(responseText);
            bookingDebugger.log('debug', 'Flight Tracking', 'Parsed API response', { 
              flights,
              flightCount: flights.length
            });
          } catch (err) {
            bookingDebugger.log('error', 'Flight Tracking', 'Failed to parse JSON response', {
              responseText,
              error: err instanceof Error ? err.message : 'Unknown error'
            });
            throw new Error('Invalid response format from API');
          }

          if (!flights || flights.length === 0) {
            throw new Error('No flight information found for the specified date.');
          }

          // Find the exact flight number match
          const flight = flights.find(f => 
            f.number.replace(/\s+/, '') === standardizedFlightNumber
          );
          
          if (!flight) {
            bookingDebugger.log('error', 'Flight Tracking', 'No exact flight number match', {
              requested: cleanFlightNumber,
              standardized: standardizedFlightNumber,
              available: flights.map(f => f.number)
            });
            throw new Error(`Flight ${cleanFlightNumber} not found for the specified date.`);
          }

          // Log raw date values for debugging
          bookingDebugger.log('debug', 'Flight Tracking', 'Raw flight dates', {
            flightNumber: flight.number,
            departure: flight.departure.scheduledTime,
            arrival: flight.arrival.scheduledTime
          });

          // Ensure we have valid scheduled times
          if (!flight.departure.scheduledTime || !flight.arrival.scheduledTime) {
            throw new Error('Flight schedule times are missing from the API response');
          }

          const flightDetails: FlightDetails = {
            flightNumber: cleanFlightNumber,
            departure: {
              airport: flight.departure.airport.iata,
              terminal: flight.departure.terminal,
              gate: flight.departure.gate,
              scheduled: flight.departure.scheduledTime
            },
            arrival: {
              airport: flight.arrival.airport.iata,
              terminal: flight.arrival.terminal,
              gate: flight.arrival.gate,
              scheduled: flight.arrival.scheduledTime
            },
            status: {
              text: getFlightStatus(flight.status?.text || 'scheduled'),
              color: getStatusColor(flight.status?.text || 'scheduled')
            },
            airline: {
              name: flight.airline.name,
              iata: flight.airline.iata
            }
          };

          // Validate the dates before setting the flight info
          const departureDate = formatDateForDisplay(flightDetails.departure.scheduled);
          const arrivalDate = formatDateForDisplay(flightDetails.arrival.scheduled);

          if (departureDate === 'Date not available' || arrivalDate === 'Date not available') {
            bookingDebugger.log('error', 'Flight Tracking', 'Invalid dates in API response', {
              departure: flightDetails.departure.scheduled,
              arrival: flightDetails.arrival.scheduled
            });
            throw new Error('Invalid flight schedule times received from API');
          }

          setFlightInfo(flightDetails);
          bookingDebugger.log('info', 'Flight Tracking', 'Flight details updated successfully', {
            flightDetails,
            formattedDeparture: departureDate,
            formattedArrival: arrivalDate
          });

          return;
        } catch (err) {
          lastError = err;
          
          if (err instanceof Error && err.name === 'AbortError') {
            bookingDebugger.log('debug', 'Flight Tracking', 'Request aborted');
            return;
          }

          // Handle network errors
          if (err instanceof Error && (
            err.name === 'TypeError' || 
            err.message.includes('network') || 
            err.message.includes('failed to fetch')
          )) {
            bookingDebugger.log('error', 'Flight Tracking', 'Network error', {
              error: err.message,
              attempt: retryCount + 1
            });
            
            if (retryCount >= MAX_RETRIES - 1) {
              throw new Error('Network error: Please check your internet connection and try again.');
            }
          }

          if (retryCount >= MAX_RETRIES - 1) {
            throw err;
          }

          const delay = Math.min(
            Math.max(
              MIN_RETRY_DELAY * Math.pow(BACKOFF_MULTIPLIER, retryCount),
              MIN_RETRY_DELAY
            ),
            MAX_RETRY_DELAY
          );

          bookingDebugger.log('error', 'Flight Tracking', 'Request failed, retrying', {
            attempt: retryCount + 1,
            error: err instanceof Error ? err.message : 'Unknown error',
            delay
          });

          await new Promise(resolve => setTimeout(resolve, delay));
          retryCount++;
        }
      }

      if (lastError) {
        throw lastError;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error tracking flight';
      setError(errorMessage);
      bookingDebugger.log('error', 'Flight Tracking', 'Final error', {
        error: errorMessage,
        flightNumber,
        date: date?.toISOString()
      });
    } finally {
      setIsLoading(false);
      pendingRequest.current = null;
    }
  };

  const clearFlightInfo = () => {
    setFlightInfo(null);
    setError(null);
  };

  // Helper function to format flight status
  const getFlightStatus = (status: string): string => {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'en-route':
        return 'En Route';
      case 'scheduled':
        return 'Scheduled';
      case 'landed':
        return 'Landed';
      case 'delayed':
        return 'Delayed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status || 'Unknown';
    }
  };

  // Helper function to determine status color
  const getStatusColor = (status: string): string => {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'en-route':
        return 'rgba(34, 197, 94, 0.2)'; // green with opacity
      case 'scheduled':
        return 'rgba(59, 130, 246, 0.2)'; // blue with opacity
      case 'landed':
        return 'rgba(34, 197, 94, 0.2)'; // green with opacity
      case 'delayed':
        return 'rgba(245, 158, 11, 0.2)'; // yellow with opacity
      case 'cancelled':
        return 'rgba(239, 68, 68, 0.2)'; // red with opacity
      default:
        return 'rgba(107, 114, 128, 0.2)'; // gray with opacity
    }
  };

  return {
    flightInfo,
    loading,
    error,
    trackFlight: debouncedTrackFlight, // Export the debounced version
    clearFlightInfo
  };
};
