import { useState, useEffect } from 'react';
import { generateUUID } from '@core/utils/uuid';

interface SavedLocation {
  id: string;
  address: string;
  coordinates: [number, number];
  lastUsed: Date;
}

export const useLocationHistory = () => {
  const [recentLocations, setRecentLocations] = useState<SavedLocation[]>([]);

  useEffect(() => {
    const saved = localStorage.getItem('recent-locations');
    if (saved) {
      setRecentLocations(JSON.parse(saved));
    }
  }, []);

  const addLocation = (location: Omit<SavedLocation, 'id' | 'lastUsed'>) => {
    const newLocation = {
      ...location,
      id: generateUUID(),
      lastUsed: new Date(),
    };

    setRecentLocations(prev => {
      const updated = [newLocation, ...prev.slice(0, 4)]; // Keep only last 5
      localStorage.setItem('recent-locations', JSON.stringify(updated));
      return updated;
    });
  };

  return { recentLocations, addLocation };
};
