# CSS Logger Documentation

A comprehensive logging system for tracking and analyzing CSS usage in React applications.

## Features

- Log applied CSS properties with component context
- Track CSS changes over time
- Measure performance impact of style applications
- Provide filtering by component, property, or selector
- Low overhead implementation with buffering for production use
- Visual analysis of CSS usage patterns
- Export logs for external analysis

## Getting Started

### Basic Usage

```tsx
import { cssLogger } from '@core/utils/cssLogger';

// Log styles for a specific component
cssLogger.logStyle(
  'MyComponent', // Component ID
  '.my-class',   // CSS selector
  { color: 'red', fontSize: '16px' } // CSS properties
);

// Log theme-based styles
cssLogger.logThemeStyles(
  'ThemeProvider',
  { primary: '#ff0000', secondary: '#00ff00' }
);
```

### React Hook Integration

```tsx
import { useCSSLogger } from '@core/utils/useCSSLogger';

const MyComponent: React.FC = () => {
  // Get logging utilities and a ref to attach to your component
  const { ref, logStyle } = useCSSLogger('MyComponent');
  
  // Log any specific styles if needed
  useEffect(() => {
    logStyle('.special-class', { color: 'blue', animation: 'fade 0.3s' });
  }, []);
  
  // Attach the ref to your component to auto-log its computed styles
  return <div ref={ref}>Content</div>;
};
```

### HOC Pattern

```tsx
import { withCSSLogging } from '@core/utils/useCSSLogger';

const MyComponent: React.FC = () => {
  return <div>Content</div>;
};

// Wrap your component to automatically log all CSS changes
export default withCSSLogging(MyComponent, 'CustomComponentId');
```

### Using the Visual Debug Interface

```tsx
import { CSSLogViewer } from '@core/components/CSSLogViewer';

const App: React.FC = () => {
  return (
    <>
      {/* Your application content */}
      <main>...</main>
      
      {/* Add the logger viewer (only in development) */}
      {process.env.NODE_ENV === 'development' && (
        <CSSLogViewer 
          position="bottom-right"
          filter={{ componentId: 'MyComponent' }} // Optional filtering
        />
      )}
    </>
  );
};
```

## API Reference

### Core Logger

```typescript
cssLogger.logStyle(
  componentId: string,
  selector: string,
  properties: Record<string, string>,
  source?: 'inline' | 'class' | 'stylesheet' | 'theme'
): void
```

```typescript
cssLogger.logThemeStyles(
  componentId: string,
  properties: Record<string, string>
): void
```

```typescript
cssLogger.getLogs(
  filter?: {
    componentId?: string;
    property?: string;
    selector?: string;
    timeRange?: [number, number];
    source?: 'inline' | 'class' | 'stylesheet' | 'theme';
  }
): CSSLogEntry[]
```

```typescript
cssLogger.clear(): void
```

```typescript
cssLogger.analyzePerformance(): Record<string, any>
```

```typescript
cssLogger.exportLogs(): string
```

```typescript
cssLogger.findStyleThrashing(): Record<string, number>
```

### React Hook API

```typescript
const { 
  ref,
  logStyle,
  logThemeStyles,
  logElementStyle
} = useCSSLogger(componentId: string, dependencies?: React.DependencyList);
```

### Component Props

```typescript
<CSSLogViewer 
  filter?: {
    componentId?: string;
    property?: string;
    selector?: string;
    timeRange?: [number, number];
    source?: 'inline' | 'class' | 'stylesheet' | 'theme';
  };
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  width?: string;
  height?: string;
/>
```

## Browser Console API

The following methods are available in the browser console for debugging:

```javascript
// Access the logger directly
window.__cssLogger

// Toggle logging on/off
window.toggleCSSLogging()

// Clear all logs
window.clearCSSLogs()

// Run performance analysis
window.analyzeCSSPerformance()
```

## Performance Considerations

### Development Mode

In development mode, the logger automatically:
- Enables console logging
- Captures stack traces
- Updates the UI more frequently

### Production Mode

In production, the logger:
- Disables most logging by default
- Uses a buffer to reduce performance impact
- Does not capture stack traces
- Can be completely disabled if needed

## Use Cases

1. **Detecting CSS Thrashing**  
   Find components that are frequently changing styles and causing layout recalculations

2. **Theme Analysis**  
   Track theme variable usage across components

3. **Performance Optimization**  
   Identify slow style applications and components with excessive styling

4. **Debugging Layout Issues**  
   Capture the exact state of styles at a specific point in time

5. **CSS Refactoring**  
   Find duplicate or conflicting styles across components 