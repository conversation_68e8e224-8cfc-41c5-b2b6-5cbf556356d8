import React, { useEffect, useRef } from 'react';
import { cssLogger } from './cssLogger';

/**
 * Hook to log component CSS styles
 * 
 * @param componentId - Identifier for the component
 * @param dependencies - Optional array of dependencies to trigger style logging
 * @returns - ref to attach to the component and logging methods
 */
export function useCSSLogger(
  componentId: string,
  dependencies: React.DependencyList = []
) {
  const ref = useRef<HTMLElement>(null);
  
  useEffect(() => {
    if (!ref.current) return;
    
    // Get computed styles
    const computedStyle = window.getComputedStyle(ref.current);
    const styleProperties: Record<string, string> = {};
    
    for (let i = 0; i < computedStyle.length; i++) {
      const prop = computedStyle[i];
      styleProperties[prop] = computedStyle.getPropertyValue(prop);
    }
    
    // Log the styles
    cssLogger.logStyle(
      componentId,
      ref.current.className || ref.current.tagName.toLowerCase(),
      styleProperties
    );
  }, dependencies);
  
  return {
    ref,
    logStyle: (selector: string, properties: Record<string, string>) => 
      cssLogger.logStyle(componentId, selector, properties),
    logThemeStyles: (properties: Record<string, string>) => 
      cssLogger.logThemeStyles(componentId, properties),
    logElementStyle: (element: HTMLElement, selector?: string) => {
      if (!element) return;
      
      const computedStyle = window.getComputedStyle(element);
      const styleProperties: Record<string, string> = {};
      
      for (let i = 0; i < computedStyle.length; i++) {
        const prop = computedStyle[i];
        styleProperties[prop] = computedStyle.getPropertyValue(prop);
      }
      
      cssLogger.logStyle(
        componentId,
        selector || element.className || element.tagName.toLowerCase(),
        styleProperties
      );
    }
  };
}

/**
 * Higher-order component to automatically log CSS styles
 */
export function withCSSLogging<T extends React.ComponentType<any>>(
  Component: T,
  componentId?: string
): React.FC<React.ComponentProps<T>> {
  const displayName = Component.displayName || Component.name || 'Component';
  const loggedComponentId = componentId || displayName;
  
  const WrappedComponent: React.FC<React.ComponentProps<T>> = (props) => {
    const { ref, logElementStyle } = useCSSLogger(loggedComponentId, [props]);
    
    useEffect(() => {
      // Log initial mount
      console.log(`[CSSLogger] ${loggedComponentId} mounted`);
      
      return () => {
        console.log(`[CSSLogger] ${loggedComponentId} unmounted`);
      };
    }, []);
    
    return (
      <div ref={ref as React.RefObject<HTMLDivElement>} data-css-logged={loggedComponentId}>
        <Component {...props} />
      </div>
    );
  };
  
  WrappedComponent.displayName = `withCSSLogging(${displayName})`;
  return WrappedComponent;
} 