import React, { useRef, useEffect, RefObject } from 'react';
import { cssLogger } from './cssLogger';

/**
 * Custom hook for CSS logging
 * This replaces the missing hook from cssLogger
 */
export const useCSSLogger = (
  componentId: string,
  elementRef: RefObject<HTMLElement>,
  dependencies: any[] = []
): void => {
  useEffect(() => {
    if (elementRef.current) {
      const styles = window.getComputedStyle(elementRef.current);
      const properties: Record<string, string> = {};
      
      // Log basic properties
      ['color', 'background-color', 'font-size', 'margin', 'padding'].forEach(prop => {
        properties[prop] = styles.getPropertyValue(prop);
      });
      
      cssLogger.logStyle(componentId, 'element', properties);
    }
  }, [componentId, elementRef, ...dependencies]);
};

/**
 * Example component showing how to use the CSS Logger with a hook
 */
export const LoggedComponent: React.FC<{
  id: string;
  className?: string;
  children: React.ReactNode;
}> = ({ id, className, children }) => {
  const ref = useRef<HTMLDivElement>(null);
  
  // Use the CSS logger hook
  useCSSLogger(id, ref, [className]);
  
  // You can also log specific CSS changes
  useEffect(() => {
    if (className) {
      cssLogger.logStyle(id, `.${className}`, { 
        className, 
        changedAt: new Date().toISOString() 
      }, 'class');
    }
  }, [className, id]);
  
  return (
    <div ref={ref} className={className}>
      {children}
    </div>
  );
};

/**
 * Higher-order component to wrap existing components with CSS logging
 */
export function withCSSLoggingWrapper<P extends object>(
  Component: React.ComponentType<P>,
  componentId: string
): React.FC<P> {
  return (props: P) => {
    const ref = useRef<HTMLDivElement>(null);
    
    // Use the CSS logger hook
    useCSSLogger(componentId, ref, [props]);
    
    return (
      <div ref={ref} data-css-logged={componentId}>
        <Component {...props} />
      </div>
    );
  };
}

/**
 * CSS Debug Panel component to show in development mode
 */
export const CSSDebugPanel: React.FC<{
  filter?: {
    componentId?: string;
    property?: string;
  }
}> = ({ filter }) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const [logs, setLogs] = React.useState<any[]>([]);
  const [analysis, setAnalysis] = React.useState<Record<string, any>>({});
  
  // Update logs periodically
  useEffect(() => {
    if (!isOpen) return;
    
    const updateLogs = () => {
      const filteredLogs = cssLogger.getLogs(filter);
      setLogs(filteredLogs);
    };
    
    updateLogs();
    const interval = setInterval(updateLogs, 2000);
    
    return () => clearInterval(interval);
  }, [isOpen, filter]);
  
  // Run performance analysis 
  const runAnalysis = () => {
    const results = cssLogger.analyzePerformance();
    setAnalysis(results);
  };
  
  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          background: '#333',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          padding: '8px 12px',
          cursor: 'pointer',
          zIndex: 9999,
          fontSize: '14px'
        }}
      >
        CSS Debug
      </button>
    );
  }
  
  return (
    <div
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        width: '500px',
        maxHeight: '80vh',
        background: '#222',
        color: '#eee',
        borderRadius: '6px',
        boxShadow: '0 0 20px rgba(0,0,0,0.3)',
        zIndex: 9999,
        overflow: 'hidden',
        fontFamily: 'monospace',
        fontSize: '12px'
      }}
    >
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '8px 16px',
          background: '#333',
          borderBottom: '1px solid #444'
        }}
      >
        <h3 style={{ margin: 0 }}>CSS Debug ({logs.length} logs)</h3>
        <div>
          <button
            onClick={runAnalysis}
            style={{
              marginRight: '8px',
              background: '#555',
              border: 'none',
              color: 'white',
              padding: '4px 8px',
              borderRadius: '3px',
              cursor: 'pointer'
            }}
          >
            Analyze
          </button>
          <button
            onClick={() => setIsOpen(false)}
            style={{
              background: 'none',
              border: 'none',
              color: '#999',
              fontSize: '16px',
              cursor: 'pointer'
            }}
          >
            ×
          </button>
        </div>
      </div>
      
      <div style={{ padding: '16px', maxHeight: 'calc(80vh - 50px)', overflow: 'auto' }}>
        {/* Analysis results */}
        {Object.keys(analysis).length > 0 && (
          <div
            style={{
              marginBottom: '20px',
              padding: '12px',
              background: '#2a2a2a',
              borderRadius: '4px'
            }}
          >
            <h4 style={{ margin: '0 0 8px 0' }}>Performance Analysis</h4>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
              {Object.entries(analysis).map(([key, value]) => {
                if (typeof value === 'object') return null;
                return (
                  <div key={key}>
                    <span style={{ color: '#aaa' }}>{key}:</span>{' '}
                    <span>{value}</span>
                  </div>
                );
              })}
            </div>
            
            {analysis.topChangedComponents && (
              <>
                <h5 style={{ marginTop: '12px' }}>Top Changed Components</h5>
                <div>
                  {analysis.topChangedComponents.map((item: any, idx: number) => (
                    <div key={idx}>
                      {item.component}: {item.count} changes
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        )}
        
        {/* Logs display */}
        {logs.length === 0 ? (
          <p>No CSS logs captured yet.</p>
        ) : (
          logs.slice(0, 50).map((log, idx) => (
            <div
              key={idx}
              style={{
                padding: '8px',
                marginBottom: '8px',
                background: '#2a2a2a',
                borderRadius: '4px',
                borderLeft: '3px solid #555'
              }}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <strong>{log.componentId}</strong>
                <span style={{ color: '#999' }}>
                  {new Date(log.timestamp).toLocaleTimeString()}
                </span>
              </div>
              <div style={{ color: '#aaa', fontSize: '11px' }}>
                {log.selector} ({log.source})
              </div>
              
              {log.properties && Object.keys(log.properties).length > 0 && (
                <details>
                  <summary style={{ margin: '6px 0', cursor: 'pointer' }}>
                    {Object.keys(log.properties).length} properties
                  </summary>
                  <div
                    style={{
                      maxHeight: '200px',
                      overflow: 'auto',
                      padding: '8px',
                      background: '#333',
                      borderRadius: '3px',
                      fontSize: '11px'
                    }}
                  >
                    {Object.entries(log.properties)
                      .slice(0, 20)
                      .map(([prop, value]) => (
                        <div key={prop}>
                          <span style={{ color: '#6cf' }}>{prop}:</span>{' '}
                          <span>{String(value).slice(0, 50)}</span>
                        </div>
                      ))}
                    
                    {Object.keys(log.properties).length > 20 && (
                      <div style={{ color: '#999', marginTop: '8px' }}>
                        ...and {Object.keys(log.properties).length - 20} more properties
                      </div>
                    )}
                  </div>
                </details>
              )}
              
              {log.applyTime && (
                <div
                  style={{
                    marginTop: '4px',
                    fontSize: '11px',
                    color: log.applyTime > 5 ? '#f77' : '#7f7'
                  }}
                >
                  Applied in {log.applyTime.toFixed(2)}ms
                </div>
              )}
            </div>
          ))
        )}
        
        {logs.length > 50 && (
          <div style={{ textAlign: 'center', color: '#999' }}>
            Showing 50 of {logs.length} logs. Export to see all.
          </div>
        )}
        
        <div style={{ marginTop: '16px' }}>
          <button
            onClick={() => cssLogger.clear()}
            style={{
              background: '#444',
              border: 'none',
              color: 'white',
              padding: '6px 12px',
              borderRadius: '3px',
              marginRight: '8px',
              cursor: 'pointer'
            }}
          >
            Clear Logs
          </button>
          
          <button
            onClick={() => {
              const data = cssLogger.exportLogs();
              const blob = new Blob([data], { type: 'application/json' });
              const url = URL.createObjectURL(blob);
              
              const a = document.createElement('a');
              a.href = url;
              a.download = `css-logs-${new Date().toISOString().slice(0, 10)}.json`;
              document.body.appendChild(a);
              a.click();
              
              setTimeout(() => {
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
              }, 100);
            }}
            style={{
              background: '#444',
              border: 'none',
              color: 'white',
              padding: '6px 12px',
              borderRadius: '3px',
              cursor: 'pointer'
            }}
          >
            Export Logs
          </button>
        </div>
      </div>
    </div>
  );
};