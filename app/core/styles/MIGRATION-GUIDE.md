# CSS Migration Guide

## Transitioning from !important to Namespaced CSS

This guide will help you migrate from the previous CSS approach that relied heavily on `!important` declarations to our new namespaced CSS architecture.

## Why Migrate?

The previous CSS approach had several issues:

- Overuse of `!important` declarations, making styles difficult to override
- Lack of proper specificity, leading to CSS conflicts
- Poor maintainability and scalability
- Potential conflicts with WordPress themes

Our new namespaced approach solves these issues by:

- Using specific selectors with a unique plugin prefix
- Implementing CSS custom properties for theme customization
- Ensuring proper specificity through class hierarchy
- Making the plugin more maintainable and compatible with various WordPress themes

## Migration Steps

### 1. Update HTML Structure

Add the `limo-booking-plugin` class to your root container:

```html
<!-- Before -->
<div class="limo-booking-wrapper">
  <!-- Content -->
</div>

<!-- After -->
<div class="limo-booking-plugin">
  <!-- Content -->
</div>
```

### 2. Replace CSS Classes

Replace generic classes with namespaced equivalents:

| Old Class | New Class |
|-----------|------------|
| `bg-primary` | `limo-bg-primary` |
| `text-primary` | `limo-text` |
| `text-secondary` | `limo-text-secondary` |
| `text-muted` | `limo-text-muted` |
| `text-disabled` | `limo-text-disabled` |
| Button with `bg-primary` | `limo-button limo-button-primary` |
| Form input | `limo-input` |
| Tab button | `limo-tab` |
| Toggle button | `limo-toggle` |
| Toggle thumb | `limo-toggle-thumb` |
| Marker | `limo-marker` |
| Pickup marker | `limo-pickup-marker` |
| Stop marker | `limo-stop-marker` |
| Dropoff marker | `limo-dropoff-marker` |

### 3. Update Theme Switching

Replace data attributes for theme switching:

```html
<!-- Before -->
<div data-theme="light">
  <!-- Content -->
</div>

<!-- After -->
<div class="limo-booking-plugin" data-theme="light">
  <!-- Content -->
</div>
```

### 4. Update Animation Classes

Replace animation classes with namespaced versions:

| Old Class | New Class |
|-----------|------------|
| `animate-glow-pulse` | `limo-animate-glow` |
| `updating-header` | `limo-animate-shimmer` |

### 5. Update WordPress Integration

If you're using the plugin in WordPress, update your integration code to use the new wrapper component:

```jsx
// Before
import { BookingForm } from './components/BookingForm';

const WordPressIntegration = (props) => (
  <div className="limo-booking-wrapper">
    <BookingForm {...props} />
  </div>
);

// After
import LimoBookingWrapper from './wordpress/LimoBookingWrapper';

const WordPressIntegration = (props) => (
  <LimoBookingWrapper theme="dark" {...props} />
);
```

## Example Migration

### Before:

```html
<div class="limo-booking-wrapper" data-theme="dark">
  <button class="bg-primary">Book Now</button>
  <div class="booking-tabs">
    <button class="bg-primary" data-active="true">Point to Point</button>
    <button>Hourly</button>
  </div>
  <input type="text" placeholder="Enter pickup location">
</div>
```

### After:

```html
<div class="limo-booking-plugin" data-theme="dark">
  <button class="limo-button limo-button-primary">Book Now</button>
  <div class="limo-tabs">
    <button class="limo-tab" data-active="true">Point to Point</button>
    <button class="limo-tab">Hourly</button>
  </div>
  <input type="text" class="limo-input" placeholder="Enter pickup location">
</div>
```

## Testing Your Migration

After migrating, test your application to ensure:

1. All styles are applied correctly
2. Theme switching works properly
3. No CSS conflicts with WordPress themes
4. All interactive elements (buttons, tabs, toggles) function correctly

## Need Help?

Refer to the following resources:

- `README-WORDPRESS.md` - Detailed documentation on the new CSS approach
- `wordpress-plugin.css` - Complete list of available namespaced classes
- `LimoBookingWrapper.jsx` - Example component using the new approach

If you encounter any issues during migration, please open an issue in the repository.