/* Trip Summary Mobile Panel */
.trip-summary-backdrop {
  z-index: 9998 !important;
  position: fixed;
  inset: 0;
  background-color: var(--overlay-dark);
  pointer-events: auto;
}

.trip-summary-panel {
  z-index: 9999 !important;
  position: fixed;
  inset-y: 0;
  right: 0;
  width: 100%;
  max-width: 32rem;
  background-color: var(--background);
  box-shadow: 0 0 20px var(--shadow);
  pointer-events: auto;
}

/* Ensure the panel content has proper pointer events */
.trip-summary-panel * {
  pointer-events: auto;
}

/* Map Controls */
.mapboxgl-control-container {
  position: relative;
  z-index: var(--z-controls);
}

.mapboxgl-ctrl-top-right {
  z-index: var(--z-controls);
}

.mapboxgl-ctrl-group {
  background-color: var(--surface-dark) !important;
  border: 1px solid var(--border-color) !important;
}

.mapboxgl-ctrl-group button {
  background-color: transparent !important;
  border: none !important;
}

.mapboxgl-ctrl-group button:hover {
  background-color: var(--overlay-light) !important;
}

/* Map Markers and Popups */
.marker {
  cursor: pointer;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-weight: bold;
  border: 2px solid var(--text-primary);
  box-shadow: 0 2px 4px var(--shadow);
  z-index: var(--z-map);
}

.pickup-marker {
  background-color: var(--success);
}

.stop-marker {
  background-color: var(--primary-transparent);
}

.dropoff-marker {
  background-color: var(--error);
}

.mapboxgl-popup {
  max-width: 200px;
  z-index: var(--z-tooltip);
}

.mapboxgl-popup-content {
  background-color: var(--surface-dark) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
  padding: 12px !important;
  border-radius: 8px !important;
}

.mapboxgl-popup-close-button {
  color: var(--text-primary) !important;
}

.mapboxgl-popup-tip {
  border-top-color: var(--surface-dark) !important;
}

/* Force map to stay behind */
.mapboxgl-map,
.mapboxgl-canvas,
.mapboxgl-canvas-container {
  z-index: var(--z-base) !important;
}

@keyframes subtle-bounce {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-2px);
  }
}

@keyframes ping {
  0% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(2);
    opacity: 0.5;
  }

  100% {
    transform: scale(3);
    opacity: 0;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.3;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.3;
  }
}

.animate-subtle-bounce {
  animation: subtle-bounce 2s ease-in-out infinite;
}

.animate-ping {
  animation: ping 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-pulse {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes gradient-shift {

  0%,
  100% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }
}

.after\:animate-pulse::after {
  content: '';
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.after\:bg-gradient-to-r::after {
  background-size: 200% 200%;
  animation: gradient-shift 2s ease infinite;
}

/* Trip Summary Fixed Header */
.trip-summary-fixed {
  position: sticky;
  top: 0;
  z-index: 40;
  background-color: var(--background);
  border-bottom: 1px solid var(--border);
  margin: -1rem -1rem 1rem -1rem;
  padding: 1rem;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Enhanced Glow Animation */
@keyframes glow-pulse {
  0% {
    background-color: color-mix(in srgb, var(--primary) 10%, var(--background));
    box-shadow: 0 0 0 0 var(--primary);
  }

  50% {
    background-color: color-mix(in srgb, var(--primary) 30%, var(--background));
    box-shadow: 0 0 20px 0 var(--primary);
  }

  100% {
    background-color: color-mix(in srgb, var(--primary) 10%, var(--background));
    box-shadow: 0 0 0 0 var(--primary);
  }
}

.animate-glow-pulse {
  animation: glow-pulse 2s ease-in-out infinite;
  position: relative;
  isolation: isolate;
  overflow: visible;
}

/* Form Container */
.booking-form-container {
  position: relative;
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* Ensure the form container stops at viewport edges */
.booking-form-container.embedded {
  max-height: 100vh;
  overflow: hidden;
}

.booking-form-container.embedded .form-content {
  height: calc(100vh - 140px);
  /* Account for fixed headers/footers */
  overflow-y: auto;
  padding: 1rem;
}

/* Smooth Scroll Behavior */
.booking-form-container,
.form-content {
  scroll-behavior: smooth;
}

/* Mobile Header Styles */
.mobile-header-fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background-color: var(--surface-dark);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-bottom: 1px solid var(--primary);
  transition: all 0.3s ease-in-out;
}