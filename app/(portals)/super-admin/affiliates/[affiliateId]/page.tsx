"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/app/components/ui/tabs"
import { useToast } from "@/app/components/ui/use-toast"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
  Building2,
  Car,
  Users,
  MapPin,
  FileText,
  CheckCircle2,
  XCircle,
  ArrowLeft,
  Shield,
  DollarSign,
  ClipboardCheck,
  AlertTriangle,
  Mail,
  Phone,
  Globe,
  FileCheck,
  Clock,
  Calendar,
  Truck,
  CheckCircle,
  TrendingUp,
  Star,
  Award,
  Target,
  Activity
} from "lucide-react"
import Link from "next/link"
import { Badge } from "@/app/components/ui/badge"
import { Progress } from "@/app/components/ui/progress"
import { Separator } from "@/app/components/ui/separator"

// API functions
const fetchAffiliateDetails = async (id: string) => {
  const response = await fetch(`/api/super-admin/affiliates/${id}`);
  if (!response.ok) {
    throw new Error('Failed to fetch affiliate details');
  }
  return response.json();
};

const updateAffiliateStatus = async ({ id, status, verificationStatus }: {
  id: string;
  status?: string;
  verificationStatus?: string;
}) => {
  const response = await fetch(`/api/super-admin/affiliates/${id}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ status, verificationStatus }),
  });

  if (!response.ok) {
    throw new Error('Failed to update affiliate status');
  }

  return response.json();
};

export default function AffiliateDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const affiliateId = params.affiliateId as string;

  // Fetch affiliate details
  const { data: affiliate, isLoading, error } = useQuery({
    queryKey: ['affiliate', affiliateId],
    queryFn: () => fetchAffiliateDetails(affiliateId),
    enabled: !!affiliateId,
  });

  // Status update mutation
  const statusMutation = useMutation({
    mutationFn: updateAffiliateStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['affiliate', affiliateId] });
      queryClient.invalidateQueries({ queryKey: ['affiliate-live-data'] });
      toast({
        title: "Success",
        description: "Affiliate status updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: (error instanceof Error ? error.message : String(error)) || "Failed to update affiliate status",
        variant: "destructive",
      });
    },
  });

  const handleApprove = () => {
    statusMutation.mutate({
      id: affiliateId,
      status: 'active',
      verificationStatus: 'verified'
    });
  };

  const handleReject = () => {
    statusMutation.mutate({
      id: affiliateId,
      status: 'inactive',
      verificationStatus: 'rejected'
    });
  };

  const handleRequestUpdates = () => {
    statusMutation.mutate({
      id: affiliateId,
      verificationStatus: 'in_progress'
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading affiliate details...</p>
        </div>
      </div>
    );
  }

  if (error || !affiliate) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-8 w-8 text-red-600 mx-auto mb-4" />
          <p className="text-muted-foreground">Failed to load affiliate details</p>
          <p className="text-sm text-red-600 mt-2">{error?.message || 'Unknown error'}</p>
          <Button variant="outline" onClick={() => router.back()} className="mt-2">
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  // Extract affiliate data from the response
  const affiliateData = affiliate.affiliate || affiliate;
  return (
    <div className="container p-6 space-y-8">
      {/* Header with navigation and primary actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/super-admin/affiliates">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Affiliates
            </Link>
          </Button>
          <div className="flex items-center gap-2">
            <Badge variant={
              affiliateData.status === 'active' ? 'default' :
              affiliateData.status === 'pending' ? 'secondary' :
              'destructive'
            }>
              {affiliateData.status === 'active' ? 'Active' :
               affiliateData.status === 'pending' ? 'Pending Review' :
               'Inactive'}
            </Badge>
            <Badge variant="outline">
              {affiliateData.verificationStatus === 'verified' ? 'Verified' :
               affiliateData.verificationStatus === 'in_progress' ? 'In Progress' :
               affiliateData.verificationStatus === 'rejected' ? 'Rejected' :
               'Pending Verification'}
            </Badge>
          </div>
        </div>

        {/* Prominent Action Buttons */}
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={handleRequestUpdates}
            disabled={statusMutation.isLoading}
            className="h-10 px-6"
          >
            <FileText className="h-4 w-4 mr-2" />
            Request Updates
          </Button>
          <Button
            variant="destructive"
            onClick={handleReject}
            disabled={statusMutation.isLoading || affiliateData.status === 'inactive'}
            className="h-10 px-6"
          >
            <XCircle className="h-4 w-4 mr-2" />
            {statusMutation.isLoading ? 'Processing...' : 'Reject Application'}
          </Button>
          <Button
            onClick={handleApprove}
            disabled={statusMutation.isLoading || affiliateData.status === 'active'}
            className="h-10 px-6 bg-green-600 hover:bg-green-700"
          >
            <CheckCircle2 className="h-4 w-4 mr-2" />
            {statusMutation.isLoading ? 'Processing...' : 'Approve & Activate'}
          </Button>
        </div>
      </div>

      {/* Key Metrics Dashboard - Moved to Top */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-600" />
              Affiliate Distribution
            </CardTitle>
            <p className="text-sm text-muted-foreground">By tenant usage</p>
          </CardHeader>
          <CardContent>
            <div className="h-[120px] bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">85%</div>
                <p className="text-sm text-blue-700">Active Usage</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <MapPin className="h-5 w-5 text-green-600" />
              Service Coverage
            </CardTitle>
            <p className="text-sm text-muted-foreground">By location</p>
          </CardHeader>
          <CardContent>
            <div className="h-[120px] bg-gradient-to-br from-green-50 to-green-100 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">12</div>
                <p className="text-sm text-green-700">Cities Covered</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Shield className="h-5 w-5 text-purple-600" />
              Performance
            </CardTitle>
            <p className="text-sm text-muted-foreground">Top rated affiliates</p>
          </CardHeader>
          <CardContent>
            <div className="h-[120px] bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">4.8</div>
                <p className="text-sm text-purple-700">Rating Score</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Comprehensive Affiliate Management */}
      <Tabs defaultValue="approval" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5 lg:grid-cols-9">
          <TabsTrigger value="approval">Approval</TabsTrigger>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="contact">Contact</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="rates">Rates</TabsTrigger>
          <TabsTrigger value="fleet">Fleet</TabsTrigger>
          <TabsTrigger value="service-area">Service Area</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="audit">Audit Log</TabsTrigger>
        </TabsList>

        {/* Approval Tab - Current Content */}
        <TabsContent value="approval" className="space-y-6">
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        {/* Left Column - Company Overview & Contact */}
        <div className="xl:col-span-1 space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center">
                  <Building2 className="h-8 w-8 text-gray-600" />
                </div>
                <div>
                  <CardTitle className="text-xl">{affiliateData.name || affiliateData.businessName}</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Applied {new Date(affiliateData.createdAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Email</p>
                    <p className="font-medium">{affiliateData.email || 'Not provided'}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Phone</p>
                    <p className="font-medium">{affiliateData.phone || 'Not provided'}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Address</p>
                    <p className="font-medium">
                      {affiliateData.address?.street ?
                        `${affiliateData.address.street}, ${affiliateData.address.city}, ${affiliateData.address.state} ${affiliateData.address.zip}` :
                        'Not provided'
                      }
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Business License</p>
                  <p className="font-medium text-sm">{affiliateData.businessInfo?.taxId || 'Not provided'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Established</p>
                  <p className="font-medium text-sm">{affiliateData.businessInfo?.yearEstablished || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Rating</p>
                  <p className="font-medium text-sm">{affiliateData.metrics?.rating?.toFixed(1) || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Last Active</p>
                  <p className="font-medium text-sm">
                    {affiliateData.lastActive ? new Date(affiliateData.lastActive).toLocaleDateString() : 'Never'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Application Progress */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <ClipboardCheck className="h-5 w-5" />
                Application Progress
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Progress value={65} className="w-full" />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Documents</span>
                  <span>Verification</span>
                  <span>Review</span>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Business Registration</span>
                    </div>
                    <Badge variant="outline" className="text-xs">Verified</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-yellow-500" />
                      <span className="text-sm">Insurance Documentation</span>
                    </div>
                    <Badge variant="secondary" className="text-xs">In Review</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                      <span className="text-sm">Fleet Inspection</span>
                    </div>
                    <Badge variant="outline" className="text-xs">Pending</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Fleet & Rates, Compliance & Audit */}
        <div className="xl:col-span-2 space-y-6">
          {/* Fleet & Rates Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Car className="h-5 w-5" />
                Fleet & Rates
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Fleet Overview */}
                <div className="space-y-4">
                  <h4 className="font-medium">Fleet Overview</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-4 bg-blue-50 rounded-lg text-center">
                      <div className="text-2xl font-bold text-blue-600">12</div>
                      <p className="text-sm text-blue-700">Total Vehicles</p>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg text-center">
                      <div className="text-2xl font-bold text-green-600">8</div>
                      <p className="text-sm text-green-700">Active Drivers</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <span className="text-sm">Sedans</span>
                      <Badge variant="outline">6 vehicles</Badge>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <span className="text-sm">SUVs</span>
                      <Badge variant="outline">4 vehicles</Badge>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <span className="text-sm">Luxury</span>
                      <Badge variant="outline">2 vehicles</Badge>
                    </div>
                  </div>
                </div>

                {/* Rate Structure */}
                <div className="space-y-4">
                  <h4 className="font-medium">Rate Structure</h4>
                  <div className="space-y-3">
                    <div className="p-4 border rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-medium">Sedan - Airport</span>
                        <span className="text-lg font-bold">$85</span>
                      </div>
                      <p className="text-sm text-muted-foreground">Base rate to/from major airports</p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-medium">SUV - Hourly</span>
                        <span className="text-lg font-bold">$120/hr</span>
                      </div>
                      <p className="text-sm text-muted-foreground">3-hour minimum</p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-medium">Luxury - Point-to-Point</span>
                        <span className="text-lg font-bold">$3.50/mi</span>
                      </div>
                      <p className="text-sm text-muted-foreground">$50 minimum</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Compliance & Audit Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Compliance & Audit
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Documents Status */}
                <div className="space-y-4">
                  <h4 className="font-medium">Required Documents</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                        <span className="text-sm">Business License</span>
                      </div>
                      <Badge variant="outline" className="bg-green-100 text-green-700">Verified</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                        <span className="text-sm">Commercial Insurance</span>
                      </div>
                      <Badge variant="outline" className="bg-green-100 text-green-700">Verified</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-yellow-500" />
                        <span className="text-sm">Vehicle Inspections</span>
                      </div>
                      <Badge variant="secondary">In Progress</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                        <span className="text-sm">Driver Background Checks</span>
                      </div>
                      <Badge variant="outline" className="bg-red-100 text-red-700">Missing</Badge>
                    </div>
                  </div>
                </div>

                {/* Operational Compliance */}
                <div className="space-y-4">
                  <h4 className="font-medium">Operational Compliance</h4>
                  <div className="space-y-3">
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Dispatch Integration</span>
                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                      </div>
                      <p className="text-xs text-muted-foreground">LimoAnywhere connected</p>
                    </div>
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">24/7 Support</span>
                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                      </div>
                      <p className="text-xs text-muted-foreground">Confirmed availability</p>
                    </div>
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Service Standards</span>
                        <Clock className="h-4 w-4 text-yellow-500" />
                      </div>
                      <p className="text-xs text-muted-foreground">Training in progress</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Service Coverage */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Service Coverage
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Coverage Areas</h4>
                  <div className="h-[200px] bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <MapPin className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">Interactive coverage map</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Primary Hub</p>
                      <p className="font-medium">Manhattan, NY</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Coverage Radius</p>
                      <p className="font-medium">50 miles</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Service Capabilities</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <span className="text-sm">Airport Transfers</span>
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                    </div>
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <span className="text-sm">Corporate Events</span>
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="text-sm">Wedding Services</span>
                      <XCircle className="h-4 w-4 text-gray-400" />
                    </div>
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <span className="text-sm">24/7 Availability</span>
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          </div>
        </div>
        </TabsContent>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Total Quotes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">247</div>
                <p className="text-xs text-muted-foreground">+12% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Completed Trips</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">189</div>
                <p className="text-xs text-muted-foreground">76% completion rate</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Revenue Generated</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$45,230</div>
                <p className="text-xs text-muted-foreground">This month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Average Rating</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4.8</div>
                <p className="text-xs text-muted-foreground">Based on 156 reviews</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Contact Tab */}
        <TabsContent value="contact" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Primary Contact</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Full Name</label>
                  <p className="text-sm">{affiliateData.contactPerson || 'Not provided'}</p>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Email</label>
                  <p className="text-sm">{affiliateData.email || 'Not provided'}</p>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Phone</label>
                  <p className="text-sm">{affiliateData.phone || 'Not provided'}</p>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Position</label>
                  <p className="text-sm">{affiliateData.position || 'Not provided'}</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Business Address</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Street Address</label>
                  <p className="text-sm">{affiliateData.address?.street || 'Not provided'}</p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">City</label>
                    <p className="text-sm">{affiliateData.address?.city || 'Not provided'}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">State</label>
                    <p className="text-sm">{affiliateData.address?.state || 'Not provided'}</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">ZIP Code</label>
                    <p className="text-sm">{affiliateData.address?.zip || 'Not provided'}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Country</label>
                    <p className="text-sm">{affiliateData.address?.country || 'United States'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Response Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm">Average Response Time</span>
                  <span className="font-medium">12 minutes</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Quote Acceptance Rate</span>
                  <span className="font-medium">78%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">On-Time Performance</span>
                  <span className="font-medium">94%</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Customer Satisfaction</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm">Overall Rating</span>
                  <span className="font-medium">4.8/5.0</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Total Reviews</span>
                  <span className="font-medium">156</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Repeat Customers</span>
                  <span className="font-medium">67%</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Financial Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm">Monthly Revenue</span>
                  <span className="font-medium">$45,230</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Average Trip Value</span>
                  <span className="font-medium">$239</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Growth Rate</span>
                  <span className="font-medium text-green-600">+12%</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Rates Tab */}
        <TabsContent value="rates" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Active Rate Cards
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="p-4 border rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">Sedan - Airport Transfer</span>
                      <Badge variant="default">Active</Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Base Rate:</span>
                        <span className="font-medium ml-2">$85.00</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Per Mile:</span>
                        <span className="font-medium ml-2">$2.50</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Wait Time:</span>
                        <span className="font-medium ml-2">$45/hr</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Min Charge:</span>
                        <span className="font-medium ml-2">$65.00</span>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">SUV - Hourly Service</span>
                      <Badge variant="default">Active</Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Hourly Rate:</span>
                        <span className="font-medium ml-2">$120.00</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Minimum:</span>
                        <span className="font-medium ml-2">3 hours</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Overtime:</span>
                        <span className="font-medium ml-2">$150/hr</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Fuel Surcharge:</span>
                        <span className="font-medium ml-2">5%</span>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">Luxury - Point to Point</span>
                      <Badge variant="secondary">Inactive</Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Base Rate:</span>
                        <span className="font-medium ml-2">$150.00</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Per Mile:</span>
                        <span className="font-medium ml-2">$4.00</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Status:</span>
                        <span className="text-red-600 ml-2">Under Review</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Rate Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Average Quote Response Rate</span>
                    <span className="font-medium">78%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Competitive Rate Percentage</span>
                    <span className="font-medium">65%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Rate Adjustment Frequency</span>
                    <span className="font-medium">Monthly</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Last Rate Update</span>
                    <span className="font-medium">Dec 15, 2024</span>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-3">Rate Comparison</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Market Average (Sedan)</span>
                      <span>$92.00</span>
                    </div>
                    <div className="flex justify-between">
                      <span>This Affiliate (Sedan)</span>
                      <span className="text-green-600">$85.00 (-7.6%)</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="fleet" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Car className="h-5 w-5" />
                  Vehicle Fleet
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                          <Car className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium">2023 Mercedes S-Class</h4>
                          <p className="text-sm text-muted-foreground">Luxury Sedan</p>
                        </div>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>License Plate:</span>
                          <span className="font-medium">ABC-1234</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Capacity:</span>
                          <span className="font-medium">4 passengers</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Status:</span>
                          <Badge variant="default" className="bg-green-500">Active</Badge>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                          <Truck className="h-6 w-6 text-green-600" />
                        </div>
                        <div>
                          <h4 className="font-medium">2022 Cadillac Escalade</h4>
                          <p className="text-sm text-muted-foreground">Luxury SUV</p>
                        </div>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>License Plate:</span>
                          <span className="font-medium">XYZ-5678</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Capacity:</span>
                          <span className="font-medium">7 passengers</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Status:</span>
                          <Badge variant="default" className="bg-green-500">Active</Badge>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                          <Car className="h-6 w-6 text-purple-600" />
                        </div>
                        <div>
                          <h4 className="font-medium">2023 BMW 7 Series</h4>
                          <p className="text-sm text-muted-foreground">Executive Sedan</p>
                        </div>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>License Plate:</span>
                          <span className="font-medium">DEF-9012</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Capacity:</span>
                          <span className="font-medium">4 passengers</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Status:</span>
                          <Badge variant="secondary">Maintenance</Badge>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                          <Truck className="h-6 w-6 text-yellow-600" />
                        </div>
                        <div>
                          <h4 className="font-medium">2021 Mercedes Sprinter</h4>
                          <p className="text-sm text-muted-foreground">Group Transport</p>
                        </div>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>License Plate:</span>
                          <span className="font-medium">GHI-3456</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Capacity:</span>
                          <span className="font-medium">14 passengers</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Status:</span>
                          <Badge variant="default" className="bg-green-500">Active</Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Fleet Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Total Vehicles</span>
                    <span className="font-medium">12</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Active Vehicles</span>
                    <span className="font-medium text-green-600">10</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">In Maintenance</span>
                    <span className="font-medium text-yellow-600">2</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Average Age</span>
                    <span className="font-medium">2.3 years</span>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-3">Vehicle Types</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Luxury Sedans</span>
                      <span>6</span>
                    </div>
                    <div className="flex justify-between">
                      <span>SUVs</span>
                      <span>4</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Group Transport</span>
                      <span>2</span>
                    </div>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-3">Utilization</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Average Utilization</span>
                      <span>73%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Peak Hours</span>
                      <span>6-9 AM, 5-8 PM</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="service-area" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Primary Service Areas
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="p-4 border rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">Austin Metropolitan Area</span>
                      <Badge variant="default">Primary</Badge>
                    </div>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <p>• Downtown Austin</p>
                      <p>• Austin-Bergstrom International Airport</p>
                      <p>• University of Texas Campus</p>
                      <p>• South by Southwest District</p>
                      <p>• Cedar Park & Round Rock</p>
                    </div>
                    <div className="mt-3 flex justify-between text-sm">
                      <span>Coverage Radius:</span>
                      <span className="font-medium">25 miles</span>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">San Antonio</span>
                      <Badge variant="secondary">Secondary</Badge>
                    </div>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <p>• San Antonio International Airport</p>
                      <p>• Downtown & Riverwalk</p>
                      <p>• Medical Center</p>
                      <p>• Stone Oak Area</p>
                    </div>
                    <div className="mt-3 flex justify-between text-sm">
                      <span>Coverage Radius:</span>
                      <span className="font-medium">15 miles</span>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">Houston Intercity</span>
                      <Badge variant="outline">Limited</Badge>
                    </div>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <p>• George Bush Intercontinental Airport</p>
                      <p>• William P. Hobby Airport</p>
                      <p>• Downtown Houston (by request)</p>
                    </div>
                    <div className="mt-3 flex justify-between text-sm">
                      <span>Service Type:</span>
                      <span className="font-medium">Airport Only</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Coverage Analytics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Total Service Areas</span>
                    <span className="font-medium">3</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Primary Coverage</span>
                    <span className="font-medium">Austin Metro</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Coverage Radius</span>
                    <span className="font-medium">25 miles</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Response Time</span>
                    <span className="font-medium">12 minutes avg</span>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-3">Service Frequency</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Austin Metro</span>
                      <span>85% of trips</span>
                    </div>
                    <div className="flex justify-between">
                      <span>San Antonio</span>
                      <span>12% of trips</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Houston Airports</span>
                      <span>3% of trips</span>
                    </div>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-3">Popular Routes</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Downtown → Airport</span>
                      <span>45 trips/month</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Hotel District → Convention Center</span>
                      <span>32 trips/month</span>
                    </div>
                    <div className="flex justify-between">
                      <span>University → Downtown</span>
                      <span>28 trips/month</span>
                    </div>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-3">Expansion Opportunities</h4>
                  <div className="space-y-2 text-sm">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <div className="font-medium text-blue-900">Dallas-Fort Worth</div>
                      <div className="text-blue-700">High demand, 45 quote requests/month</div>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <div className="font-medium text-green-900">College Station</div>
                      <div className="text-green-700">Growing market, 18 quote requests/month</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="documents" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Required Documents
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="font-medium">Business License</span>
                      </div>
                      <Badge variant="default" className="bg-green-500">Verified</Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      <p>License #: TX-BL-2024-001234</p>
                      <p>Expires: December 31, 2025</p>
                      <p>Uploaded: March 15, 2024</p>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="font-medium">Commercial Insurance</span>
                      </div>
                      <Badge variant="default" className="bg-green-500">Verified</Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      <p>Policy #: CI-789456123</p>
                      <p>Coverage: $2M Liability</p>
                      <p>Expires: June 30, 2025</p>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-yellow-600" />
                        <span className="font-medium">DOT Registration</span>
                      </div>
                      <Badge variant="secondary">Pending Review</Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      <p>DOT #: 3456789</p>
                      <p>Uploaded: December 28, 2024</p>
                      <p>Status: Under verification</p>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <XCircle className="h-4 w-4 text-red-600" />
                        <span className="font-medium">Safety Certificate</span>
                      </div>
                      <Badge variant="destructive">Missing</Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      <p>Required for full approval</p>
                      <p>Must be current and valid</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Vehicle Documents</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">2023 Mercedes S-Class</span>
                      <Badge variant="default" className="bg-green-500">Complete</Badge>
                    </div>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <div className="flex justify-between">
                        <span>Registration:</span>
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      </div>
                      <div className="flex justify-between">
                        <span>Insurance:</span>
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      </div>
                      <div className="flex justify-between">
                        <span>Inspection:</span>
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">2022 Cadillac Escalade</span>
                      <Badge variant="default" className="bg-green-500">Complete</Badge>
                    </div>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <div className="flex justify-between">
                        <span>Registration:</span>
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      </div>
                      <div className="flex justify-between">
                        <span>Insurance:</span>
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      </div>
                      <div className="flex justify-between">
                        <span>Inspection:</span>
                        <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">2023 BMW 7 Series</span>
                      <Badge variant="secondary">Incomplete</Badge>
                    </div>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <div className="flex justify-between">
                        <span>Registration:</span>
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      </div>
                      <div className="flex justify-between">
                        <span>Insurance:</span>
                        <XCircle className="h-4 w-4 text-red-600" />
                      </div>
                      <div className="flex justify-between">
                        <span>Inspection:</span>
                        <XCircle className="h-4 w-4 text-red-600" />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Document Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">8</div>
                  <p className="text-sm text-green-700">Verified Documents</p>
                </div>
                <div className="text-center p-4 bg-yellow-50 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">2</div>
                  <p className="text-sm text-yellow-700">Pending Review</p>
                </div>
                <div className="text-center p-4 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">3</div>
                  <p className="text-sm text-red-700">Missing Documents</p>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">75%</div>
                  <p className="text-sm text-blue-700">Compliance Score</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audit" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Audit Trail
              </CardTitle>
              <CardDescription>
                Complete history of affiliate account changes and activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Application Approved</span>
                      <span className="text-sm text-muted-foreground">2 hours ago</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Super Admin (<EMAIL>) approved affiliate application
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Document Uploaded</span>
                      <span className="text-sm text-muted-foreground">1 day ago</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      DOT Registration certificate uploaded by affiliate
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Rate Card Updated</span>
                      <span className="text-sm text-muted-foreground">3 days ago</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Sedan airport transfer rate changed from $90.00 to $85.00
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Vehicle Added</span>
                      <span className="text-sm text-muted-foreground">1 week ago</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      2023 BMW 7 Series (DEF-9012) added to fleet
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Service Area Expanded</span>
                      <span className="text-sm text-muted-foreground">2 weeks ago</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Added San Antonio to secondary service areas
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Compliance Issue</span>
                      <span className="text-sm text-muted-foreground">3 weeks ago</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Insurance document expired - affiliate notified
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Profile Updated</span>
                      <span className="text-sm text-muted-foreground">1 month ago</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Contact information and business address updated
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Account Created</span>
                      <span className="text-sm text-muted-foreground">2 months ago</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Affiliate account created and application submitted
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-center">
                <Button variant="outline">Load More History</Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Activity Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">47</div>
                  <p className="text-sm text-blue-700">Total Activities</p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">12</div>
                  <p className="text-sm text-green-700">This Month</p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">3</div>
                  <p className="text-sm text-purple-700">Admin Actions</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
