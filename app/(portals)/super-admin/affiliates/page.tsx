"use client";

import { useState, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, useEffect } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import { Input } from "@/app/components/ui/input";
import { <PERSON><PERSON> } from "@/app/components/ui/button";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import {
  Search,
  Download,
  Clock,
  Car,
  Building2,
  Star,
  AlertTriangle,
  ExternalLink,
  MapPin,
  Users,
} from "lucide-react";

import { Badge } from "@/app/components/ui/badge";

import { useQuery } from "@tanstack/react-query";

// API function to fetch live data
const fetchLiveData = async () => {
  const response = await fetch("/api/super-admin/affiliates/live-data");
  if (!response.ok) {
    throw new Error("Failed to fetch live data");
  }
  return response.json();
};

// Debounce utility
const debounce = (func: Function, delay: number) => {
  let timeoutId: NodeJS.Timeout;
  return (...args: any[]) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(null, args), delay);
  };
};

export default function AffiliateOperationsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [locationFilter, setLocationFilter] = useState("all");

  // Fetch live data using React Query
  const {
    data: liveData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["super-admin-affiliates-live-data"],
    queryFn: fetchLiveData,
    refetchInterval: 30000, // Refresh every 30 seconds
    retry: 3,
  });



  const affiliateStats = liveData?.data?.affiliate_stats || {};
  const operationalAlerts = liveData?.data?.operational_alerts || [];

  // Get all affiliates with operations (from affiliate_stats)
  const affiliatesWithOperations = Object.values(affiliateStats).map((stats: any) => ({
    id: stats.affiliate_id,
    name: stats.name,
    city: stats.city,
    state: stats.state,
    rating: stats.rating,
    status: 'active', // Assume active if they have operations
  }));

  // Debounced search handler
  const debouncedSetSearchQuery = useMemo(
    () => debounce(setSearchQuery, 300),
    []
  );

  const handleSearch = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      debouncedSetSearchQuery(e.target.value);
    },
    [debouncedSetSearchQuery]
  );

  const statusOptions = [
    { value: "all", label: "All Status" },
    { value: "active", label: "Active" },
    { value: "pending", label: "Pending Review" },
    { value: "inactive", label: "Inactive" },
    { value: "verification", label: "In Verification" },
  ];

  const locationOptions = [
    { value: "all", label: "All Locations" },
    { value: "CA", label: "California" },
    { value: "NY", label: "New York" },
    { value: "FL", label: "Florida" },
    { value: "IL", label: "Illinois" },
    { value: "TX", label: "Texas" },
  ];

  // Filter affiliates for the cards (only show those with current/upcoming operations)
  const activeAffiliates = useMemo(() => {
    return affiliatesWithOperations.filter((affiliate: any) => {
      const stats = affiliateStats[affiliate.id] || {};
      // Show affiliates with ANY current or upcoming operations (offers received, trips scheduled, etc.)
      const hasCurrentOperations = stats.total_offers > 0 || stats.active_trips > 0;

      // Apply search filter
      if (searchQuery && !affiliate.name.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }

      // Apply status filter
      if (statusFilter !== "all" && affiliate.status !== statusFilter) {
        return false;
      }

      // Apply location filter
      if (locationFilter !== "all" && affiliate.state !== locationFilter) {
        return false;
      }

      return hasCurrentOperations; // Show affiliates with any current/upcoming operations
    });
  }, [affiliatesWithOperations, affiliateStats, searchQuery, statusFilter, locationFilter]);

  // Calculate current/upcoming operations stats
  const stats = useMemo(() => {
    const summary = liveData?.data?.summary || {};

    // Count affiliates with ANY current/upcoming operations (offers received, trips scheduled)
    const affiliatesWithCurrentOps = affiliatesWithOperations.filter((a: any) => {
      const stats = affiliateStats[a.id];
      return stats && (stats.total_offers > 0 || stats.active_trips > 0);
    }).length;

    // Total pending offers across all affiliates (awaiting response)
    const totalPendingOffers = Object.values(affiliateStats).reduce((sum: number, stats: any) =>
      sum + (stats.pending_offers || 0), 0);

    // Total active trips across all affiliates
    const totalActiveTrips = Object.values(affiliateStats).reduce((sum: number, stats: any) =>
      sum + (stats.active_trips || 0), 0);

    // Calculate average response time for current operations only
    const affiliatesWithOffers = affiliatesWithOperations.filter((a: any) => {
      const stats = affiliateStats[a.id];
      return stats && stats.total_offers > 0;
    });

    const avgResponseTime = affiliatesWithOffers.length > 0
      ? affiliatesWithOffers.reduce((sum: any, a: any) => {
          const stats = affiliateStats[a.id];
          return sum + (stats?.avg_response_time_hours || 0);
        }, 0) / affiliatesWithOffers.length
      : 0;

    // Calculate average rating for affiliates with current operations
    const avgRating = affiliatesWithCurrentOps > 0
      ? (affiliatesWithOperations
          .filter((a: any) => {
            const stats = affiliateStats[a.id];
            return stats && (stats.total_offers > 0 || stats.active_trips > 0);
          })
          .reduce((sum: any, a: any) => sum + (a.rating || 0), 0) / affiliatesWithCurrentOps).toFixed(1)
      : "0.0";

    return {
      affiliatesWithCurrentOps,
      totalPendingOffers,
      totalActiveTrips,
      avgResponseTime: avgResponseTime.toFixed(1),
      avgRating,
      alerts: summary.total_alerts || 0,
    };
  }, [liveData, affiliatesWithOperations, affiliateStats]);

  // Auto-refresh data every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      refetch();
    }, 30000);

    return () => clearInterval(interval);
  }, [refetch]);

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-red-600">Error Loading Data</h3>
          <p className="text-muted-foreground">Failed to load affiliate operations data</p>
          <Button onClick={() => refetch()} className="mt-4">
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            Live Operations Dashboard
          </h2>
          <p className="text-muted-foreground">
            Monitor current and upcoming operations for all affiliates with received offers or scheduled trips
          </p>
        </div>
      </div>

      {/* Live Operations Stats */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Building2 className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">
                {isLoading ? "-" : stats.affiliatesWithCurrentOps}
              </div>
              <div className="text-sm text-muted-foreground">
                Current Operations
              </div>
            </div>
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Clock className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">
                {isLoading ? "-" : stats.totalPendingOffers}
              </div>
              <div className="text-sm text-muted-foreground">
                Pending Quotes
              </div>
            </div>
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-green-100 rounded-lg">
              <Car className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">
                {isLoading ? "-" : stats.totalActiveTrips}
              </div>
              <div className="text-sm text-muted-foreground">Active Trips</div>
            </div>
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Clock className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">
                {isLoading ? "-" : `${stats.avgResponseTime}h`}
              </div>
              <div className="text-sm text-muted-foreground">Avg Response</div>
            </div>
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-yellow-50 rounded-lg">
              <Star className="h-5 w-5 text-yellow-500" />
            </div>
            <div>
              <div className="text-2xl font-bold">
                {isLoading ? "-" : stats.avgRating}
              </div>
              <div className="text-sm text-muted-foreground">Performance</div>
            </div>
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-red-100 rounded-lg">
              <AlertTriangle className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">
                {isLoading ? "-" : stats.alerts}
              </div>
              <div className="text-sm text-muted-foreground">Active Alerts</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h3 className="text-lg font-semibold">Current Operations</h3>
              <p className="text-sm text-muted-foreground">
                Affiliates with received offers or scheduled trips (regardless of response status)
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((opt) => (
                    <SelectItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={locationFilter} onValueChange={setLocationFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Location" />
                </SelectTrigger>
                <SelectContent>
                  {locationOptions.map((opt) => (
                    <SelectItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search affiliates..."
                  className="w-[250px] pl-9"
                  onChange={handleSearch}
                />
              </div>
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Live Affiliate Operations */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        {activeAffiliates.length === 0 ? (
          <div className="col-span-full text-center py-8 text-muted-foreground">
            No affiliates with current or upcoming operations found
          </div>
        ) : (
          activeAffiliates.map((affiliate: any) => {
            const stats = affiliateStats[affiliate.id] || {};
            const hasCurrentOps = stats.total_offers > 0 || stats.active_trips > 0;

          return (
            <Card key={affiliate.id} className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Building2 className="h-5 w-5 text-blue-600" />
                  <span className="font-bold">{affiliate.name}</span>
                  <Badge variant="outline">{affiliate.city}, {affiliate.state}</Badge>
                  {hasCurrentOps && <Badge variant="default" className="bg-green-500">LIVE</Badge>}
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" asChild>
                    <a href={`/super-admin/affiliates/${affiliate.id}`}>
                      <ExternalLink className="h-3 w-3 mr-1" />
                      Profile
                    </a>
                  </Button>
                  <Button variant="outline" size="sm" asChild>
                    <a href={`/super-admin/quotes?affiliate=${affiliate.id}`}>
                      <ExternalLink className="h-3 w-3 mr-1" />
                      Quotes
                    </a>
                  </Button>
                </div>
              </div>

              {/* Current/Upcoming Operations Status */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs mb-3">
                <Button
                  variant="ghost"
                  className="p-2 bg-blue-50 rounded h-auto flex-col hover:bg-blue-100 transition-colors"
                  asChild
                >
                  <a href={`/super-admin/quotes?affiliate=${affiliate.id}&status=all`}>
                    <div className="font-bold text-blue-600">{stats.total_offers || 0}</div>
                    <div className="text-muted-foreground">Total Offers</div>
                  </a>
                </Button>
                <Button
                  variant="ghost"
                  className="p-2 bg-orange-50 rounded h-auto flex-col hover:bg-orange-100 transition-colors"
                  asChild
                >
                  <a href={`/super-admin/quotes?affiliate=${affiliate.id}&status=pending`}>
                    <div className="font-bold text-orange-600">{stats.pending_offers || 0}</div>
                    <div className="text-muted-foreground">Awaiting Response</div>
                  </a>
                </Button>
                <Button
                  variant="ghost"
                  className="p-2 bg-green-50 rounded h-auto flex-col hover:bg-green-100 transition-colors"
                  asChild
                >
                  <a href={`/super-admin/quotes?affiliate=${affiliate.id}&status=accepted`}>
                    <div className="font-bold text-green-600">{stats.accepted_offers || 0}</div>
                    <div className="text-muted-foreground">Accepted</div>
                  </a>
                </Button>
                <Button
                  variant="ghost"
                  className="p-2 bg-purple-50 rounded h-auto flex-col hover:bg-purple-100 transition-colors"
                  asChild
                >
                  <a href={`/super-admin/trips?affiliate=${affiliate.id}&status=active`}>
                    <div className="font-bold text-purple-600">{stats.active_trips || 0}</div>
                    <div className="text-muted-foreground">Scheduled Trips</div>
                  </a>
                </Button>
              </div>

              {/* Service Coverage */}
              <div className="mb-3">
                <div className="flex items-center gap-2 mb-1">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">Service Coverage</span>
                </div>
                <div className="text-xs text-muted-foreground">
                  {stats.cities_served?.length ? `${stats.cities_served.length} cities` : 'No coverage data'}
                </div>
              </div>

              {/* Driver Status */}
              <div className="mb-3">
                <div className="flex items-center gap-2 mb-1">
                  <Users className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">Driver Status</span>
                </div>
                <div className="text-xs text-muted-foreground">
                  {stats.total_drivers || 0} drivers registered
                </div>
              </div>

              {/* Urgent Actions Needed */}
              {stats.oldest_pending_quote && (
                <Button
                  variant="ghost"
                  className="mt-3 p-2 bg-orange-50 border border-orange-200 rounded h-auto w-full hover:bg-orange-100 transition-colors"
                  asChild
                >
                  <a href={`/super-admin/quotes/${stats.oldest_pending_quote.quote_id}`}>
                    <div className="text-left w-full">
                      <div className="font-semibold text-xs text-orange-800">Urgent: Quote Pending</div>
                      <div className="text-xs text-orange-700">
                        {stats.oldest_pending_quote.reference_number} - {stats.oldest_pending_quote.city}
                        <br />
                        Pending since: {new Date(stats.oldest_pending_quote.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  </a>
                </Button>
              )}
            </Card>
          );
        })
        )}
      </div>

      {/* Operational Alerts */}
      {operationalAlerts.length > 0 && (
        <Card className="mb-4">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              Operational Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {operationalAlerts.map((alert: any) => (
                <div
                  key={alert.id}
                  className="flex items-center gap-2 p-3 bg-red-50 border-l-4 border-red-400 rounded"
                >
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <div className="flex-1">
                    <span className="font-medium">
                      {alert.affiliate_name
                        ? `${alert.affiliate_name}: `
                        : "System Alert: "}
                    </span>
                    <span>{alert.message}</span>
                  </div>
                  <Button variant="outline" size="sm">
                    Resolve
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}