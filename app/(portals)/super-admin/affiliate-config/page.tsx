"use client"

import { useState, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/app/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import {
  Search,
  Plus,
  MoreHorizontal,
  Network,
  Filter,
  MapPin,
  Star,
  CheckCircle,
  XCircle,
  Car,
  ArrowUpDown,
  Download,
  Check,
  X,
  CheckCircle2,
  ShieldAlert,
  FileWarning,
  Hourglass,
  Users,
  Truck,
  ClipboardCheck,
  Clock
} from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table"
import { Badge } from "@/app/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/app/components/ui/dropdown-menu"
import { useToast } from "@/app/components/ui/use-toast"
import { Progress } from "@/app/components/ui/progress"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { Label } from "@/app/components/ui/label"
import { Switch } from "@/app/components/ui/switch"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/app/components/ui/dialog"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/app/components/ui/alert-dialog"
import { RatesFleetDetailsDrawer } from "./RatesFleetDetailsDrawer"
import { ComplianceDetailsDrawer } from "./ComplianceDetailsDrawer"
import { GeneralDetailsDrawer } from "./GeneralDetailsDrawer"
import { ApplicationDetailsDrawer } from "./ApplicationDetailsDrawer"
import { RejectionDialog } from "./RejectionDialog"
import { Loader2 } from "lucide-react"
import { Affiliate, PartnerData, ComplianceEntry, RateData, AffiliateStatus } from "@/app/lib/types/affiliates"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"

const mockApplications = [
  {
    id: "app_001",
    name: "Executive Car Fleet",
    city: "Chicago",
    state: "IL",
    status: "pending",
    progress: 40,
    submitted: "2024-06-01",
    email: "<EMAIL>"
  },
  {
    id: "app_002",
    name: "Coastal Transfers",
    city: "Miami",
    state: "FL",
    status: "in_verification",
    progress: 70,
    submitted: "2024-05-28",
    email: "<EMAIL>"
  },
  {
    id: "app_003",
    name: "Golden State Chauffeurs",
    city: "Los Angeles",
    state: "CA",
    status: "approved",
    progress: 100,
    submitted: "2024-05-20",
    email: "<EMAIL>"
  }
]

function debounce<T extends (...args: any[]) => void>(fn: T, wait: number) {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => fn(...args), wait);
  };
}

// --- Rates Management ---
const vehicleTypes = [
  { id: "sedan", label: "SEDAN" },
  { id: "suv", label: "SUV" },
  { id: "luxury_sedan", label: "LUXURY SEDAN" },
  { id: "luxury_suv", label: "LUXURY SUV" },
  { id: "sprinter", label: "MERCEDES SPRINTER" },
  { id: "executive_sprinter", label: "EXECUTIVE SPRINTER" },
  { id: "stretch", label: "STRETCH LIMOUSINE" },
  { id: "hummer", label: "HUMMER LIMOUSINE" },
  { id: "sprinter_limo", label: "SPRINTER LIMO" },
  { id: "minibus", label: "MINI BUS" },
  { id: "motorcoach", label: "MOTOR COACH" },
  { id: "tesla", label: "TESLA" },
  { id: "party_bus", label: "PARTY BUS" },
  { id: "wheelchair", label: "WHEELCHAIR ACCESSIBLE VEHICLE" },
]

const ratesFormSchema = z.object({
  vehicles: z.array(z.object({
    type: z.string(),
    enabled: z.boolean(),
    useDistanceTime: z.boolean().default(false),
    pointToPointRate: z.string().optional(),
    baseDistanceRate: z.string().optional(),
    perMileRate: z.string().optional(),
    perHourRate: z.string().optional(),
    minimumMiles: z.string().optional(),
    extraHourRate: z.string().optional(),
    airportRate: z.string().optional(),
    hourlyRate: z.string().optional(),
    minimumHours: z.string().optional(),
    cancellationPolicy: z.string().optional(),
  })),
  seasonalRanges: z.array(z.object({
    id: z.string(),
    name: z.string().min(1, "Please enter a name for this seasonal period"),
    startDate: z.date(),
    endDate: z.date(),
    description: z.string().optional(),
  })),
})

// --- Fleet Management ---
const fleetVehicleSchema = z.object({
  typeId: z.string().min(1, "Please select a vehicle type"),
  make: z.string().min(1, "Please enter the make"),
  model: z.string().min(1, "Please enter the model"),
  year: z.string().min(4, "Please enter a valid year"),
  capacity: z.string().min(1, "Please enter the capacity"),
  status: z.string().default("active"),
})

// --- Mock Data ---
const mockPartners: PartnerData[] = [
  { id: 'partner1', name: 'Prestige Limo', location: 'New York, NY', fleetSize: 15, status: 'Active', configProgress: 80 },
  { id: 'partner2', name: 'Executive Transport', location: 'Los Angeles, CA', fleetSize: 25, status: 'Active', configProgress: 100 },
  { id: 'partner3', name: 'City Cruisers', location: 'Chicago, IL', fleetSize: 10, status: 'Pending Approval', configProgress: 30 },
  { id: 'partner4', name: 'Global Shuttles', location: 'Miami, FL', fleetSize: 12, status: 'Compliance Review', configProgress: 60 },
];

const mockComplianceData: ComplianceEntry[] = [
  { partnerId: 'partner1', overallStatus: 'Verified', insuranceStatus: 'Verified', insuranceExpiry: '2025-01-01', licenseStatus: 'Verified', licenseExpiry: '2024-12-01', driverDocsStatus: 'Verified', vehicleDocsStatus: 'Verified', lastAudit: '2024-03-01' },
  { partnerId: 'partner2', overallStatus: 'Verified', insuranceStatus: 'Verified', insuranceExpiry: '2025-02-15', licenseStatus: 'Verified', licenseExpiry: '2025-01-10', driverDocsStatus: 'Verified', vehicleDocsStatus: 'Verified', lastAudit: '2024-04-10' },
  { partnerId: 'partner3', overallStatus: 'Pending', insuranceStatus: 'Missing', insuranceExpiry: 'N/A', licenseStatus: 'Pending', licenseExpiry: 'N/A', driverDocsStatus: 'Missing', vehicleDocsStatus: 'Pending', lastAudit: 'N/A' },
  { partnerId: 'partner4', overallStatus: 'Issues Found', insuranceStatus: 'Expired', insuranceExpiry: '2024-06-01', licenseStatus: 'Verified', licenseExpiry: '2024-11-01', driverDocsStatus: 'Verified', vehicleDocsStatus: 'Issues Found', lastAudit: '2024-05-20' },
];

// Mock Rates Data (Example)
const mockRatesData: { [partnerId: string]: RateData[] } = {
  partner1: [{ vehicleType: 'Sedan', p2pRate: 80, hourlyRate: 75, minHours: 3 }, { vehicleType: 'SUV', p2pRate: 100, hourlyRate: 95, minHours: 3 }],
  partner2: [{ vehicleType: 'Sedan', hourlyRate: 80, minHours: 4 }, { vehicleType: 'SUV', hourlyRate: 100, minHours: 4 }, { vehicleType: 'Sprinter', hourlyRate: 150, minHours: 5 }],
  // partner3, partner4 might not have rates configured yet
};

export default function AffiliateConfigPage() {
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [locationFilter, setLocationFilter] = useState("all")
  const [tenantFilter, setTenantFilter] = useState("all")
  const [showAddModal, setShowAddModal] = useState(false)
  const [tab, setTab] = useState("general")
  const [selectedPartnerIdForTab, setSelectedPartnerIdForTab] = useState<string>("all")
  const [showRatesDrawer, setShowRatesDrawer] = useState(false)
  const [showComplianceDrawer, setShowComplianceDrawer] = useState(false)
  const [showGeneralDrawer, setShowGeneralDrawer] = useState(false)
  const [showApplicationDrawer, setShowApplicationDrawer] = useState(false)
  const [selectedAffiliateForDrawer, setSelectedAffiliateForDrawer] = useState<Affiliate | null>(null)
  const [selectedApplicationForDrawer, setSelectedApplicationForDrawer] = useState<any>(null)
  const [selectedApplicationForRejection, setSelectedApplicationForRejection] = useState<{ id: string, name: string } | null>(null)
  const [showRejectionDialog, setShowRejectionDialog] = useState(false)
  const [isLoading, setIsLoading] = useState(true);
  const [partners, setPartners] = useState<PartnerData[]>(mockPartners);
  const [compliance, setCompliance] = useState<ComplianceEntry[]>(mockComplianceData);
  const [rates, setRates] = useState<{ [partnerId: string]: RateData[] }>(mockRatesData);
  const [activeTab, setActiveTab] = useState<string>("affiliates")
  const queryClient = useQueryClient()

  // Function to refresh affiliates data
  const fetchAffiliates = () => {
    queryClient.invalidateQueries({ queryKey: ['affiliates'] })
  }

  const getStatusBadgeClass = (status: AffiliateStatus) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'inactive':
        return 'bg-red-100 text-red-800'
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date)
  }

  const handleStatusChange = (id: string, newStatus: AffiliateStatus) => {
    updateAffiliateMutation.mutate({ id, status: newStatus })
  }

  const renderRatingStars = (rating: number) => {
    return (
      <div className="flex items-center">
        <div className="mr-1">{rating.toFixed(1)}</div>
        <div className="flex">
          {[1, 2, 3, 4, 5].map(star => (
            <Star
              key={star}
              className={`h-3 w-3 ${star <= Math.round(rating) ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`}
            />
          ))}
        </div>
      </div>
    )
  }

  // Debounced search handler
  const debouncedSetSearchQuery = useMemo(() => debounce(setSearchQuery, 300), [])
  const handleSearch = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    debouncedSetSearchQuery(e.target.value)
  }, [debouncedSetSearchQuery])

  // Memoized filtered applications
  const filteredApplications = useMemo(() => {
    if (!searchQuery) return mockApplications
    return mockApplications.filter(a =>
      a.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      a.city.toLowerCase().includes(searchQuery.toLowerCase()) ||
      a.state.toLowerCase().includes(searchQuery.toLowerCase())
    )
  }, [searchQuery])

  // --- Rates State ---
  const [seasonalRanges, setSeasonalRanges] = useState([])
  const ratesForm = useForm({
    resolver: zodResolver(ratesFormSchema),
    defaultValues: {
      vehicles: vehicleTypes.map(type => ({
        type: type.id,
        enabled: false,
        useDistanceTime: false,
        pointToPointRate: "",
        baseDistanceRate: "",
        perMileRate: "",
        perHourRate: "",
        minimumMiles: "",
        extraHourRate: "",
        airportRate: "",
        hourlyRate: "",
        minimumHours: "",
        cancellationPolicy: "",
      })),
      seasonalRanges: [],
    },
  })
  // --- Fleet State ---
  const [fleet, setFleet] = useState([]) // TODO: Replace with backend data
  const fleetForm = useForm({
    resolver: zodResolver(fleetVehicleSchema),
    defaultValues: { status: "active" },
  })
  const [showFleetModal, setShowFleetModal] = useState(false)
  const [editingVehicle, setEditingVehicle] = useState(null)

  const complianceSummary = useMemo(() => {
    let issues = 0;
    let expired = 0;
    let pending = 0;
    compliance.forEach(data => {
      if (data.overallStatus === 'Issues Found' ||
          data.vehicleDocsStatus === 'Issues Found') issues++;
      if (data.insuranceStatus === 'Expired' ||
          data.licenseStatus === 'Expired') expired++;
      if (data.overallStatus === 'Pending' ||
          data.insuranceStatus === 'Pending' ||
          data.licenseStatus === 'Pending' ||
          data.driverDocsStatus === 'Pending' ||
          data.vehicleDocsStatus === 'Pending') pending++;
    });
    return { issues, expired, pending };
  }, [compliance]);

  // Function to open Rates drawer
  const handleOpenRatesDetails = (affiliate: Affiliate) => {
    setSelectedAffiliateForDrawer(affiliate);
    setShowRatesDrawer(true);
  };

  // Function to open Compliance drawer
  const handleOpenComplianceDetails = (affiliate: Affiliate) => {
    setSelectedAffiliateForDrawer(affiliate);
    setShowComplianceDrawer(true);
  };

  // Function to open General drawer
  const handleOpenGeneralDetails = (affiliate: Affiliate) => {
    setSelectedAffiliateForDrawer(affiliate);
    setShowGeneralDrawer(true);
  };

  // Function to open Application drawer
  const handleOpenApplicationDetails = (application: any) => {
    setSelectedApplicationForDrawer(application);
    setShowApplicationDrawer(true);
  };

  // Function to approve application
  const handleApproveApplication = async (applicationId: string) => {
    try {
      const response = await fetch(`/api/super-admin/affiliates/${applicationId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notes: 'Approved via super admin interface',
          send_notification: true
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Application approved successfully:', result)
        // Show success message
        alert('Application approved successfully! Notification sent to affiliate.')
        // Refresh the data
        fetchAffiliates()
      } else {
        const error = await response.json()
        console.error('Failed to approve application:', error)
        alert('Failed to approve application: ' + (error.message || 'Unknown error'))
      }
    } catch (error) {
      console.error('Error approving application:', error)
      alert('Error approving application: ' + (error instanceof Error ? error.message : String(error)))
    }
  };

  // Function to reject application
  const handleRejectApplication = async (applicationId: string, affiliateName: string) => {
    setSelectedApplicationForRejection({ id: applicationId, name: affiliateName })
    setShowRejectionDialog(true)
  };

  // Function to handle rejection with structured reasons
  const handleRejectionSubmit = async (reasons: string[], customReason: string) => {
    if (!selectedApplicationForRejection) return

    try {
      const response = await fetch(`/api/super-admin/affiliates/${selectedApplicationForRejection.id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reasons: reasons,
          customReason: customReason,
          send_notification: true
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Application rejected successfully:', result)
        // Show success message
        alert(`Application rejected successfully! Notification sent to ${selectedApplicationForRejection.name}.`)
        // Refresh the data
        fetchAffiliates()
      } else {
        const error = await response.json()
        console.error('Failed to reject application:', error)
        alert('Failed to reject application: ' + (error.message || 'Unknown error'))
      }
    } catch (error) {
      console.error('Error rejecting application:', error)
      alert('Error rejecting application: ' + (error instanceof Error ? error.message : String(error)))
    }
  };

  useEffect(() => {
    // Simulate initial data load
    setTimeout(() => {
      setIsLoading(false);
    }, 500);
  }, [])

  // Add React Query for fetching affiliates
  const { data: affiliatesResponse, isLoading: dataLoading, error: dataError } = useQuery({
    queryKey: ['affiliates'],
    queryFn: async () => {
      const response = await fetch('/api/super-admin/affiliates')
      if (!response.ok) {
        throw new Error('Failed to fetch affiliates')
      }
      return response.json()
    }
  })

  // Add mutation for changing affiliate status
  const updateAffiliateMutation = useMutation({
    mutationFn: async ({ id, status }: { id: string, status: string }) => {
      const response = await fetch(`/api/super-admin/affiliates/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      })

      if (!response.ok) {
        throw new Error('Failed to update affiliate status')
      }

      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['affiliates'] })
      toast({
        title: "Status updated",
        description: "Affiliate status has been updated successfully.",
      })
    },
    onError: (error) => {
      toast({
        title: "Update failed",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      })
    }
  })

  // Use fetched data instead of mock data
  const affiliates = affiliatesResponse?.affiliates || [];
  const availableCities = affiliatesResponse?.cities || [];

  // Apply filters
  const filteredAffiliates = useMemo(() => {
    if (!affiliates) return []

    return affiliates.filter((affiliate: any) => {
      // Apply search
      if (searchQuery &&
          !affiliate.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !affiliate.address.city.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !affiliate.address.state.toLowerCase().includes(searchQuery.toLowerCase())
         ) {
        return false
      }

      // Apply status filter
      if (statusFilter !== "all" && affiliate.status !== statusFilter) {
        return false
      }

      // Apply location filter
      if (locationFilter !== "all" && affiliate.address.city !== locationFilter) {
        return false
      }

      // Apply affiliate name filter (changed from tenant filter)
      if (tenantFilter !== "all" && affiliate.name !== tenantFilter) {
        return false
      }

      return true
    })
  }, [affiliates, searchQuery, statusFilter, locationFilter, tenantFilter])

  // Filter data for Rates/Compliance tabs based on selectedPartnerIdForTab
  const affiliatesForTabs = useMemo(() => {
    if (selectedPartnerIdForTab === "all") {
      return filteredAffiliates;
    }
    return filteredAffiliates.filter((a: Affiliate) => a.id === selectedPartnerIdForTab);
  }, [filteredAffiliates, selectedPartnerIdForTab]);

  // --- Mock Stats Data ---
  const affiliateStats = useMemo(() => {
    const totalPartners = affiliates.length;
    const pendingPartners = affiliates.filter((a: any) => a.status === 'pending').length;
    const complianceIssues = affiliates.filter((a: any) => a.verificationStatus !== "verified").length;
    // For total fleet, we might need to sum from partner details if not readily available
    const totalActiveFleet = affiliates
      .filter((p: any) => p.status === 'active')
      .length; // Since we don't have fleetSize, just count active affiliates

    return {
      totalPartners,
      pendingPartners,
      complianceIssues,
      totalActiveFleet
    };
  }, [affiliates]);

  // Handle loading state
  if (dataLoading) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Affiliate Configuration</h2>
        </div>
        <div className="flex items-center justify-center h-64">
          <p>Loading affiliate data...</p>
        </div>
      </div>
    )
  }

  // Handle error state
  if (dataError) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Affiliate Configuration</h2>
        </div>
        <div className="flex items-center justify-center h-64 text-red-500">
          <p>Error loading affiliate data: {dataError instanceof Error ? dataError.message : 'Unknown error'}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Affiliate Onboarding Management</h1>
          <p className="text-muted-foreground">
            Control center for affiliate applications, compliance, and duty of care oversight
          </p>
        </div>
        <Button onClick={() => setShowAddModal(true)} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="mr-2 h-4 w-4" />
          Add New Affiliate
        </Button>
      </div>

      {/* Enhanced Stats Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Partners</CardTitle>
            <div className="p-2 bg-blue-100 rounded-lg">
              <Network className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-600">{affiliateStats.totalPartners}</div>
            <p className="text-xs text-muted-foreground mt-1">
              +3 new this month
            </p>
            <div className="mt-2">
              <div className="text-xs text-muted-foreground">Active: {affiliateStats.totalPartners - affiliateStats.pendingPartners}</div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-yellow-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Onboarding</CardTitle>
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Hourglass className="h-4 w-4 text-yellow-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-yellow-600">{affiliateStats.pendingPartners}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Awaiting review
            </p>
            <div className="mt-2">
              <Progress value={(affiliateStats.pendingPartners / affiliateStats.totalPartners) * 100} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-red-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Compliance Issues</CardTitle>
            <div className="p-2 bg-red-100 rounded-lg">
              <ShieldAlert className="h-4 w-4 text-red-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-red-600">{complianceSummary.issues}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Require immediate attention
            </p>
            <div className="mt-2 space-y-1">
              <div className="text-xs text-muted-foreground">Expired docs: {complianceSummary.expired}</div>
              <div className="text-xs text-muted-foreground">Pending review: {complianceSummary.pending}</div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Active Fleet</CardTitle>
            <div className="p-2 bg-green-100 rounded-lg">
              <Truck className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-600">{affiliateStats.totalActiveFleet}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Vehicles in service
            </p>
            <div className="mt-2">
              <div className="text-xs text-muted-foreground">Avg per affiliate: {Math.round(affiliateStats.totalActiveFleet / affiliateStats.totalPartners)}</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Application Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl flex items-center gap-2">
            <ClipboardCheck className="h-5 w-5" />
            Application Progress Overview
          </CardTitle>
          <CardDescription>
            Track affiliate onboarding progress and duty of care compliance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-4">
              <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">Application Stages</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm">Initial Submission</span>
                  </div>
                  <Badge variant="outline">12 pending</Badge>
                </div>
                <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <span className="text-sm">Document Review</span>
                  </div>
                  <Badge variant="outline">8 in review</Badge>
                </div>
                <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span className="text-sm">Compliance Check</span>
                  </div>
                  <Badge variant="outline">5 checking</Badge>
                </div>
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">Final Approval</span>
                  </div>
                  <Badge variant="outline">3 ready</Badge>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">Duty of Care Status</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Insurance Verified</span>
                  </div>
                  <span className="text-sm font-medium">85%</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Driver Background</span>
                  </div>
                  <span className="text-sm font-medium">78%</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm">Vehicle Inspection</span>
                  </div>
                  <span className="text-sm font-medium">65%</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span className="text-sm">Safety Training</span>
                  </div>
                  <span className="text-sm font-medium">45%</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">Recent Activity</h4>
              <div className="space-y-3">
                <div className="p-3 border rounded-lg">
                  <div className="text-sm font-medium">Elite Transportation</div>
                  <div className="text-xs text-muted-foreground">Submitted insurance docs</div>
                  <div className="text-xs text-muted-foreground">2 hours ago</div>
                </div>
                <div className="p-3 border rounded-lg">
                  <div className="text-sm font-medium">Boston Elite Limo</div>
                  <div className="text-xs text-muted-foreground">Completed vehicle inspection</div>
                  <div className="text-xs text-muted-foreground">5 hours ago</div>
                </div>
                <div className="p-3 border rounded-lg">
                  <div className="text-sm font-medium">Company 1</div>
                  <div className="text-xs text-muted-foreground">Application approved</div>
                  <div className="text-xs text-muted-foreground">1 day ago</div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
            <div>
              <CardTitle>Registered Affiliates</CardTitle>
              <CardDescription>Manage service providers across all tenants</CardDescription>
            </div>
            <div className="flex flex-col md:flex-row gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search affiliates..."
                  className="pl-8 md:w-[250px]"
                  value={searchQuery}
                  onChange={handleSearch}
                />
              </div>
              <Button variant="outline" size="icon" title="Download CSV">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>

              <Select value={locationFilter} onValueChange={setLocationFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by location" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Locations</SelectItem>
                  {availableCities.map((city: string) => (
                    <SelectItem key={city} value={city}>{city}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={tenantFilter} onValueChange={setTenantFilter}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="Filter by affiliate name" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Affiliates</SelectItem>
                  {affiliates.map((affiliate: any) => (
                    <SelectItem key={affiliate.id} value={affiliate.name}>{affiliate.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Tabs value={tab} onValueChange={setTab}>
              <TabsList>
                <TabsTrigger value="general">General</TabsTrigger>
                <TabsTrigger value="rates">Rates & Fleet</TabsTrigger>
                <TabsTrigger value="applications">Applications</TabsTrigger>
                <TabsTrigger value="compliance">Compliance & Audit</TabsTrigger>
              </TabsList>

              <TabsContent value="general">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Affiliate</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Services</TableHead>
                        <TableHead>Rating</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Tenants</TableHead>
                        <TableHead>Last Active</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredAffiliates.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-8">
                            <div className="flex flex-col items-center gap-2">
                              <Network className="h-8 w-8 text-muted-foreground" />
                              <div className="text-sm text-muted-foreground">No affiliates found</div>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredAffiliates.map((affiliate: any) => (
                          <TableRow key={affiliate.id}>
                            <TableCell>
                              <div className="font-medium">{affiliate.name}</div>
                              <div className="text-sm text-muted-foreground">ID: {affiliate.id}</div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <MapPin className="h-3 w-3 mr-1" />
                                {affiliate.address.city}, {affiliate.address.state}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex flex-wrap gap-1">
                                <Badge variant="outline" className="bg-muted text-xs">
                                  Transportation
                                </Badge>
                              </div>
                            </TableCell>
                            <TableCell>
                              {renderRatingStars(affiliate.metrics?.rating || 0)}
                            </TableCell>
                            <TableCell>
                              <Badge className={`${getStatusBadgeClass(affiliate.status)}`}>
                                {affiliate.status.charAt(0).toUpperCase() + affiliate.status.slice(1)}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex flex-col gap-1">
                                {affiliate.tenants?.map((tenant: any) => (
                                  <span key={tenant} className="text-xs">
                                    {tenant}
                                  </span>
                                ))}
                              </div>
                            </TableCell>
                            <TableCell>
                              {affiliate.lastActive ? formatDate(affiliate.lastActive) : 'N/A'}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleOpenGeneralDetails(affiliate)}
                                >
                                  View Details
                                </Button>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" className="h-8 w-8 p-0">
                                      <span className="sr-only">Open menu</span>
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                    <DropdownMenuItem>
                                      Edit Affiliate
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    {affiliate.status === 'active' ? (
                                      <DropdownMenuItem onClick={() => handleStatusChange(affiliate.id, 'inactive')} className="text-red-600">
                                        <XCircle className="mr-2 h-4 w-4" />
                                        Deactivate
                                      </DropdownMenuItem>
                                    ) : (
                                      <DropdownMenuItem onClick={() => handleStatusChange(affiliate.id, 'active')} className="text-green-600">
                                        <CheckCircle className="mr-2 h-4 w-4" />
                                        Activate
                                      </DropdownMenuItem>
                                    )}
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>

              <TabsContent value="rates">
                <div className="space-y-4">
                  {/* Partner Filter for this tab */}
                  <div className="flex flex-wrap gap-2 mb-4 p-4 border rounded-md bg-muted/50">
                     <Select value={selectedPartnerIdForTab} onValueChange={setSelectedPartnerIdForTab}>
                       <SelectTrigger className="w-[250px]">
                         <SelectValue placeholder="Filter by Partner" />
                       </SelectTrigger>
                       <SelectContent>
                         <SelectItem value="all">All Partners</SelectItem>
                         {affiliates.map((aff: any) => ( // Use unfiltered affiliates for the dropdown options
                           <SelectItem key={aff.id} value={aff.id}>{aff.name} ({aff.address.city}, {aff.address.state})</SelectItem>
                         ))}
                       </SelectContent>
                     </Select>
                     {/* Placeholder for other filters */}
                     <Input placeholder="Filter by City..." className="w-[150px]" disabled/>
                     <Select disabled>
                       <SelectTrigger className="w-[180px]">
                         <SelectValue placeholder="Filter by Vehicle Type" />
                       </SelectTrigger>
                       <SelectContent>
                         <SelectItem value="sedan">Sedan</SelectItem>
                       </SelectContent>
                     </Select>
                     <Button variant="outline" disabled>Apply Filters</Button>
                   </div>

                   {/* New Overview Table for Rates & Fleet */}
                   <Card>
                     <CardHeader>
                       <CardTitle>Rates & Fleet Overview</CardTitle>
                       <CardDescription>Summary of rates and fleet size for selected partner(s).</CardDescription>
                     </CardHeader>
                     <CardContent>
                       <div className="rounded-md border">
                         <Table>
                           <TableHeader>
                             <TableRow>
                               <TableHead>Partner</TableHead>
                               <TableHead>Location</TableHead>
                               <TableHead>Fleet Size</TableHead>
                               <TableHead>Status</TableHead>
                               <TableHead className="text-right">Actions</TableHead>
                             </TableRow>
                           </TableHeader>
                           <TableBody>
                             {affiliatesForTabs.length === 0 ? (
                               <TableRow>
                                 <TableCell colSpan={5} className="text-center h-24">No partners match the selected filter.</TableCell>
                               </TableRow>
                             ) : (
                               affiliatesForTabs.map((affiliate: any) => (
                                 <TableRow key={affiliate.id}>
                                   <TableCell className="font-medium">{affiliate.name}</TableCell>
                                   <TableCell>{affiliate.address.city}, {affiliate.address.state}</TableCell>
                                   <TableCell>{Math.floor(Math.random() * 50) + 5} Vehicles</TableCell> {/* Mock Fleet Size */}
                                   <TableCell>
                                      <Badge className={`${getStatusBadgeClass(affiliate.status)}`}>
                                        {affiliate.status.charAt(0).toUpperCase() + affiliate.status.slice(1)}
                                      </Badge>
                                    </TableCell>
                                   <TableCell className="text-right">
                                     <Button
                                       variant="outline"
                                       size="sm"
                                       onClick={() => handleOpenRatesDetails(affiliate)}
                                     >
                                       View Details
                                     </Button>
                                   </TableCell>
                                 </TableRow>
                               ))
                             )}
                           </TableBody>
                         </Table>
                       </div>
                     </CardContent>
                   </Card>
                   {/* Keep placeholders for future sections if needed */}
                   {/* <Card><CardHeader><CardTitle>Seasonal Pricing</CardTitle></CardHeader><CardContent>Coming soon.</CardContent></Card> */}
                   {/* <Card><CardHeader><CardTitle>Fleet Management</CardTitle></CardHeader><CardContent>Coming soon.</CardContent></Card> */}
                </div>
              </TabsContent>

              <TabsContent value="applications">
                {/* Move existing applications list and review UI here */}
                <div className="grid gap-4">
                  {filteredApplications.length === 0 ? (
                    <div className="text-center text-muted-foreground py-8">No applications found</div>
                  ) : (
                    filteredApplications.map(app => (
                      <Card key={app.id} className="p-4 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-4">
                            <div>
                              <h3 className="font-medium">{app.name}</h3>
                              <p className="text-sm text-muted-foreground">{app.city}, {app.state}</p>
                              <p className="text-xs text-muted-foreground">Submitted: {app.submitted}</p>
                              <p className="text-xs text-muted-foreground">Email: {app.email}</p>
                            </div>
                            <div className="flex gap-2">
                              <Badge variant="outline">{app.status.charAt(0).toUpperCase() + app.status.slice(1)}</Badge>
                            </div>
                          </div>
                          <div className="mt-2">
                            <Progress value={app.progress} className="h-2" />
                            <span className="text-xs text-muted-foreground ml-2">{app.progress}% complete</span>
                          </div>
                        </div>
                        <div className="flex flex-col gap-2 min-w-[120px]">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            onClick={() => handleOpenApplicationDetails(app)}
                          >
                            View Details
                          </Button>
                          {app.status === "pending" && (
                            <>
                              <Button
                                variant="default"
                                size="sm"
                                className="w-full"
                                onClick={() => handleApproveApplication(app.id)}
                              >
                                <Check className="mr-1 h-4 w-4" /> Approve
                              </Button>
                              <Button
                                variant="destructive"
                                size="sm"
                                className="w-full"
                                onClick={() => handleRejectApplication(app.id, app.name)}
                              >
                                <X className="mr-1 h-4 w-4" /> Reject
                              </Button>
                            </>
                          )}
                          {app.status === "in_verification" && (
                            <Button variant="outline" size="sm" className="w-full">
                              Review
                            </Button>
                          )}
                          {app.status === "approved" && (
                            <Button variant="secondary" size="sm" className="w-full">
                              Edit
                            </Button>
                          )}
                        </div>
                      </Card>
                    ))
                  )}
                </div>
              </TabsContent>

              <TabsContent value="compliance">
                 <div className="space-y-6">
                    {/* Partner Filter for this tab */}
                    <div className="flex flex-wrap gap-2 mb-4 p-4 border rounded-md bg-muted/50">
                       <Select value={selectedPartnerIdForTab} onValueChange={setSelectedPartnerIdForTab}>
                         <SelectTrigger className="w-[250px]">
                           <SelectValue placeholder="Filter by Partner" />
                         </SelectTrigger>
                         <SelectContent>
                           <SelectItem value="all">All Partners</SelectItem>
                           {affiliates.map((aff: any) => ( // Use unfiltered affiliates for the dropdown options
                             <SelectItem key={aff.id} value={aff.id}>{aff.name} ({aff.address.city}, {aff.address.state})</SelectItem>
                           ))}
                         </SelectContent>
                       </Select>
                       {/* Placeholder for other filters */}
                       <Input placeholder="Filter by City..." className="w-[150px]" disabled/>
                       <Select disabled>
                         <SelectTrigger className="w-[180px]">
                           <SelectValue placeholder="Filter by Status" />
                         </SelectTrigger>
                         <SelectContent>
                           <SelectItem value="compliant">Compliant</SelectItem>
                         </SelectContent>
                       </Select>
                       <Button variant="outline" disabled>Apply Filters</Button>
                     </div>

                    {/* New Compliance Dashboard */}
                    <div className="grid gap-4 md:grid-cols-3">
                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                          <CardTitle className="text-sm font-medium">Compliance Issues</CardTitle>
                          <ShieldAlert className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">{complianceSummary.issues}</div>
                          <p className="text-xs text-muted-foreground">Partners with active compliance issues</p>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                          <CardTitle className="text-sm font-medium">Expired Documents</CardTitle>
                          <FileWarning className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">{complianceSummary.expired}</div>
                          <p className="text-xs text-muted-foreground">Partners with one or more expired documents</p>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                          <CardTitle className="text-sm font-medium">Pending Verifications</CardTitle>
                          <Hourglass className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">{complianceSummary.pending}</div>
                          <p className="text-xs text-muted-foreground">Partners awaiting document verification</p>
                        </CardContent>
                      </Card>
                    </div>

                    {/* New Compliance Table View */}
                    <Card>
                      <CardHeader>
                        <CardTitle>Compliance Status Overview</CardTitle>
                        <CardDescription>Compliance status for selected partner(s).</CardDescription>
                      </CardHeader>
                      <CardContent>
                         <div className="rounded-md border">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Partner</TableHead>
                                <TableHead>Location</TableHead>
                                <TableHead>Overall Status</TableHead>
                                <TableHead>Insurance</TableHead>
                                <TableHead>Biz License</TableHead>
                                <TableHead>Last Audit</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {affiliatesForTabs.length === 0 ? (
                                <TableRow>
                                  <TableCell colSpan={7} className="text-center h-24">No partners match the selected filter.</TableCell>
                                </TableRow>
                              ) : (
                                affiliatesForTabs.map((affiliate: any) => {
                                  const complianceEntry = compliance.find(c => c.partnerId === affiliate.id);
                                  return (
                                    <TableRow key={affiliate.id}>
                                      <TableCell className="font-medium">{affiliate.name}</TableCell>
                                      <TableCell>{affiliate.address.city}, {affiliate.address.state}</TableCell>
                                      <TableCell>
                                        <Badge variant={complianceEntry?.overallStatus === 'Verified' ? 'default' : complianceEntry?.overallStatus === 'Pending' ? 'outline' : 'destructive'}>
                                          {complianceEntry?.overallStatus || 'N/A'}
                                        </Badge>
                                      </TableCell>
                                      <TableCell>
                                        <span className={`text-xs ${complianceEntry?.insuranceStatus === 'Expired' ? 'text-red-600' : 'text-muted-foreground'}`}>
                                          {complianceEntry?.insuranceStatus || 'N/A'} {complianceEntry?.insuranceStatus !== 'Missing' && complianceEntry?.insuranceExpiry ? `(Exp: ${formatDate(complianceEntry.insuranceExpiry)})` : ''}
                                        </span>
                                      </TableCell>
                                      <TableCell>
                                         <span className={`text-xs ${complianceEntry?.licenseStatus === 'Expired' ? 'text-red-600' : 'text-muted-foreground'}`}>
                                           {complianceEntry?.licenseStatus || 'N/A'} {complianceEntry?.licenseStatus !== 'Missing' && complianceEntry?.licenseExpiry ? `(Exp: ${formatDate(complianceEntry.licenseExpiry)})` : ''}
                                         </span>
                                      </TableCell>
                                      <TableCell>{complianceEntry?.lastAudit ? formatDate(complianceEntry.lastAudit) : 'N/A'}</TableCell>
                                      <TableCell className="text-right">
                                         <Button
                                           variant="outline"
                                           size="sm"
                                           onClick={() => handleOpenComplianceDetails(affiliate)}
                                         >
                                           View Details
                                         </Button>
                                      </TableCell>
                                    </TableRow>
                                  );
                                })
                              )}
                            </TableBody>
                          </Table>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Keep Compliance Settings Section */}
                    <Card>
                      <CardHeader>
                        <CardTitle>Compliance Settings</CardTitle>
                        <CardDescription>Configure compliance requirements and thresholds</CardDescription>
                      </CardHeader>
                      <CardContent>
                        {/* ... Existing settings switches ... */}
                      </CardContent>
                    </Card>
                  </div>
              </TabsContent>
            </Tabs>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-4 grid-cols-1 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Affiliate Distribution</CardTitle>
            <CardDescription>By tenant usage</CardDescription>
          </CardHeader>
          <CardContent className="h-[200px] flex items-center justify-center bg-muted/20 rounded-md">
            <div className="text-center">
              <div className="text-sm text-muted-foreground">Distribution chart placeholder</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Service Coverage</CardTitle>
            <CardDescription>By location</CardDescription>
          </CardHeader>
          <CardContent className="h-[200px] flex items-center justify-center bg-muted/20 rounded-md">
            <div className="text-center">
              <div className="text-sm text-muted-foreground">Coverage map placeholder</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Performance</CardTitle>
            <CardDescription>Top rated affiliates</CardDescription>
          </CardHeader>
          <CardContent className="h-[200px] flex items-center justify-center bg-muted/20 rounded-md">
            <div className="text-center">
              <div className="text-sm text-muted-foreground">Performance chart placeholder</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Add Affiliate Modal (stub) */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 shadow-lg w-full max-w-md">
            <h3 className="text-lg font-bold mb-4">Add Affiliate (Stub)</h3>
            <p className="mb-4 text-muted-foreground">TODO: Implement add affiliate form and backend integration.</p>
            <Button onClick={() => setShowAddModal(false)} className="w-full">Close</Button>
          </div>
        </div>
      )}

      {/* Render Drawers */}
      <RatesFleetDetailsDrawer
        open={showRatesDrawer}
        onOpenChange={setShowRatesDrawer}
        affiliate={selectedAffiliateForDrawer}
      />
      <ComplianceDetailsDrawer
        open={showComplianceDrawer}
        onOpenChange={setShowComplianceDrawer}
        affiliate={selectedAffiliateForDrawer}
        complianceData={selectedAffiliateForDrawer ? (compliance.find(c => c.partnerId === selectedAffiliateForDrawer!.id) ?? null) : null}
      />
      <GeneralDetailsDrawer
        open={showGeneralDrawer}
        onOpenChange={setShowGeneralDrawer}
        affiliate={selectedAffiliateForDrawer}
      />
      <ApplicationDetailsDrawer
        open={showApplicationDrawer}
        onOpenChange={setShowApplicationDrawer}
        application={selectedApplicationForDrawer}
      />
      <RejectionDialog
        open={showRejectionDialog}
        onOpenChange={setShowRejectionDialog}
        onReject={handleRejectionSubmit}
        affiliateName={selectedApplicationForRejection?.name || ''}
      />

    </div>
  )
}