import { NextResponse } from 'next/server'
import { Database } from '@/lib/types/supabase'
import { createAuthenticatedSupabaseClient, requireAuth } from '@/lib/auth/server'

// Define types for our data
interface Quote {
  id: string;
  customer_id: string;
  pickup_latitude?: number;
  pickup_longitude?: number;
  dropoff_latitude?: number;
  dropoff_longitude?: number;
  intermediate_stops?: Array<{
    location: string;
    order_number: number;
    latitude?: number;
    longitude?: number;
  }>;
  [key: string]: any;
}

interface Customer {
  id: string;
  email?: string;
  full_name?: string;
  [key: string]: any;
}

// Request deduplication cache
interface RequestCache {
  data: any;
  timestamp: number;
  status: number;
}

// Cache for deduplicating requests (in-memory, will reset on server restart)
const requestCache = new Map<string, RequestCache>();
const CACHE_TTL = 30000; // 30 seconds cache TTL (increased from 5 seconds)

// Generate a cache key from the request
function getCacheKey(req: Request, userId: string): string {
  const url = new URL(req.url);
  return `${userId}:${url.pathname}${url.search}`;
}

export async function GET(req: Request) {
  try {
    // Generate a request ID for tracking
    const requestId = Math.random().toString(36).substring(2, 10);
    console.log(`GET /api/quotes [${requestId}] - Starting request`);

    console.log(`GET /api/quotes [${requestId}] - Using centralized auth`)

    // Authenticate user
    const user = await requireAuth()
    console.log(`GET /api/quotes [${requestId}] - User authenticated:`, user.id)

    const supabase = await createAuthenticatedSupabaseClient()

    // User is already authenticated via requireAuth()
    console.log('GET /api/quotes - User email:', user.email);

    // Check cache for this request
    const cacheKey = getCacheKey(req, user.id);
    const now = Date.now();
    const cachedResponse = requestCache.get(cacheKey);

    if (cachedResponse && (now - cachedResponse.timestamp < CACHE_TTL)) {
      console.log(`GET /api/quotes [${requestId}] - Returning cached response for`, cacheKey);
      return NextResponse.json(cachedResponse.data, { status: cachedResponse.status });
    }

    // Get query parameters
    const url = new URL(req.url)
    const status = url.searchParams.get('status')
    const limit = parseInt(url.searchParams.get('limit') || '50')
    const id = url.searchParams.get('id')

    // Get user's roles from profiles table
    console.log('GET /api/quotes - Fetching user profile for roles');
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', user.id)
      .single()

    if (profileError) {
      console.error('GET /api/quotes - Error getting user profile:', profileError);
      // Continue with no roles rather than failing
    }

    const isAdmin = profile?.roles?.includes('ADMIN')
    console.log('GET /api/quotes - User is admin:', isAdmin);
    console.log('GET /api/quotes - User roles:', profile?.roles || []);

    if (!isAdmin) {
      console.log('GET /api/quotes - User is not an admin, checking if they can access quotes');
    }

    // If a specific quote ID is requested
    if (id) {
      console.log('GET /api/quotes - Fetching specific quote:', id);

      let query = supabase
        .from('quotes')
        .select('*')
        .eq('id', id)

      // Only check ownership for non-admin users
      if (!isAdmin) {
        query = query.eq('customer_id', user.id)
      }

      const { data: quote, error: quoteError } = await query.single()

      if (quoteError) {
        console.error('GET /api/quotes - Error fetching quote:', quoteError);
        return NextResponse.json({ error: quoteError.message, details: quoteError }, { status: 404 })
      }

      // Ensure all required fields are present
      const processedQuote = ensureQuoteFields(quote);
      console.log('GET /api/quotes - Successfully fetched quote:', id);

      // Cache the response
      requestCache.set(cacheKey, {
        data: processedQuote,
        timestamp: now,
        status: 200
      });

      return NextResponse.json(processedQuote, { status: 200 })
    }

    // Build the query - don't try to join with customer_id since the relationship isn't defined
    console.log('GET /api/quotes - Building query for quotes list');
    let query = supabase
      .from('quotes')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit)

    // Add filters based on user role
    if (!isAdmin) {
      // Non-admin users can only see their own quotes
      console.log('GET /api/quotes - Filtering quotes for non-admin user');
      query = query.eq('customer_id', user.id)
    } else {
      console.log('GET /api/quotes - User is admin, fetching all quotes');
    }

    // Add status filter if provided
    if (status && status !== 'all') {
      console.log('GET /api/quotes - Filtering by status:', status);
      query = query.eq('status', status)
    }

    // Execute the query
    console.log('GET /api/quotes - Executing query');
    const { data: quotes, error } = await query

    if (error) {
      console.error('GET /api/quotes - Error fetching quotes:', error)
      return NextResponse.json({ error: error.message, details: error }, { status: 500 })
    }

    console.log('GET /api/quotes - Found', quotes?.length || 0, 'quotes');

    // Process quotes to ensure all required fields are present
    const processedQuotes = quotes ? quotes.map(ensureQuoteFields) : [];

    // If we need customer data, we can fetch it separately
    if (processedQuotes && processedQuotes.length > 0) {
      // Get unique customer IDs
      const customerIds = Array.from(new Set(processedQuotes.map((q: Quote) => q.customer_id)))

      console.log('GET /api/quotes - Fetching customer profiles for', customerIds.length, 'customers');
      // Fetch customer profiles
      const { data: customers, error: customersError } = await supabase
        .from('profiles')
        .select('id, email, full_name')
        .in('id', customerIds)

      if (customersError) {
        console.error('GET /api/quotes - Error fetching customer profiles:', customersError);
      }

      if (!customersError && customers) {
        // Create a map of customer data
        const customerMap: Record<string, Customer> = {}
        customers.forEach((customer: Customer) => {
          customerMap[customer.id] = customer
        })

        // Add customer data to quotes
        processedQuotes.forEach((quote: Quote) => {
          quote.customer = customerMap[quote.customer_id] || null
        })
      }
    }

    console.log('GET /api/quotes - Successfully returning', processedQuotes.length, 'quotes');

    // Cache the response
    requestCache.set(cacheKey, {
      data: processedQuotes,
      timestamp: now,
      status: 200
    });

    return NextResponse.json(processedQuotes, { status: 200 })
  } catch (error: any) {
    console.error('GET /api/quotes - Unhandled error:', error)
    return NextResponse.json(
      { error: error.message, stack: error.stack },
      { status: 500 }
    )
  }
}

// Helper function to ensure all required fields are present in a quote
function ensureQuoteFields(quote: any): Quote {
  if (!quote) return quote;

  // Log the original quote for debugging
  console.log('Processing quote:', quote.id);

  // Ensure all required fields have at least default values
  const processedQuote = {
    ...quote,
    id: quote.id || `generated-${Date.now()}`,
    customer_id: quote.customer_id || 'unknown',
    reference_number: quote.reference_number || `Q${Date.now().toString().slice(-6)}`,
    service_type: quote.service_type || 'standard',
    vehicle_type: quote.vehicle_type || 'sedan',
    pickup_location: quote.pickup_location || 'Unknown',
    pickup_latitude: quote.pickup_latitude || null,
    pickup_longitude: quote.pickup_longitude || null,
    dropoff_location: quote.dropoff_location || 'Unknown',
    dropoff_latitude: quote.dropoff_latitude || null,
    dropoff_longitude: quote.dropoff_longitude || null,
    pickup_date: quote.pickup_date || new Date().toISOString().split('T')[0],
    pickup_time: quote.pickup_time || '12:00',
    dropoff_date: quote.dropoff_date || null,
    dropoff_time: quote.dropoff_time || null,
    passenger_count: quote.passenger_count || 1,
    luggage_count: quote.luggage_count || 0,
    special_requests: quote.special_requests || null,
    status: quote.status || 'pending',
    priority: quote.priority || 'medium',
    notes: quote.notes || null,
    total_amount: quote.total_amount || null,
    base_fare: quote.base_fare || null,
    gratuity: quote.gratuity || null,
    intermediate_stops: quote.intermediate_stops?.map((stop: any) => ({
      location: stop.location || 'Unknown',
      order_number: stop.order_number || 0,
      latitude: stop.latitude || null,
      longitude: stop.longitude || null
    })) || []
  };

  return processedQuote;
}

export async function POST(req: Request) {
  try {
    console.log('POST /api/quotes - Starting quote creation request')
    console.log('POST /api/quotes - Using centralized auth')

    // Authenticate user
    const user = await requireAuth()
    console.log('POST /api/quotes - User authenticated:', user.id)

    const supabase = await createAuthenticatedSupabaseClient()

    // Get the request body
    const body = await req.json()

    // Support both old and new field formats
    const pickup_location = body.pickup_location || body.pickupLocation
    const dropoff_location = body.dropoff_location || body.dropoffLocation
    const pickup_date = body.date || body.pickup_date || body.pickupDate
    const pickup_time = body.time || body.pickup_time || body.pickupTime
    const service_type = body.service_type || body.bookingType || 'point-to-point'
    const vehicle_type = body.vehicle_type || 'sedan'
    const city = body.city || (pickup_location ? pickup_location.split(',')[1]?.trim() || pickup_location.split(' ').pop() : 'Unknown')

    // Validate required fields (more flexible for booking form)
    if (!pickup_location || !pickup_date || !pickup_time) {
      console.error('POST /api/quotes - Missing required fields');
      return NextResponse.json({
        error: "Missing required fields",
        details: "pickup_location, pickup_date, and pickup_time are required"
      }, { status: 400 })
    }

    // Validate service type specific requirements
    if (service_type !== 'hourly' && !dropoff_location) {
      return NextResponse.json({
        error: "Drop-off location is required for non-hourly services"
      }, { status: 400 })
    }

    // Log city information
    console.log('POST /api/quotes - City information:', {
      raw_city: body.city,
      trimmed_city: body.city.trim(),
      city_present: !!body.city && body.city.trim() !== ''
    });

    // Create the quote
    const quoteData = {
      customer_id: user.id,
      reference_number: `TF${Date.now().toString().slice(-8)}`,
      service_type: service_type,
      vehicle_type: vehicle_type,
      pickup_location: pickup_location,
      pickup_latitude: body.pickup_latitude || null,
      pickup_longitude: body.pickup_longitude || null,
      dropoff_location: dropoff_location || null,
      dropoff_latitude: body.dropoff_latitude || null,
      dropoff_longitude: body.dropoff_longitude || null,
      city: city.trim(),
      date: pickup_date,
      time: pickup_time,
      pickup_date: pickup_date,
      pickup_time: pickup_time,
      passenger_count: body.passenger_count || (body.adults || 1) + (body.children || 0),
      adults: body.adults || 1,
      children: body.children || 0,
      luggage_count: body.luggage_count || 0,
      special_requests: body.special_requests || body.special_instructions ? [body.special_requests || body.special_instructions] : null,
      status: 'pending',
      priority: body.priority || 'medium',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      // Optional fields based on service type
      duration_hours: service_type === 'hourly' ? (body.duration_hours || body.duration || 4) : null,
      is_multi_day: body.is_multi_day || service_type === 'multi-day',
      flight_number: service_type === 'airport' ? (body.flight_number || body.flightNumber) : null,
      airline: service_type === 'airport' ? body.airline : null,
      is_return_trip: body.is_return_trip || false,
      return_date: body.return_date || null,
      return_time: body.return_time || null,
      return_flight_number: body.return_flight_number || null,
      end_date: service_type === 'multi-day' ? (body.end_date || body.endDate) : null,
      car_seats_needed: body.car_seats_needed || false,
      infant_seats: body.infant_seats || 0,
      toddler_seats: body.toddler_seats || 0,
      booster_seats: body.booster_seats || 0,
      intermediate_stops: body.intermediate_stops || null,
      duration: body.duration || null,
      distance: body.distance || null,
      total_amount: body.total_amount || null
    }

    // If intermediate stops are provided, create them in a transaction
    if (body.intermediate_stops && body.intermediate_stops.length > 0) {
      const { data: quote, error: quoteError } = await supabase
        .rpc('create_quote_with_stops', {
          quote_data: quoteData,
          stops_data: body.intermediate_stops.map((stop: any) => ({
            location: stop.location,
            order_number: stop.order_number,
            latitude: stop.latitude || null,
            longitude: stop.longitude || null
          }))
        })

      if (quoteError) {
        console.error('Error creating quote with stops:', quoteError)
        return NextResponse.json({ error: quoteError.message, details: quoteError }, { status: 500 })
      }

      return NextResponse.json(quote, { status: 201 })
    }

    // If no intermediate stops, just create the quote
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .insert([quoteData])
      .select()
      .single()

    if (quoteError) {
      console.error('Error creating quote:', quoteError)
      return NextResponse.json({ error: quoteError.message, details: quoteError }, { status: 500 })
    }

    console.log('POST /api/quotes - Quote created successfully:', {
      id: quote.id,
      reference_number: quote.reference_number,
      city: quote.city,
      city_truthy: !!quote.city,
      city_empty: quote.city === ''
    });

    // After creating the quote, find matching affiliates and determine processing approach
    try {
      if (body.pickup_latitude && body.pickup_longitude && body.city) {
        console.log('POST /api/quotes - Finding matching affiliates for quote:', quote.id);

        // Map service type for database function
        const mappedServiceType = body.service_type === 'point' ? 'point_to_point' : body.service_type
        const cleanCity = body.city.split(',')[0].trim()

        console.log('POST /api/quotes - Service type mapping:', body.service_type, '->', mappedServiceType)
        console.log('POST /api/quotes - City cleaning:', body.city, '->', cleanCity)

        // Find matching affiliates using the enhanced matching logic
        const { data: matchingAffiliates, error: matchingError } = await supabase
          .rpc('find_matching_affiliates_for_quote', {
            p_pickup_lat: body.pickup_latitude,
            p_pickup_lng: body.pickup_longitude,
            p_pickup_city: cleanCity,
            p_vehicle_type: body.vehicle_type,
            p_pickup_date: body.date,
            p_service_type: mappedServiceType
          });

        if (matchingError) {
          console.error('POST /api/quotes - Error finding matching affiliates:', matchingError);
        } else {
          console.log('POST /api/quotes - Found matching affiliates:', {
            count: matchingAffiliates?.length || 0,
            affiliates: matchingAffiliates?.map((a: any) => ({ id: a.affiliate_id, name: a.company_name, score: a.matching_score }))
          });
        }

        // Get processing approach recommendation
        const { data: approachData, error: approachError } = await supabase
          .rpc('recommend_quote_processing_approach', {
            p_pickup_lat: body.pickup_latitude,
            p_pickup_lng: body.pickup_longitude,
            p_pickup_city: cleanCity,
            p_vehicle_type: body.vehicle_type,
            p_pickup_date: body.date
          });

        if (approachError) {
          console.error('POST /api/quotes - Error getting processing approach:', approachError);
        } else if (approachData && approachData.length > 0) {
          const approach = approachData[0];
          console.log('POST /api/quotes - Processing approach recommendation:', {
            approach: approach.recommended_approach,
            reason: approach.reason,
            confidence: approach.confidence_score,
            matching_count: approach.matching_affiliates_count
          });

          // Update quote with additional metadata
          const { error: updateError } = await supabase
            .from('quotes')
            .update({
              processing_approach: approach.recommended_approach,
              matching_affiliates_count: approach.matching_affiliates_count,
              updated_at: new Date().toISOString()
            })
            .eq('id', quote.id);

          if (updateError) {
            console.error('POST /api/quotes - Error updating quote with approach data:', updateError);
          }
        }

        // If we have matching affiliates and this is a competitive bidding scenario,
        // we could automatically send quote offers here in the future
        if (matchingAffiliates && matchingAffiliates.length > 0) {
          // For now, just log that affiliates are available
          console.log('POST /api/quotes - Quote is ready for affiliate processing');
        }
      } else {
        console.log('POST /api/quotes - Missing coordinate data, skipping affiliate matching');
      }
    } catch (matchingError) {
      console.error('POST /api/quotes - Error in affiliate matching workflow:', matchingError);
      // Don't fail the quote creation if affiliate matching fails
    }

    return NextResponse.json(quote, { status: 201 })
  } catch (error: any) {
    console.error('Unhandled error in POST /api/quotes:', error)
    return NextResponse.json(
      { error: error.message, stack: error.stack },
      { status: 500 }
    )
  }
}

export async function PATCH(request: Request) {
  try {
    // Authenticate user
    const user = await requireAuth()
    const supabase = await createAuthenticatedSupabaseClient()

    // User is already authenticated via requireAuth()

    // Get request body and URL
    const body = await request.json()
    const url = new URL(request.url)
    const id = url.searchParams.get('id')

    if (!id) {
      return NextResponse.json({ error: "Missing quote ID" }, { status: 400 })
    }

    // Get user's roles from profiles table
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', user.id)
      .single()

    if (profileError) {
      console.error('Error getting user profile:', profileError)
      // Continue with no roles rather than failing
    }

    const isAdmin = profile?.roles?.includes('ADMIN')

    // Check if the user has permission to update this quote
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select('customer_id')
      .eq('id', id)
      .single()

    if (quoteError) {
      return NextResponse.json({ error: "Quote not found", details: quoteError }, { status: 404 })
    }

    if (!isAdmin && quote.customer_id !== user.id) {
      return NextResponse.json({ error: "Unauthorized to update this quote" }, { status: 403 })
    }

    // Extract intermediate stops if present
    const { intermediate_stops, ...quoteData } = body

    // Start a transaction if we have intermediate stops
    if (intermediate_stops) {
      const { data: result, error: updateError } = await supabase
        .rpc('update_quote_with_stops', {
          quote_id: id,
          quote_data: quoteData,
          stops_data: intermediate_stops.map((stop: any) => ({
            location: stop.location,
            order_number: stop.order_number,
            latitude: stop.latitude || null,
            longitude: stop.longitude || null
          }))
        })

      if (updateError) {
        console.error('Error updating quote with stops:', updateError)
        return NextResponse.json({ error: updateError.message, details: updateError }, { status: 500 })
      }

      return NextResponse.json(result, { status: 200 })
    }

    // If no intermediate stops, just update the quote
    const { data: updatedQuote, error: updateError } = await supabase
      .from('quotes')
      .update({
        ...quoteData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating quote:', updateError)
      return NextResponse.json({ error: updateError.message, details: updateError }, { status: 500 })
    }

    return NextResponse.json(updatedQuote, { status: 200 })
  } catch (error: any) {
    console.error('Unhandled error in PATCH /api/quotes:', error)
    return NextResponse.json(
      { error: error.message, stack: error.stack },
      { status: 500 }
    )
  }
}

export async function DELETE(request: Request) {
  try {
    // Authenticate user
    const user = await requireAuth()
    const supabase = await createAuthenticatedSupabaseClient()

    // User is already authenticated via requireAuth()

    // Get user's roles from profiles table
    const { data: profile } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', user.id)
      .single()

    const isAdmin = profile?.roles?.includes('ADMIN')

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({ error: 'Missing quote ID' }, { status: 400 })
    }

    // Build the query
    let query = supabase
      .from('quotes')
      .update({
        status: 'cancelled',
        updated_at: new Date().toISOString()
      })
      .eq('id', id)

    // Only check ownership for non-admin users
    if (!isAdmin) {
      query = query.eq('customer_id', user.id)
    }

    const { data: quote, error } = await query.select().single()

    if (error) {
      console.error('Error cancelling quote:', error)
      return NextResponse.json({ error: 'Failed to cancel quote' }, { status: 500 })
    }

    return NextResponse.json(quote)
  } catch (error) {
    console.error('Error in quotes route:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}