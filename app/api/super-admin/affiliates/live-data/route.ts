import { NextResponse } from 'next/server'
import { createAuthenticatedSupabaseClient, requireRole } from '@/lib/auth/server'
import { createClient } from '@supabase/supabase-js'

export async function GET() {
  try {
    // Require super admin access
    await requireRole(['SUPER_ADMIN', 'ADMIN'])
    const supabase = await createAuthenticatedSupabaseClient()

    // Create service role client for admin operations (bypasses RLS)
    const serviceSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get all affiliate offers with quote and affiliate info (using service role to bypass RLS)
    const { data: affiliateOffers, error: offersError } = await serviceSupabase
      .from('quote_affiliate_offers')
      .select(`
        id,
        quote_id,
        company_id,
        rate_amount,
        status,
        created_at,
        updated_at,
        expires_at
      `)
      .order('created_at', { ascending: false })
      .limit(1000)

    if (offersError) {
      console.error('Error fetching affiliate offers:', offersError)
    }



    // Get active trips with basic info (fetch affiliate info separately due to missing FK)
    const { data: activeTrips, error: tripsError } = await supabase
      .from('trips')
      .select(`
        id,
        quote_id,
        status,
        pickup_location,
        dropoff_location,
        scheduled_pickup_time,
        actual_pickup_time,
        driver_id,
        company_id,
        quotes (
          reference_number,
          customer_id,
          city
        ),
        drivers (
          id,
          name,
          phone
        )
      `)
      .in('status', ['confirmed', 'en_route', 'arrived', 'in_progress'])
      .order('scheduled_pickup_time', { ascending: true })
      .limit(100)

    if (tripsError) {
      console.error('Error fetching active trips:', tripsError)
    }

    // Fetch affiliate company info for trips separately
    let tripsWithAffiliates = activeTrips || []
    if (activeTrips && activeTrips.length > 0) {
      const companyIds = Array.from(new Set(activeTrips.map(trip => trip.company_id).filter(Boolean)))

      if (companyIds.length > 0) {
        const { data: tripAffiliates, error: tripAffiliatesError } = await supabase
          .from('affiliate_companies')
          .select('id, name, city, state, rating')
          .in('id', companyIds)

        if (!tripAffiliatesError && tripAffiliates) {
          // Create a map for quick lookup
          const affiliateMap = new Map(tripAffiliates.map(aff => [aff.id, aff]))

          // Merge affiliate info into trips
          tripsWithAffiliates = activeTrips.map(trip => ({
            ...trip,
            affiliate_companies: trip.company_id ? affiliateMap.get(trip.company_id) : null
          }))
        }
      }
    }

    // Get affiliate companies
    const { data: affiliates, error: affiliatesError } = await supabase
      .from('affiliate_companies')
      .select(`
        id,
        name,
        city,
        state,
        status,
        rating,
        created_at,
        contact_email,
        contact_phone
      `)
      .order('name')

    if (affiliatesError) {
      console.error('Error fetching affiliates:', affiliatesError)
    }

    // Aggregate affiliate-centric stats
    const affiliateStats: Record<string, any> = {};
    (affiliates || []).forEach((affiliate) => {
      affiliateStats[affiliate.id] = {
        affiliate_id: affiliate.id,
        name: affiliate.name,
        city: affiliate.city,
        state: affiliate.state,
        rating: affiliate.rating,
        total_offers: 0,
        pending_offers: 0,
        accepted_offers: 0,
        rejected_offers: 0,
        counter_offered_offers: 0,
        expired_offers: 0,
        oldest_pending_quote: null,
        avg_response_time_hours: null,
        acceptance_rate: null,
        last_activity: null,
        cities_served: new Set<string>(),
        most_active_customer: null,
        most_active_customer_count: 0,
        active_trips: 0, // Initialize active trips
      };
    });

    // Process offers for stats
    const offerTimes: Record<string, number[]> = {};
    const customerCounts: Record<string, Record<string, number>> = {};

    (affiliateOffers || []).forEach((offer) => {
      const affId = offer.company_id;

      // If affiliate not in stats yet, skip for now (will be handled by separate affiliate fetch)
      if (!affiliateStats[affId]) {
        return; // Skip if affiliate not in main list
      }

      affiliateStats[affId].total_offers += 1;

      // Count offers by status (handle all possible statuses)
      const status = offer.status;
      if (status === 'PENDING') {
        affiliateStats[affId].pending_offers += 1;
      } else if (status === 'ACCEPTED') {
        affiliateStats[affId].accepted_offers += 1;
      } else if (status === 'REJECTED') {
        affiliateStats[affId].rejected_offers += 1;
      } else if (status === 'COUNTER_OFFERED') {
        affiliateStats[affId].counter_offered_offers += 1;
      } else if (status === 'EXPIRED') {
        affiliateStats[affId].expired_offers += 1;
      }

      // Track cities served (commented out - property doesn't exist)
      // if (offer.quotes?.city) {
      //   affiliateStats[affId].cities_served.add(offer.quotes.city);
      // }

      // Track oldest pending quote
      if (offer.status === 'PENDING') {
        if (
          !affiliateStats[affId].oldest_pending_quote ||
          new Date(offer.created_at) < new Date(affiliateStats[affId].oldest_pending_quote.created_at)
        ) {
          affiliateStats[affId].oldest_pending_quote = {
            quote_id: offer.quote_id,
            created_at: offer.created_at,
            reference_number: 'TBD', // Will fetch separately
            customer_id: null,
            city: 'TBD',
          };
        }
      }

      // Track response times for accepted/rejected
      if (['ACCEPTED', 'REJECTED'].includes(offer.status)) {
        if (offer.created_at && offer.updated_at) {
          const created = new Date(offer.created_at).getTime();
          const updated = new Date(offer.updated_at).getTime();
          const hours = (updated - created) / (1000 * 60 * 60);
          if (!offerTimes[affId]) offerTimes[affId] = [];
          offerTimes[affId].push(hours);
        }
      }

      // Track most active customer (temporarily disabled)
      // if ((offer.quotes as any)?.customer_id) {
      //   if (!customerCounts[affId]) customerCounts[affId] = {};
      //   customerCounts[affId][(offer.quotes as any).customer_id] = (customerCounts[affId][(offer.quotes as any).customer_id] || 0) + 1;
      // }

      // Track last activity (most recent offer activity)
      if (offer.updated_at) {
        const activityDate = new Date(offer.updated_at);
        if (!affiliateStats[affId].last_activity || activityDate > new Date(affiliateStats[affId].last_activity)) {
          affiliateStats[affId].last_activity = offer.updated_at;
        }
      }
    });



    // Process active trips for stats
    (tripsWithAffiliates || []).forEach((trip: any) => {
      const affId = trip.company_id;
      if (affId && affiliateStats[affId]) {
        affiliateStats[affId].active_trips += 1;
      }
    });

    // Finalize stats
    Object.values(affiliateStats).forEach((stats: any) => {
      // Acceptance rate (only count accepted vs total responded, not pending)
      const totalResponded = stats.accepted_offers + stats.rejected_offers + stats.counter_offered_offers;
      stats.acceptance_rate = totalResponded > 0 ? (stats.accepted_offers / totalResponded) * 100 : null;

      // Avg response time
      if (offerTimes[stats.affiliate_id] && offerTimes[stats.affiliate_id].length > 0) {
        stats.avg_response_time_hours =
          offerTimes[stats.affiliate_id].reduce((a, b) => a + b, 0) / offerTimes[stats.affiliate_id].length;
      }

      // Most active customer
      if (customerCounts[stats.affiliate_id]) {
        const sorted = Object.entries(customerCounts[stats.affiliate_id]).sort((a, b) => b[1] - a[1]);
        if (sorted.length > 0) {
          stats.most_active_customer = sorted[0][0];
          stats.most_active_customer_count = sorted[0][1];
        }
      }

      // Cities served (convert Set to Array)
      stats.cities_served = Array.from(stats.cities_served);

      // Format last activity for display
      if (stats.last_activity) {
        const lastActivityDate = new Date(stats.last_activity);
        const now = new Date();
        const diffHours = (now.getTime() - lastActivityDate.getTime()) / (1000 * 60 * 60);

        if (diffHours < 1) {
          stats.last_activity_display = 'Less than 1 hour ago';
        } else if (diffHours < 24) {
          stats.last_activity_display = `${Math.floor(diffHours)} hours ago`;
        } else {
          const diffDays = Math.floor(diffHours / 24);
          stats.last_activity_display = `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
        }
      } else {
        stats.last_activity_display = 'No recent activity';
      }
    });

    // (Removed old quoteStats aggregation, replaced by above)

    // Get operational alerts
    const alerts: any[] = [];

    // Expiring offers (less than 2 hours left)
    (affiliateOffers || []).forEach((offer) => {
      if ((offer.status === 'PENDING' || offer.status === 'SENT') && offer.expires_at) {
        const hoursUntilExpiry = (new Date(offer.expires_at).getTime() - Date.now()) / (1000 * 60 * 60);
        if (hoursUntilExpiry < 2 && hoursUntilExpiry > 0) {
          alerts.push({
            id: `offer_expiring_${offer.id}`,
            type: 'offer_expiring_soon',
            message: `Offer for quote ${offer.quote_id} expires soon`,
            affiliate_name: 'TBD',
            related_id: offer.quote_id,
            affiliate_id: offer.company_id,
          });
        }
      }
    });

    // Late trips (15+ min past scheduled pickup)
    (tripsWithAffiliates || []).forEach((trip: any) => {
      if (trip.status === 'confirmed' && trip.scheduled_pickup_time) {
        const scheduledTime = new Date(trip.scheduled_pickup_time);
        const now = new Date();
        if (now.getTime() - scheduledTime.getTime() > 15 * 60 * 1000) {
          alerts.push({
            id: `trip_late_${trip.id}`,
            type: 'late_trip',
            message: `Trip is running late - ${trip.pickup_location}`,
            affiliate_name: (trip.affiliate_companies as any)?.name,
            related_id: trip.id,
            affiliate_id: trip.company_id,
          });
        }
      }
    });

    // Driver contact missing (48h before trip)
    (tripsWithAffiliates || []).forEach((trip: any) => {
      if (['confirmed', 'scheduled'].includes(trip.status) && trip.scheduled_pickup_time) {
        const scheduledTime = new Date(trip.scheduled_pickup_time);
        const now = new Date();
        const hoursUntilTrip = (scheduledTime.getTime() - now.getTime()) / (1000 * 60 * 60);

        // Alert if trip is within 48 hours and no driver assigned or driver has no contact info
        if (hoursUntilTrip <= 48 && hoursUntilTrip > 0) {
          const hasDriverContact = trip.driver_id && trip.drivers?.phone;

          if (!hasDriverContact) {
            alerts.push({
              id: `driver_contact_missing_${trip.id}`,
              type: 'driver_contact_missing',
              message: `Driver contact missing for upcoming trip - ${trip.pickup_location}`,
              affiliate_name: (trip.affiliate_companies as any)?.name,
              related_id: trip.id,
              affiliate_id: trip.company_id,
            });
          }
        }
      }
    });



    return NextResponse.json({
      success: true,
      data: {
        affiliate_offers: affiliateOffers || [],
        active_trips: tripsWithAffiliates || [],
        affiliates: affiliates || [],
        affiliate_stats: affiliateStats,
        operational_alerts: alerts,
        summary: {
          total_pending_offers: (affiliateOffers || []).filter(o => o.status === 'PENDING').length,
          total_active_trips: (tripsWithAffiliates || []).length,
          total_active_affiliates: (affiliates || []).length,
          total_alerts: alerts.length,
        }
      }
    })

  } catch (error) {
    console.error('❌ ERROR in live data API:', error)
    console.error('❌ Error type:', typeof error)
    console.error('❌ Error details:', error instanceof Error ? error.message : String(error))

    if (error instanceof Error && error.message.includes('Forbidden')) {
      return NextResponse.json(
        { error: 'Access denied', details: 'Super admin access required' },
        { status: 403 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}