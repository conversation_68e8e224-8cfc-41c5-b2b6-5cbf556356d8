import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

/**
 * GET /api/super-admin/tenants
 * Get all available tenants for tenant switching
 */
export async function GET() {
  try {
    console.log('Super-admin tenants API - Starting request');

    // Create a service role client that bypasses authentication
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Fetch all tenants
    const { data: tenants, error } = await supabase
      .from('tenants')
      .select(`
        id,
        name,
        slug,
        domain,
        tenant_type,
        status,
        branding,
        settings,
        created_at,
        updated_at
      `)
      .eq('status', 'active')
      .order('name');

    if (error) {
      console.error('Error fetching tenants:', error);
      return NextResponse.json({ error: 'Failed to fetch tenants' }, { status: 500 });
    }

    console.log(`Super-admin tenants API - Found ${tenants?.length || 0} tenants`);

    return NextResponse.json({
      tenants: tenants || [],
      success: true
    });
  } catch (error) {
    console.error('Error in GET /api/super-admin/tenants:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/super-admin/tenants
 * Create a new tenant
 */
export async function POST(request: Request) {
  try {
    console.log('Super-admin tenants API - Creating new tenant');

    // Create a service role client that bypasses authentication
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    const body = await request.json();
    const { name, slug, domain, tenant_type, branding, settings } = body;

    // Validate required fields
    if (!name || !slug || !tenant_type) {
      return NextResponse.json(
        { error: 'Name, slug, and tenant_type are required' },
        { status: 400 }
      );
    }

    // Create the tenant
    const { data: tenant, error } = await supabase
      .from('tenants')
      .insert({
        name,
        slug,
        domain,
        tenant_type,
        branding: branding || {},
        settings: settings || {},
        status: 'active'
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating tenant:', error);
      return NextResponse.json({ error: 'Failed to create tenant' }, { status: 500 });
    }

    console.log('Super-admin tenants API - Tenant created successfully:', tenant.id);

    return NextResponse.json({
      tenant,
      success: true
    });
  } catch (error) {
    console.error('Error in POST /api/super-admin/tenants:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}
