import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

/**
 * TEST ENDPOINT: Test with real quote data that was failing
 * This tests the exact parameters from the failing quote
 */
export async function POST(request: NextRequest) {
  try {
    console.log('TEST: Testing with real quote data that was failing')

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Real quote data that was failing
    const realQuoteData = {
      city: "Austin, TX",
      service_type: "point",
      vehicle_type: "SUV",
      passenger_count: 1,
      date: "2025-06-27",
      time: "11:11:00"
    }

    console.log('Original quote data:', realQuoteData)

    // Apply the same fixes as in the API
    const mappedServiceType = realQuoteData.service_type === 'point' ? 'point_to_point' : realQuoteData.service_type
    const cleanCity = realQuoteData.city.split(',')[0].trim()

    console.log('Fixed data:', {
      originalCity: realQuoteData.city,
      cleanCity: cleanCity,
      originalServiceType: realQuoteData.service_type,
      mappedServiceType: mappedServiceType
    })

    // Step 1: Test affiliate query with cleaned city
    console.log('Step 1: Testing affiliate query with cleaned city...')
    
    const { data: affiliates, error: affiliatesError } = await supabase
      .from('affiliate_companies')
      .select('id, name, city, status, application_status')
      .eq('status', 'active')
      .eq('application_status', 'approved')
      .ilike('city', `%${cleanCity}%`)

    console.log('Affiliates result:', {
      success: !affiliatesError,
      error: affiliatesError?.message,
      count: affiliates?.length || 0,
      affiliates: affiliates?.map(a => ({ id: a.id, name: a.name, city: a.city }))
    })

    if (!affiliates || affiliates.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'No affiliates found even with cleaned city',
        debug: { cleanCity, originalCity: realQuoteData.city }
      })
    }

    // Step 2: Test vehicles query
    console.log('Step 2: Testing vehicles query...')
    
    const affiliateIds = affiliates.map(a => a.id)
    
    const { data: vehicles, error: vehiclesError } = await supabase
      .from('vehicles')
      .select('id, company_id, type, make, model, capacity, status')
      .in('company_id', affiliateIds)
      .eq('status', 'active')
      .eq('type', realQuoteData.vehicle_type)
      .gte('capacity', realQuoteData.passenger_count)

    console.log('Vehicles result:', {
      success: !vehiclesError,
      error: vehiclesError?.message,
      count: vehicles?.length || 0,
      vehicles: vehicles?.map(v => ({ 
        id: v.id, 
        company_id: v.company_id, 
        type: v.type, 
        capacity: v.capacity 
      }))
    })

    // Step 3: Test rate cards query with mapped service type
    console.log('Step 3: Testing rate cards query with mapped service type...')
    
    let rateCardQuery = supabase
      .from('rate_cards')
      .select(`
        id, company_id, vehicle_type, pricing_model_type,
        p2p_point_to_point_rate, dt_per_mile_rate, dt_per_hour_rate,
        airport_transfer_flat_rate, charter_hourly_rate, status, is_active
      `)
      .in('company_id', affiliateIds)
      .eq('is_active', true)
      .eq('vehicle_type', realQuoteData.vehicle_type)

    // Apply service type filtering
    if (mappedServiceType === 'point_to_point') {
      rateCardQuery = rateCardQuery.eq('pricing_model_type', 'P2P')
    } else if (mappedServiceType === 'airport') {
      rateCardQuery = rateCardQuery.eq('pricing_model_type', 'AIRPORT_TRANSFER')
    } else if (mappedServiceType === 'hourly') {
      rateCardQuery = rateCardQuery.eq('pricing_model_type', 'CHARTER')
    }

    const { data: rateCards, error: rateCardsError } = await rateCardQuery

    console.log('Rate cards result:', {
      success: !rateCardsError,
      error: rateCardsError?.message,
      count: rateCards?.length || 0,
      rateCards: rateCards?.map(rc => ({ 
        id: rc.id, 
        company_id: rc.company_id, 
        vehicle_type: rc.vehicle_type,
        pricing_model_type: rc.pricing_model_type,
        p2p_rate: rc.p2p_point_to_point_rate
      }))
    })

    // Step 4: Test database function with corrected parameters
    console.log('Step 4: Testing database function with corrected parameters...')
    
    const { data: dbFunctionResult, error: dbFunctionError } = await supabase
      .rpc('find_matching_affiliates_for_quote', {
        p_pickup_lat: 30.2672, // Austin coordinates
        p_pickup_lng: -97.7431,
        p_pickup_city: cleanCity,
        p_vehicle_type: realQuoteData.vehicle_type,
        p_pickup_date: realQuoteData.date,
        p_service_type: mappedServiceType
      })

    console.log('Database function result:', {
      success: !dbFunctionError,
      error: dbFunctionError?.message,
      count: dbFunctionResult?.length || 0,
      results: dbFunctionResult?.map((r: any) => ({
        affiliate_id: r.affiliate_id,
        company_name: r.company_name,
        matching_score: r.matching_score,
        has_rates: r.has_rates,
        has_active_vehicles: r.has_active_vehicles
      }))
    })

    // Step 5: Calculate final matches
    const validAffiliates = affiliates.filter(affiliate => {
      const affiliateVehicles = vehicles?.filter(v => v.company_id === affiliate.id) || []
      const affiliateRateCards = rateCards?.filter(rc => rc.company_id === affiliate.id) || []
      
      return affiliateVehicles.length > 0 && affiliateRateCards.length > 0
    })

    return NextResponse.json({
      success: true,
      message: 'Real quote matching test completed',
      original_quote: realQuoteData,
      fixes_applied: {
        city_mapping: `"${realQuoteData.city}" -> "${cleanCity}"`,
        service_type_mapping: `"${realQuoteData.service_type}" -> "${mappedServiceType}"`
      },
      results: {
        affiliates_found: affiliates?.length || 0,
        vehicles_found: vehicles?.length || 0,
        rate_cards_found: rateCards?.length || 0,
        database_function_results: dbFunctionResult?.length || 0,
        final_valid_affiliates: validAffiliates.length
      },
      detailed_results: {
        affiliates,
        vehicles,
        rateCards,
        dbFunctionResult,
        validAffiliates
      },
      conclusion: {
        should_work_now: validAffiliates.length > 0,
        issues_fixed: [
          'City format: "Austin, TX" -> "Austin"',
          'Service type: "point" -> "point_to_point"',
          'Rate card filtering by pricing model'
        ]
      }
    })

  } catch (error) {
    console.error('Error in real quote matching test:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}
