import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

/**
 * TEST ENDPOINT: Test the event manager affiliate matching API logic
 * This replicates the fixed logic from the event manager matching API
 */
export async function POST(request: NextRequest) {
  try {
    console.log('TEST: Testing event manager affiliate matching logic')

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Test parameters - using 2 passengers to match available vehicle capacity
    const testParams = {
      city: 'Austin',
      vehicleType: 'SUV',
      serviceType: 'airport',
      date: '2025-01-02',
      time: '10:00',
      passengers: 2
    }

    const { city, vehicleType, serviceType, passengers } = testParams

    console.log('Event Manager Matching API - Starting request')
    console.log('Search criteria:', testParams)

    // Query to find matching affiliates - we'll join vehicles and rate cards separately
    const { data: affiliates, error: affiliatesError } = await supabase
      .from('affiliate_companies')
      .select('id, name, city, status, application_status')
      .eq('status', 'active')
      .eq('application_status', 'approved')
      .ilike('city', `%${city}%`)

    if (affiliatesError) {
      console.error('Error fetching affiliates:', affiliatesError)
      return NextResponse.json({ error: 'Failed to fetch affiliates' }, { status: 500 })
    }

    console.log(`Found ${affiliates?.length || 0} potential affiliates`)

    if (!affiliates || affiliates.length === 0) {
      return NextResponse.json({
        success: true,
        affiliates: [],
        searchCriteria: { city, vehicleType, serviceType, date: testParams.date, time: testParams.time, passengers }
      })
    }

    // Now fetch vehicles and rate cards for these affiliates
    const affiliateIds = affiliates.map(a => a.id)
    
    // Get vehicles for these affiliates
    let vehicleQuery = supabase
      .from('vehicles')
      .select('id, company_id, type, make, model, capacity, status')
      .in('company_id', affiliateIds)
      .eq('status', 'active')

    if (vehicleType) {
      vehicleQuery = vehicleQuery.eq('type', vehicleType)
    }

    if (passengers) {
      vehicleQuery = vehicleQuery.gte('capacity', passengers)
    }

    const { data: vehicles, error: vehiclesError } = await vehicleQuery

    if (vehiclesError) {
      console.error('Error fetching vehicles:', vehiclesError)
      return NextResponse.json({ error: 'Failed to fetch vehicles' }, { status: 500 })
    }

    // Get rate cards for these affiliates
    let rateCardQuery = supabase
      .from('rate_cards')
      .select(`
        id, company_id, vehicle_type, pricing_model_type,
        p2p_point_to_point_rate, dt_per_mile_rate, dt_per_hour_rate,
        airport_transfer_flat_rate, charter_hourly_rate, status, is_active
      `)
      .in('company_id', affiliateIds)
      .eq('is_active', true)

    if (vehicleType) {
      rateCardQuery = rateCardQuery.eq('vehicle_type', vehicleType)
    }

    const { data: rateCards, error: rateCardsError } = await rateCardQuery

    if (rateCardsError) {
      console.error('Error fetching rate cards:', rateCardsError)
      return NextResponse.json({ error: 'Failed to fetch rate cards' }, { status: 500 })
    }

    console.log(`Found ${vehicles?.length || 0} vehicles and ${rateCards?.length || 0} rate cards`)

    // Filter affiliates that have both vehicles and rate cards
    const validAffiliates = affiliates.filter(affiliate => {
      const affiliateVehicles = vehicles?.filter(v => v.company_id === affiliate.id) || []
      const affiliateRateCards = rateCards?.filter(rc => rc.company_id === affiliate.id) || []
      
      // Must have at least one vehicle and one rate card
      return affiliateVehicles.length > 0 && affiliateRateCards.length > 0
    })

    console.log(`${validAffiliates.length} affiliates have both vehicles and rate cards`)

    // Transform and enhance affiliate data
    const enhancedAffiliates = validAffiliates.map((affiliate: any) => {
      const affiliateVehicles = vehicles?.filter(v => v.company_id === affiliate.id) || []
      const affiliateRateCards = rateCards?.filter(rc => rc.company_id === affiliate.id) || []
      
      const vehicle = affiliateVehicles[0] // Take first matching vehicle
      const rateCard = affiliateRateCards[0] // Take first matching rate card

      // Calculate estimated pricing based on service type and new rate card structure
      let estimatedPrice = 100 // Default fallback
      if (serviceType === 'airport') {
        estimatedPrice = rateCard.airport_transfer_flat_rate || rateCard.p2p_point_to_point_rate || 100
      } else if (serviceType === 'hourly') {
        estimatedPrice = rateCard.charter_hourly_rate || rateCard.dt_per_hour_rate || 80
      } else if (serviceType === 'point_to_point') {
        // Use point-to-point rate or estimate with distance+time
        estimatedPrice = rateCard.p2p_point_to_point_rate || 
                        ((rateCard.dt_per_mile_rate || 2) * 10) + (rateCard.dt_per_hour_rate || 50)
      }

      // No minimum fare check needed for new structure

      // Determine tier based on default logic (columns don't exist in DB)
      let tier = 'Standard'
      const rating = 4.0 // Default rating since column doesn't exist
      if (rating >= 4.8) {
        tier = 'Elite'
      } else if (rating >= 4.5) {
        tier = 'Premium'
      }

      // Calculate response time priority based on tier
      let avgResponseTime = '< 15 min'
      if (tier === 'Elite') {
        avgResponseTime = '< 5 min'
      } else if (tier === 'Premium') {
        avgResponseTime = '< 10 min'
      }

      return {
        id: affiliate.id,
        company_name: affiliate.name,
        city: affiliate.city,
        tier: tier,
        rating: rating,
        avg_response_time: avgResponseTime,
        vehicle_type: vehicle.type,
        vehicle_model: `${vehicle.make} ${vehicle.model}`,
        capacity: vehicle.capacity,
        base_rate: rateCard.p2p_point_to_point_rate || rateCard.airport_transfer_flat_rate || rateCard.charter_hourly_rate,
        total_price: Math.round(estimatedPrice),
        features: ['Professional Driver', 'Clean Vehicle', 'Phone Charging'], // Default features
        availability: 'confirmed',
        estimated_arrival: '20-25 minutes', // Default for Standard tier
        notes: tier === 'Elite' ? 'Premium service with professional chauffeur' : null
      }
    })

    // Sort by tier priority (Elite first, then Premium, then Standard)
    const sortedAffiliates = enhancedAffiliates.sort((a, b) => {
      const tierOrder = { 'Elite': 0, 'Premium': 1, 'Standard': 2 }
      const aTierOrder = tierOrder[a.tier as keyof typeof tierOrder] || 2
      const bTierOrder = tierOrder[b.tier as keyof typeof tierOrder] || 2
      
      if (aTierOrder !== bTierOrder) {
        return aTierOrder - bTierOrder
      }
      
      // If same tier, sort by rating
      return (b.rating || 0) - (a.rating || 0)
    })

    console.log(`Event Manager Matching API - Returning ${sortedAffiliates.length} sorted affiliates`)

    return NextResponse.json({
      success: true,
      affiliates: sortedAffiliates,
      searchCriteria: { city, vehicleType, serviceType, date: testParams.date, time: testParams.time, passengers },
      debug: {
        total_affiliates: affiliates?.length || 0,
        total_vehicles: vehicles?.length || 0,
        total_rate_cards: rateCards?.length || 0,
        valid_affiliates: validAffiliates.length,
        final_results: sortedAffiliates.length
      }
    })

  } catch (error) {
    console.error('Error in event manager matching test:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}
