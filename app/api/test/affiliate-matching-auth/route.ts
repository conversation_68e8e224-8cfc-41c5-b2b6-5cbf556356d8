import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

/**
 * TEST ENDPOINT: Test affiliate matching API with authentication bypass
 */
export async function POST(request: NextRequest) {
  try {
    console.log('TEST: Testing affiliate matching API with auth bypass')

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Test parameters matching the quote form
    const testParams = {
      city: 'Austin',
      vehicleType: 'SUV',
      serviceType: 'point',
      date: '2025-01-02',
      time: '10:00',
      passengers: 1
    }

    console.log('Testing with parameters:', testParams)

    // Call the actual affiliate matching API logic (without auth)
    const { city, vehicleType, serviceType, date, time, passengers } = testParams

    // Map service types to match database expectations
    const mappedServiceType = serviceType === 'point' ? 'point_to_point' : serviceType
    console.log('Mapped service type:', serviceType, '->', mappedServiceType)

    // Clean city name to remove state suffix
    const cleanCity = city.split(',')[0].trim()
    console.log('Cleaned city:', city, '->', cleanCity)

    // Query to find matching affiliates
    const { data: affiliates, error: affiliatesError } = await supabase
      .from('affiliate_companies')
      .select('id, name, city, status, application_status')
      .eq('status', 'active')
      .eq('application_status', 'approved')
      .ilike('city', `%${cleanCity}%`)

    if (affiliatesError) {
      console.error('Error fetching affiliates:', affiliatesError)
      return NextResponse.json({ error: 'Failed to fetch affiliates' }, { status: 500 })
    }

    console.log(`Found ${affiliates?.length || 0} potential affiliates`)

    if (!affiliates || affiliates.length === 0) {
      return NextResponse.json({
        success: true,
        affiliates: [],
        message: 'No affiliates found',
        searchCriteria: { city: cleanCity, vehicleType, serviceType: mappedServiceType, date, time, passengers }
      })
    }

    // Get vehicles and rate cards
    const affiliateIds = affiliates.map(a => a.id)
    
    // Get vehicles
    let vehicleQuery = supabase
      .from('vehicles')
      .select('id, company_id, type, make, model, capacity, status')
      .in('company_id', affiliateIds)
      .eq('status', 'active')

    if (vehicleType) {
      vehicleQuery = vehicleQuery.eq('type', vehicleType)
    }

    if (passengers) {
      vehicleQuery = vehicleQuery.gte('capacity', passengers)
    }

    const { data: vehicles, error: vehiclesError } = await vehicleQuery

    if (vehiclesError) {
      console.error('Error fetching vehicles:', vehiclesError)
      return NextResponse.json({ error: 'Failed to fetch vehicles' }, { status: 500 })
    }

    // Get rate cards
    let rateCardQuery = supabase
      .from('rate_cards')
      .select(`
        id, company_id, vehicle_type, pricing_model_type,
        p2p_point_to_point_rate, dt_per_mile_rate, dt_per_hour_rate,
        airport_transfer_flat_rate, charter_hourly_rate, status, is_active
      `)
      .in('company_id', affiliateIds)
      .eq('is_active', true)

    if (vehicleType) {
      rateCardQuery = rateCardQuery.eq('vehicle_type', vehicleType)
    }

    // Filter by pricing model based on service type (more flexible)
    if (mappedServiceType === 'point_to_point') {
      rateCardQuery = rateCardQuery.in('pricing_model_type', ['P2P', 'AIRPORT_TRANSFER', 'D+T'])
    } else if (mappedServiceType === 'airport') {
      rateCardQuery = rateCardQuery.in('pricing_model_type', ['AIRPORT_TRANSFER', 'P2P'])
    } else if (mappedServiceType === 'hourly') {
      rateCardQuery = rateCardQuery.eq('pricing_model_type', 'CHARTER')
    }

    const { data: rateCards, error: rateCardsError } = await rateCardQuery

    if (rateCardsError) {
      console.error('Error fetching rate cards:', rateCardsError)
      return NextResponse.json({ error: 'Failed to fetch rate cards' }, { status: 500 })
    }

    console.log(`Found ${vehicles?.length || 0} vehicles and ${rateCards?.length || 0} rate cards`)

    // Filter affiliates that have both vehicles and rate cards
    const validAffiliates = affiliates.filter(affiliate => {
      const affiliateVehicles = vehicles?.filter(v => v.company_id === affiliate.id) || []
      const affiliateRateCards = rateCards?.filter(rc => rc.company_id === affiliate.id) || []
      
      return affiliateVehicles.length > 0 && affiliateRateCards.length > 0
    })

    console.log(`${validAffiliates.length} affiliates have both vehicles and rate cards`)

    // Transform affiliate data
    const enhancedAffiliates = validAffiliates.map((affiliate: any) => {
      const affiliateVehicles = vehicles?.filter(v => v.company_id === affiliate.id) || []
      const affiliateRateCards = rateCards?.filter(rc => rc.company_id === affiliate.id) || []
      
      const vehicle = affiliateVehicles[0]
      const rateCard = affiliateRateCards[0]

      // Calculate pricing
      let estimatedPrice = 100
      if (mappedServiceType === 'point_to_point') {
        if (rateCard.pricing_model_type === 'P2P') {
          estimatedPrice = rateCard.p2p_point_to_point_rate || 100
        } else if (rateCard.pricing_model_type === 'AIRPORT_TRANSFER') {
          estimatedPrice = rateCard.airport_transfer_flat_rate || 100
        }
      }

      return {
        id: affiliate.id,
        company_name: affiliate.name,
        city: affiliate.city,
        tier: 'Standard',
        rating: 4.0,
        avg_response_time: '< 15 min',
        vehicle_type: vehicle.type,
        vehicle_model: `${vehicle.make} ${vehicle.model}`,
        capacity: vehicle.capacity,
        base_rate: rateCard.p2p_point_to_point_rate || rateCard.airport_transfer_flat_rate,
        total_price: Math.round(estimatedPrice),
        estimated_rate: Math.round(estimatedPrice),
        features: ['Professional Driver', 'Clean Vehicle', 'Phone Charging'],
        availability: 'confirmed',
        estimated_arrival: '20-25 minutes'
      }
    })

    return NextResponse.json({
      success: true,
      affiliates: enhancedAffiliates,
      message: `Found ${enhancedAffiliates.length} matching affiliates`,
      debug: {
        total_affiliates: affiliates?.length || 0,
        total_vehicles: vehicles?.length || 0,
        total_rate_cards: rateCards?.length || 0,
        valid_affiliates: validAffiliates.length,
        final_results: enhancedAffiliates.length
      },
      searchCriteria: {
        city: cleanCity,
        vehicleType,
        serviceType: mappedServiceType,
        originalCity: city,
        originalServiceType: serviceType,
        date,
        time,
        passengers
      }
    })

  } catch (error) {
    console.error('Error in affiliate matching auth test:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}
