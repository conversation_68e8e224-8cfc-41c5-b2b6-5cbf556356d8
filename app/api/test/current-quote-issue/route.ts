import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

/**
 * TEST ENDPOINT: Debug the current quote issue
 * Testing with the exact parameters from the failing quote form
 */
export async function POST(request: NextRequest) {
  try {
    console.log('TEST: Debugging current quote issue')

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Exact parameters from the failing quote form
    const currentQuoteData = {
      service_type: 'point', // Point-to-Point from form
      vehicle_type: 'SUV',
      passenger_count: 1,
      pickup_location: 'SSSSSSSSS',
      dropoff_location: 'TTTTTTTTTTTTT',
      city: 'Austin', // Just "Austin" now, not "Austin, TX"
      date: '2025-01-02',
      time: '10:00'
    }

    console.log('Current quote form data:', currentQuoteData)

    // Step 1: Test the affiliate matching API directly (without auth)
    console.log('Step 1: Testing affiliate matching logic...')
    
    // Apply the same transformations as the API
    const mappedServiceType = currentQuoteData.service_type === 'point' ? 'point_to_point' : currentQuoteData.service_type
    const cleanCity = currentQuoteData.city.split(',')[0].trim() // Should still be "Austin"

    console.log('Transformations:', {
      originalServiceType: currentQuoteData.service_type,
      mappedServiceType: mappedServiceType,
      originalCity: currentQuoteData.city,
      cleanCity: cleanCity
    })

    // Step 2: Check affiliates
    const { data: affiliates, error: affiliatesError } = await supabase
      .from('affiliate_companies')
      .select('id, name, city, status, application_status')
      .eq('status', 'active')
      .eq('application_status', 'approved')
      .ilike('city', `%${cleanCity}%`)

    console.log('Affiliates query result:', {
      success: !affiliatesError,
      error: affiliatesError?.message,
      count: affiliates?.length || 0,
      affiliates: affiliates?.map(a => ({ id: a.id, name: a.name, city: a.city, status: a.status, application_status: a.application_status }))
    })

    if (!affiliates || affiliates.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'No affiliates found',
        debug: {
          query: `city ILIKE '%${cleanCity}%' AND status = 'active' AND application_status = 'approved'`,
          cleanCity,
          originalCity: currentQuoteData.city
        }
      })
    }

    // Step 3: Check vehicles
    const affiliateIds = affiliates.map(a => a.id)
    
    const { data: vehicles, error: vehiclesError } = await supabase
      .from('vehicles')
      .select('id, company_id, type, make, model, capacity, status')
      .in('company_id', affiliateIds)
      .eq('status', 'active')
      .eq('type', currentQuoteData.vehicle_type)
      .gte('capacity', currentQuoteData.passenger_count)

    console.log('Vehicles query result:', {
      success: !vehiclesError,
      error: vehiclesError?.message,
      count: vehicles?.length || 0,
      vehicles: vehicles?.map(v => ({ 
        id: v.id, 
        company_id: v.company_id, 
        type: v.type, 
        capacity: v.capacity,
        status: v.status
      }))
    })

    // Step 4: Check rate cards with service type filtering
    let rateCardQuery = supabase
      .from('rate_cards')
      .select(`
        id, company_id, vehicle_type, pricing_model_type,
        p2p_point_to_point_rate, dt_per_mile_rate, dt_per_hour_rate,
        airport_transfer_flat_rate, charter_hourly_rate, status, is_active
      `)
      .in('company_id', affiliateIds)
      .eq('is_active', true)
      .eq('vehicle_type', currentQuoteData.vehicle_type)

    // Apply service type filtering (more flexible)
    if (mappedServiceType === 'point_to_point') {
      // Point-to-point can use P2P, AIRPORT_TRANSFER, or D+T pricing models
      rateCardQuery = rateCardQuery.in('pricing_model_type', ['P2P', 'AIRPORT_TRANSFER', 'D+T'])
    } else if (mappedServiceType === 'airport') {
      // Airport transfers can use AIRPORT_TRANSFER or P2P pricing models
      rateCardQuery = rateCardQuery.in('pricing_model_type', ['AIRPORT_TRANSFER', 'P2P'])
    } else if (mappedServiceType === 'hourly') {
      // Hourly service uses CHARTER pricing model
      rateCardQuery = rateCardQuery.eq('pricing_model_type', 'CHARTER')
    }

    const { data: rateCards, error: rateCardsError } = await rateCardQuery

    console.log('Rate cards query result:', {
      success: !rateCardsError,
      error: rateCardsError?.message,
      count: rateCards?.length || 0,
      query_details: {
        service_type: mappedServiceType,
        pricing_model_filter: mappedServiceType === 'point_to_point' ? 'P2P' : 
                             mappedServiceType === 'airport' ? 'AIRPORT_TRANSFER' : 
                             mappedServiceType === 'hourly' ? 'CHARTER' : 'none'
      },
      rateCards: rateCards?.map(rc => ({ 
        id: rc.id, 
        company_id: rc.company_id, 
        vehicle_type: rc.vehicle_type,
        pricing_model_type: rc.pricing_model_type,
        is_active: rc.is_active,
        p2p_rate: rc.p2p_point_to_point_rate
      }))
    })

    // Step 5: Check what rate cards exist without service type filtering
    const { data: allRateCards, error: allRateCardsError } = await supabase
      .from('rate_cards')
      .select('id, company_id, vehicle_type, pricing_model_type, is_active')
      .in('company_id', affiliateIds)
      .eq('is_active', true)
      .eq('vehicle_type', currentQuoteData.vehicle_type)

    console.log('All rate cards (no service type filter):', {
      success: !allRateCardsError,
      error: allRateCardsError?.message,
      count: allRateCards?.length || 0,
      rateCards: allRateCards?.map(rc => ({ 
        id: rc.id, 
        company_id: rc.company_id, 
        vehicle_type: rc.vehicle_type,
        pricing_model_type: rc.pricing_model_type,
        is_active: rc.is_active
      }))
    })

    // Step 6: Calculate final matches
    const validAffiliates = affiliates.filter(affiliate => {
      const affiliateVehicles = vehicles?.filter(v => v.company_id === affiliate.id) || []
      const affiliateRateCards = rateCards?.filter(rc => rc.company_id === affiliate.id) || []
      
      return affiliateVehicles.length > 0 && affiliateRateCards.length > 0
    })

    // Step 7: Test database function
    const { data: dbFunctionResult, error: dbFunctionError } = await supabase
      .rpc('find_matching_affiliates_for_quote', {
        p_pickup_lat: 30.2672, // Austin coordinates
        p_pickup_lng: -97.7431,
        p_pickup_city: cleanCity,
        p_vehicle_type: currentQuoteData.vehicle_type,
        p_pickup_date: currentQuoteData.date,
        p_service_type: mappedServiceType
      })

    console.log('Database function result:', {
      success: !dbFunctionError,
      error: dbFunctionError?.message,
      count: dbFunctionResult?.length || 0
    })

    return NextResponse.json({
      success: true,
      message: 'Current quote issue debugging completed',
      quote_data: currentQuoteData,
      transformations: {
        service_type: `"${currentQuoteData.service_type}" -> "${mappedServiceType}"`,
        city: `"${currentQuoteData.city}" -> "${cleanCity}"`
      },
      step_by_step_results: {
        step1_affiliates: {
          found: affiliates?.length || 0,
          details: affiliates?.map(a => ({ name: a.name, city: a.city, status: a.status, application_status: a.application_status }))
        },
        step2_vehicles: {
          found: vehicles?.length || 0,
          details: vehicles?.map(v => ({ company_id: v.company_id, type: v.type, capacity: v.capacity }))
        },
        step3_rate_cards_filtered: {
          found: rateCards?.length || 0,
          filter_applied: mappedServiceType === 'point_to_point' ? 'P2P' : 'other',
          details: rateCards?.map(rc => ({ company_id: rc.company_id, pricing_model: rc.pricing_model_type }))
        },
        step4_all_rate_cards: {
          found: allRateCards?.length || 0,
          details: allRateCards?.map(rc => ({ company_id: rc.company_id, pricing_model: rc.pricing_model_type }))
        },
        step5_final_matches: {
          valid_affiliates: validAffiliates.length,
          details: validAffiliates.map(a => ({ name: a.name }))
        },
        step6_database_function: {
          results: dbFunctionResult?.length || 0,
          error: dbFunctionError?.message
        }
      },
      diagnosis: {
        issue_found: validAffiliates.length === 0,
        likely_cause: rateCards?.length === 0 ? 'No rate cards match the service type filter' : 
                     vehicles?.length === 0 ? 'No vehicles match the criteria' :
                     affiliates?.length === 0 ? 'No affiliates found' : 'Unknown',
        recommendation: rateCards?.length === 0 && allRateCards?.length > 0 ? 
                       'Service type filtering is too restrictive - check pricing model mapping' : 
                       'Check other criteria'
      }
    })

  } catch (error) {
    console.error('Error in current quote issue test:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}
