import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

/**
 * TEST ENDPOINT: Debug affiliate matching API
 * This endpoint tests the exact same call that the quote form makes
 */
export async function POST(request: NextRequest) {
  try {
    console.log('TEST: Debugging affiliate matching API call')

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Test the exact same parameters that the quote form sends
    const testParams = {
      city: 'Austin',
      vehicleType: 'SUV',
      serviceType: 'airport',
      date: '2025-01-02',
      time: '10:00',
      passengers: 4
    }

    console.log('Testing with parameters:', testParams)

    // Step 1: Test the affiliate_companies query directly
    console.log('Step 1: Testing affiliate_companies query...')
    
    let query = supabase
      .from('affiliate_companies')
      .select(`
        id,
        name as company_name,
        city,
        status,
        application_status,
        tier,
        rating,
        avg_response_time,
        vehicles!inner (
          id,
          type as vehicle_type,
          make,
          model,
          capacity,
          status as vehicle_status,
          rate_cards!inner (
            id,
            vehicle_type,
            pricing_model_type,
            p2p_point_to_point_rate,
            dt_per_mile_rate,
            dt_per_hour_rate,
            airport_transfer_flat_rate,
            charter_hourly_rate,
            status as rate_status,
            is_active
          )
        )
      `)
      .eq('status', 'active')
      .eq('application_status', 'approved')
      .eq('vehicles.status', 'active')
      .eq('vehicles.rate_cards.is_active', true)
      .ilike('city', `%${testParams.city}%`)

    // Add vehicle type filter
    if (testParams.vehicleType) {
      query = query.eq('vehicles.type', testParams.vehicleType)
    }

    // Add capacity filter
    if (testParams.passengers) {
      query = query.gte('vehicles.capacity', testParams.passengers)
    }

    const { data: affiliates, error: affiliatesError } = await query

    console.log('Direct query result:', {
      success: !affiliatesError,
      error: affiliatesError?.message,
      count: affiliates?.length || 0,
      affiliates: affiliates?.map(a => ({
        id: a.id,
        name: a.company_name,
        city: a.city,
        status: a.status,
        application_status: a.application_status,
        vehicles: a.vehicles?.length || 0
      }))
    })

    // Step 2: Test without vehicle type filter
    console.log('Step 2: Testing without vehicle type filter...')
    
    const { data: affiliatesNoVehicleFilter, error: noVehicleError } = await supabase
      .from('affiliate_companies')
      .select(`
        id,
        name as company_name,
        city,
        status,
        application_status,
        vehicles (
          id,
          type,
          status,
          rate_cards (
            id,
            is_active,
            status
          )
        )
      `)
      .eq('status', 'active')
      .eq('application_status', 'approved')
      .ilike('city', `%${testParams.city}%`)

    console.log('Query without vehicle filter:', {
      success: !noVehicleError,
      error: noVehicleError?.message,
      count: affiliatesNoVehicleFilter?.length || 0,
      affiliates: affiliatesNoVehicleFilter?.map(a => ({
        id: a.id,
        name: a.company_name,
        city: a.city,
        status: a.status,
        application_status: a.application_status,
        vehicles: a.vehicles?.map(v => ({
          type: v.type,
          status: v.status,
          rate_cards: v.rate_cards?.length || 0
        }))
      }))
    })

    // Step 3: Test basic affiliate companies query
    console.log('Step 3: Testing basic affiliate companies...')
    
    const { data: basicAffiliates, error: basicError } = await supabase
      .from('affiliate_companies')
      .select('id, name, city, status, application_status')
      .ilike('city', `%${testParams.city}%`)

    console.log('Basic affiliates query:', {
      success: !basicError,
      error: basicError?.message,
      count: basicAffiliates?.length || 0,
      affiliates: basicAffiliates
    })

    // Step 4: Test the fixed affiliate matching API
    console.log('Step 4: Testing fixed affiliate matching API...')

    try {
      const response = await fetch('http://localhost:3000/api/event-manager/quotes/match-affiliates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token' // This will fail auth but we can see the error
        },
        body: JSON.stringify(testParams)
      })

      const apiResult = await response.json()
      console.log('API test result:', { status: response.status, data: apiResult })

      return NextResponse.json({
        success: true,
        message: 'Affiliate matching debug completed',
        test_parameters: testParams,
        results: {
          direct_query: {
            success: !affiliatesError,
            error: affiliatesError?.message,
            count: affiliates?.length || 0,
            data: affiliates
          },
          no_vehicle_filter: {
            success: !noVehicleError,
            error: noVehicleError?.message,
            count: affiliatesNoVehicleFilter?.length || 0,
            data: affiliatesNoVehicleFilter
          },
          basic_query: {
            success: !basicError,
            error: basicError?.message,
            count: basicAffiliates?.length || 0,
            data: basicAffiliates
          },
          api_test: {
            status: response.status,
            data: apiResult
          }
        }
      })
    } catch (apiError) {
      console.error('API test failed:', apiError)

      return NextResponse.json({
        success: true,
        message: 'Affiliate matching debug completed',
        test_parameters: testParams,
        results: {
          direct_query: {
            success: !affiliatesError,
            error: affiliatesError?.message,
            count: affiliates?.length || 0,
            data: affiliates
          },
          no_vehicle_filter: {
            success: !noVehicleError,
            error: noVehicleError?.message,
            count: affiliatesNoVehicleFilter?.length || 0,
            data: affiliatesNoVehicleFilter
          },
          basic_query: {
            success: !basicError,
            error: basicError?.message,
            count: basicAffiliates?.length || 0,
            data: basicAffiliates
          },
          api_test: {
            error: apiError instanceof Error ? apiError.message : 'API test failed'
          }
        }
      })
    }

  } catch (error) {
    console.error('Error in affiliate matching debug:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}
