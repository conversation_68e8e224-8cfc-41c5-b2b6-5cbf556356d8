import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

/**
 * TEST ENDPOINT: Test Austin quote matching
 * This endpoint tests the complete quote creation and affiliate matching flow
 */
export async function POST(request: NextRequest) {
  try {
    console.log('TEST: Creating Austin quote and testing affiliate matching')

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Step 1: Create a test quote
    const quoteData = {
      reference_number: `Q${Date.now()}`, // Generate unique reference
      customer_id: '7ab6f229-1250-485b-8a17-1947237b0ca3', // Required field
      service_type: 'airport',
      vehicle_type: 'SUV',
      passenger_count: 4,
      date: '2025-01-02',
      time: '10:00:00',
      pickup_location: 'Austin Airport',
      dropoff_location: 'Downtown Austin',
      city: 'Austin',
      duration: '1 hour',
      distance: '20 miles',
      priority: 'medium',
      contact_name: 'Test Client',
      contact_email: '<EMAIL>',
      contact_phone: '************',
      special_requests: ['Test quote for Austin affiliate matching'],
      status: 'pending'
    }

    const { data: createdQuote, error: quoteError } = await supabase
      .from('quotes')
      .insert(quoteData)
      .select()
      .single()

    if (quoteError) {
      console.error('Error creating quote:', quoteError)
      return NextResponse.json(
        { error: 'Failed to create quote', details: quoteError.message },
        { status: 500 }
      )
    }

    console.log('Created quote:', createdQuote)

    // Step 2: Test affiliate matching
    const { data: matchingResult, error: matchingError } = await supabase
      .rpc('find_matching_affiliates_for_quote', {
        p_pickup_lat: 30.2672,
        p_pickup_lng: -97.7431,
        p_pickup_city: 'Austin',
        p_vehicle_type: 'SUV',
        p_pickup_date: '2025-01-02',
        p_service_type: 'airport'
      })

    if (matchingError) {
      console.error('Error in affiliate matching:', matchingError)
      return NextResponse.json(
        { error: 'Failed to match affiliates', details: matchingError.message },
        { status: 500 }
      )
    }

    console.log('Matching result:', matchingResult)

    // Step 3: Test the event-manager matching API (requires auth, so skip for now)
    let apiMatchingResult = { message: 'Skipped - requires authentication' }

    return NextResponse.json({
      success: true,
      message: 'Austin quote matching test completed',
      quote: createdQuote,
      database_matching: {
        count: matchingResult?.length || 0,
        affiliates: matchingResult || []
      },
      api_matching: apiMatchingResult,
      test_summary: {
        quote_created: !!createdQuote,
        database_matches: matchingResult?.length || 0,
        api_matches: apiMatchingResult?.affiliates?.length || 0
      }
    })

  } catch (error) {
    console.error('Error in Austin quote matching test:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}
