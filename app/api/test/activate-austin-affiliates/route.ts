import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

/**
 * TEST ENDPOINT: Activate Austin affiliates and their rate cards
 * This endpoint is for testing the affiliate matching fix
 */
export async function POST(request: NextRequest) {
  try {
    console.log('TEST: Activating Austin affiliates and rate cards')

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Step 1: Approve Austin affiliate companies
    const { data: updatedCompanies, error: companyError } = await supabase
      .from('affiliate_companies')
      .update({
        application_status: 'approved',
        status: 'active',
        approved_at: new Date().toISOString(),
        approved_by: '7ab6f229-1250-485b-8a17-1947237b0ca3'
      })
      .ilike('city', '%Austin%')
      .select('id, name, city, application_status')

    if (companyError) {
      console.error('Error updating companies:', companyError)
      return NextResponse.json(
        { error: 'Failed to update companies', details: companyError.message },
        { status: 500 }
      )
    }

    console.log('Updated companies:', updatedCompanies)

    // Step 2: Activate rate cards for Austin companies
    const { data: austinCompanies } = await supabase
      .from('affiliate_companies')
      .select('id')
      .ilike('city', '%Austin%')

    const companyIds = austinCompanies?.map(c => c.id) || []

    const { data: activatedRateCards, error: rateCardError } = await supabase
      .from('rate_cards')
      .update({
        is_active: true,
        status: 'approved',
        updated_at: new Date().toISOString()
      })
      .in('company_id', companyIds)
      .select('id, company_id, vehicle_type, status, is_active')

    if (rateCardError) {
      console.error('Error activating rate cards:', rateCardError)
      return NextResponse.json(
        { error: 'Failed to activate rate cards', details: rateCardError.message },
        { status: 500 }
      )
    }

    console.log('Activated rate cards:', activatedRateCards)

    // Step 3: Test the affiliate matching function
    const { data: matchingResult, error: matchingError } = await supabase
      .rpc('find_matching_affiliates_for_quote', {
        quote_lat: 30.2672,
        quote_lng: -97.7431,
        quote_city: 'Austin',
        vehicle_type: 'SUV',
        quote_date: new Date().toISOString().split('T')[0],
        service_type: 'airport'
      })

    if (matchingError) {
      console.error('Error testing matching function:', matchingError)
    }

    console.log('Matching test result:', matchingResult)

    return NextResponse.json({
      success: true,
      message: 'Austin affiliates activated successfully',
      updated_companies: updatedCompanies?.length || 0,
      activated_rate_cards: activatedRateCards?.length || 0,
      companies: updatedCompanies,
      rate_cards: activatedRateCards,
      matching_test: matchingResult || []
    })

  } catch (error) {
    console.error('Error in test activation API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}
