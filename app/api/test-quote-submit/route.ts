import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { quoteRequest, selectedAffiliates } = body

    console.log('Test Quote Submit - Starting request')
    console.log('Quote Request:', quoteRequest)
    console.log('Selected Affiliates:', selectedAffiliates)

    // Create service role client for database operations (bypasses RLS)
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Generate quote reference number
    const quoteReference = `TEST-${Date.now()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`

    // Test user ID (use a known user ID from your database)
    const testUserId = '2fee08c5-e012-4db0-842e-ebf4d7997c39' // Your client777 user ID

    // Create the main quote record
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .insert({
        reference_number: quoteReference,
        customer_id: testUserId,
        pickup_location: quoteRequest.pickupLocation || 'Test Pickup',
        dropoff_location: quoteRequest.dropoffLocation || 'Test Dropoff',
        city: quoteRequest.city || 'Austin',
        date: quoteRequest.date || '2025-06-12',
        time: quoteRequest.time || '11:11',
        duration: '1 hour',
        distance: '25 miles',
        passenger_count: quoteRequest.passengers || 1,
        vehicle_type: quoteRequest.vehicleType || 'SUV',
        service_type: quoteRequest.serviceType || 'point',
        special_requests: quoteRequest.specialRequirements || null,
        event_id: quoteRequest.eventId || null,
        status: 'pending',
        priority: 'medium'
      })
      .select()
      .single()

    if (quoteError) {
      console.error('Error creating quote:', quoteError)
      return NextResponse.json({ error: 'Failed to create quote', details: quoteError }, { status: 500 })
    }

    console.log('Quote created successfully:', quote)

    // Test creating affiliate offers - try minimal data first
    const testAffiliateId = '62ecb99e-c299-4f90-8006-e60064c9e6e7'

    // First, check if the affiliate company exists
    const { data: affiliateCheck, error: affiliateCheckError } = await supabase
      .from('affiliate_companies')
      .select('id, name')
      .eq('id', testAffiliateId)
      .single()

    console.log('Affiliate check:', { affiliateCheck, affiliateCheckError })

    if (affiliateCheckError) {
      return NextResponse.json({
        error: 'Affiliate not found',
        details: affiliateCheckError
      }, { status: 400 })
    }

    // Try creating offer with minimal required fields only
    const expiresAt = new Date(Date.now() + (24 * 60 * 60 * 1000))

    const minimalOfferData = {
      quote_id: quote.id,
      company_id: testAffiliateId,
      rate_amount: 100,
      currency: 'USD',
      status: 'PENDING',
      expires_at: expiresAt.toISOString(),
      is_counter_offer: false,
      created_by: testUserId,
      updated_by: testUserId
    }

    console.log('Creating minimal test offer:', minimalOfferData)

    const { data: newOffer, error: offerError } = await supabase
      .from('quote_affiliate_offers')
      .insert(minimalOfferData)
      .select()
      .single()

    if (offerError) {
      console.error('Error creating minimal offer:', offerError)
      console.error('Error code:', offerError.code)
      console.error('Error message:', offerError.message)
      console.error('Error details:', offerError.details)
      console.error('Error hint:', offerError.hint)
      return NextResponse.json({
        error: 'Failed to create minimal offer',
        details: offerError,
        offerData: minimalOfferData,
        errorBreakdown: {
          code: offerError.code,
          message: offerError.message,
          details: offerError.details,
          hint: offerError.hint
        }
      }, { status: 500 })
    }

    console.log('Minimal offer created successfully:', newOffer)

    return NextResponse.json({
      success: true,
      quoteId: quote.id,
      quoteReference: quoteReference,
      offersCreated: 1,
      offer: newOffer,
      message: `Test quote ${quoteReference} created with 1 offer`
    })

  } catch (error) {
    console.error('Error in test quote submit:', error)
    return NextResponse.json(
      { error: 'Test failed', details: error },
      { status: 500 }
    )
  }
}
