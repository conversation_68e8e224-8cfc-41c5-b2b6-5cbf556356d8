-- Multi-Tenant Architecture Implementation
-- This migration implements the three-tier tenant system

-- Create tenants table
CREATE TABLE IF NOT EXISTS public.tenants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  domain VARCHAR(255),
  parent_tenant_id UUID REFERENCES tenants(id),
  tenant_type TEXT CHECK (tenant_type IN ('shared', 'segregated', 'white_label')) NOT NULL DEFAULT 'shared',
  status TEXT CHECK (status IN ('active', 'inactive', 'suspended')) DEFAULT 'active',
  branding JSONB DEFAULT '{}',
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create tenant branding table
CREATE TABLE IF NOT EXISTS public.tenant_branding (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  logo_url VARCHAR(500),
  favicon_url VARCHAR(500),
  primary_color VARCHAR(7),
  secondary_color VARCHAR(7),
  background_color VARCHAR(7),
  custom_css TEXT,
  email_templates JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tenant_id)
);

-- Add tenant_id to existing tables
ALTER TABLE public.customers ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
ALTER TABLE public.quotes ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
ALTER TABLE public.events ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);

-- Update affiliate_companies table for network participation
ALTER TABLE public.affiliate_companies ADD COLUMN IF NOT EXISTS network_participation JSONB DEFAULT '{
  "global_network": false,
  "exclusive_brands": [],
  "rate_sharing": "transparent",
  "preferred_volume": "maximum"
}';

-- Create tenant-affiliate relationships table
CREATE TABLE IF NOT EXISTS public.tenant_affiliates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  affiliate_id UUID REFERENCES affiliate_companies(id) ON DELETE CASCADE,
  relationship_type TEXT CHECK (relationship_type IN ('exclusive', 'shared', 'preferred')) NOT NULL DEFAULT 'shared',
  commission_override DECIMAL(5,2),
  priority_level INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tenant_id, affiliate_id)
);

-- Create customer migration tracking table
CREATE TABLE IF NOT EXISTS public.customer_migrations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  from_tenant_id UUID REFERENCES tenants(id),
  to_tenant_id UUID REFERENCES tenants(id),
  migration_type TEXT CHECK (migration_type IN ('upsell', 'transfer', 'merge')) NOT NULL,
  migration_status TEXT CHECK (migration_status IN ('pending', 'completed', 'failed')) DEFAULT 'pending',
  migration_data JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Create default shared tenant
INSERT INTO public.tenants (name, slug, tenant_type, settings) 
VALUES (
  'TransFlow Shared',
  'transflow-shared',
  'shared',
  '{"allowGlobalNetwork": true, "upsell_threshold": 10}'
) ON CONFLICT (slug) DO NOTHING;

-- Get the shared tenant ID for default assignments
DO $$
DECLARE
  shared_tenant_id UUID;
BEGIN
  SELECT id INTO shared_tenant_id FROM public.tenants WHERE slug = 'transflow-shared';
  
  -- Update existing customers to use shared tenant
  UPDATE public.customers SET tenant_id = shared_tenant_id WHERE tenant_id IS NULL;
  UPDATE public.quotes SET tenant_id = shared_tenant_id WHERE tenant_id IS NULL;
  UPDATE public.events SET tenant_id = shared_tenant_id WHERE tenant_id IS NULL;
  UPDATE public.bookings SET tenant_id = shared_tenant_id WHERE tenant_id IS NULL;
END $$;

-- Enable RLS on tenant-aware tables
ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tenant_branding ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tenant_affiliates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_migrations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for tenant isolation
-- Customers policy
CREATE POLICY "tenant_isolation_customers" ON public.customers
  FOR ALL TO authenticated
  USING (
    tenant_id = COALESCE(
      (current_setting('app.current_tenant_id', true))::UUID,
      tenant_id
    )
  );

-- Super admin bypass policy for customers
CREATE POLICY "super_admin_access_customers" ON public.customers
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_id = auth.uid() 
      AND 'SUPER_ADMIN' = ANY(roles)
    )
  );

-- Similar policies for other tables
CREATE POLICY "tenant_isolation_quotes" ON public.quotes
  FOR ALL TO authenticated
  USING (
    tenant_id = COALESCE(
      (current_setting('app.current_tenant_id', true))::UUID,
      tenant_id
    )
  );

CREATE POLICY "super_admin_access_quotes" ON public.quotes
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_id = auth.uid() 
      AND 'SUPER_ADMIN' = ANY(roles)
    )
  );

-- Tenants table policies
CREATE POLICY "tenants_access" ON public.tenants
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_id = auth.uid() 
      AND ('SUPER_ADMIN' = ANY(roles) OR 'ADMIN' = ANY(roles))
    )
  );

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_customers_tenant_id ON public.customers(tenant_id);
CREATE INDEX IF NOT EXISTS idx_quotes_tenant_id ON public.quotes(tenant_id);
CREATE INDEX IF NOT EXISTS idx_events_tenant_id ON public.events(tenant_id);
CREATE INDEX IF NOT EXISTS idx_bookings_tenant_id ON public.bookings(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_affiliates_tenant_id ON public.tenant_affiliates(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_affiliates_affiliate_id ON public.tenant_affiliates(affiliate_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON public.tenants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT ALL ON public.tenants TO authenticated;
GRANT ALL ON public.tenant_branding TO authenticated;
GRANT ALL ON public.tenant_affiliates TO authenticated;
GRANT ALL ON public.customer_migrations TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE public.tenants IS 'Multi-tenant configuration supporting shared, segregated, and white-label architectures';
COMMENT ON TABLE public.tenant_branding IS 'Custom branding configuration per tenant';
COMMENT ON TABLE public.tenant_affiliates IS 'Affiliate network participation and relationships per tenant';
COMMENT ON TABLE public.customer_migrations IS 'Tracking customer migrations between tenant levels';

SELECT 'Multi-tenant architecture migration completed successfully' AS status;
