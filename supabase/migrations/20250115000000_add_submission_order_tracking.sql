-- Add submission order and tier tracking to existing tables
-- Migration: 20250115000000_add_submission_order_tracking.sql

BEGIN;

-- Add submission order tracking to quote_affiliate_offers
ALTER TABLE public.quote_affiliate_offers 
ADD COLUMN IF NOT EXISTS submission_order INTEGER,
ADD COLUMN IF NOT EXISTS sent_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS scheduled_at TIMESTAMPTZ;

-- Add tier column to affiliate_companies for basic tier tracking
ALTER TABLE public.affiliate_companies 
ADD COLUMN IF NOT EXISTS tier VARCHAR(20) DEFAULT 'Standard' CHECK (tier IN ('Standard', 'Premium', 'Elite'));

-- Create index for submission order queries
CREATE INDEX IF NOT EXISTS idx_quote_affiliate_offers_submission_order 
ON public.quote_affiliate_offers(quote_id, submission_order);

-- Create index for tier-based queries
CREATE INDEX IF NOT EXISTS idx_affiliate_companies_tier 
ON public.affiliate_companies(tier);

-- Update existing affiliates to have Standard tier
UPDATE public.affiliate_companies 
SET tier = 'Standard' 
WHERE tier IS NULL;

COMMIT;
