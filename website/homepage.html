<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transflow | Streamline Your Event Transportation</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="platform-features.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="planner-section.css">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    
    <!-- Global logo size for mobile -->
    <style>
        @media (max-width: 768px) {
            .logo-image {
                width: 150px !important; /* 75% of original 200px size on mobile */
                height: auto !important;
            }
            
            .footer-logo-image {
                width: 112.5px !important; /* 75% of original 150px size on mobile */
                height: auto !important;
            }
        }
        
        /* Remove desktop logo size restrictions */
        .logo-image, .footer-logo-image {
            height: auto !important;
        }
    </style>
    
    <!-- Important overrides for planner section -->
    <style>
        /* Two column layout for planner section */
        .planner-section-unique {
            padding: 4rem 1rem !important;
            background: #ffffff !important;
        }
        
        .planner-section-unique .planner-container-unique {
            max-width: 1200px !important;
            margin: 0 auto !important;
            padding: 0 !important;
        }
        
        .planner-section-unique .two-column-layout {
            display: flex !important;
            flex-direction: row !important;
            align-items: flex-start !important;
            gap: 2rem !important;
        }
        
        /* Left column with information */
        .planner-section-unique .planner-info-column {
            flex: 1 !important;
            width: 50% !important;
        }
        
        /* Right column with calculator */
        .planner-section-unique .planner-calculator-column {
            flex: 1 !important;
            width: 50% !important;
        }
        
        /* Feature list styling */
        .planner-section-unique .planner-features-list {
            display: flex !important;
            flex-direction: column !important;
            gap: 1.5rem !important;
            margin-top: 2rem !important;
        }
        
        /* Feature item styling */
        .planner-section-unique .planner-feature-item {
            display: flex !important;
            flex-direction: row !important;
            width: 100% !important;
            background: white !important;
            border-radius: 8px !important;
            padding: 1rem !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05) !important;
        }
        
        /* Icon styling */
        .planner-section-unique .feature-icon {
            flex: 0 0 auto !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            width: 40px !important;
            height: 40px !important;
            margin-right: 1rem !important;
            color: #4a6cf7 !important;
            font-size: 16px !important;
        }
        
        /* Feature text styling */
        .planner-section-unique .feature-text {
            flex: 1 !important;
        }
        
        .planner-section-unique .feature-text h3 {
            margin: 0 0 0.5rem 0 !important;
            font-size: 1.1rem !important;
            font-weight: 600 !important;
            color: #333 !important;
        }
        
        .planner-section-unique .feature-text p {
            margin: 0 !important;
            font-size: 0.95rem !important;
            line-height: 1.5 !important;
            color: #666 !important;
        }
        
        /* IMPORTANT: Direct styling for calculator section to match screenshot exactly */
        .savings-calculator {
            background-color: #f0f4f8 !important; /* Exact gray from screenshot */
            border-radius: 8px !important;
            padding: 1.75rem !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
            height: 100% !important;
            display: flex !important;
            flex-direction: column !important;
        }
        
        /* Additional calculator styling to ensure background color is applied */
        .planner-calculator-column .savings-calculator {
            background-color: #f0f4f8 !important;
            border: 1px solid rgba(0, 0, 0, 0.05) !important;
        }
        
        .savings-calculator h3 {
            font-size: 1.4rem !important;
            font-weight: 600 !important;
            color: #333333 !important;
            text-align: center !important;
            margin-bottom: 0.75rem !important;
        }
        
        .savings-calculator .calculator-description {
            text-align: center !important;
            color: #666666 !important;
            margin-bottom: 1.5rem !important;
            font-size: 0.95rem !important;
        }
        
        .savings-calculator .calculator-input {
            background: #ffffff !important;
            border-radius: 8px !important;
            padding: 0.85rem !important;
            margin-bottom: 0.75rem !important;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
        }
        
        .savings-calculator .calculator-input label {
            font-size: 0.8rem !important;
            font-weight: 600 !important;
            margin-bottom: 0.5rem !important;
            display: block !important;
        }
        
        /* Slider styling */
        .savings-calculator .range-slider input[type="range"] {
            -webkit-appearance: none !important;
            width: 100% !important;
            height: 4px !important;
            background: #e0e0e0 !important;
            outline: none !important;
            border-radius: 2px !important;
        }
        
        .savings-calculator .range-slider input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none !important;
            appearance: none !important;
            width: 18px !important;
            height: 18px !important;
            background: #4a6cf7 !important;
            border-radius: 50% !important;
            cursor: pointer !important;
        }
        
        .savings-calculator .range-slider input[type="range"]::-moz-range-thumb {
            width: 18px !important;
            height: 18px !important;
            background: #4a6cf7 !important;
            border-radius: 50% !important;
            cursor: pointer !important;
            border: none !important;
        }
        
        .savings-calculator .range-slider {
            position: relative !important;
            height: 2rem !important;
        }
        
        .savings-calculator .range-value {
            position: absolute !important;
            right: 0 !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
            font-size: 0.875rem !important;
            font-weight: 600 !important;
            color: #4a6cf7 !important;
        }
        
        .savings-calculator .savings-result {
            background-color: #ffffff !important;
            border-radius: 8px !important;
            padding: 1.5rem !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
            margin-top: 1.5rem !important;
        }
        
        .savings-calculator .savings-amount {
            font-size: 2.5rem !important;
            font-weight: 700 !important;
            color: #4a6cf7 !important;
            text-align: center !important;
            margin-bottom: 0.25rem !important;
        }
        
        .savings-calculator .savings-period {
            font-size: 0.9rem !important;
            color: #666 !important;
            text-align: center !important;
            margin-bottom: 1.25rem !important;
        }
        
        .savings-calculator .savings-details {
            display: flex !important;
            justify-content: space-between !important;
            gap: 0.75rem !important;
        }
        
        .savings-calculator .savings-metric {
            text-align: center !important;
            flex: 1 !important;
            background-color: #f8f9fa !important;
            padding: 0.75rem 0.5rem !important;
            border-radius: 6px !important;
        }
        
        .savings-calculator .metric-value {
            font-size: 1.25rem !important;
            font-weight: 600 !important;
            color: #333 !important;
            margin-bottom: 0.25rem !important;
        }
        
        .savings-calculator .metric-label {
            font-size: 0.75rem !important;
            color: #666 !important;
            line-height: 1.2 !important;
        }
        
        /* Responsive styles */
        @media (max-width: 768px) {
            .planner-section-unique .two-column-layout {
                flex-direction: column !important;
            }
            
            .planner-section-unique .planner-info-column,
            .planner-section-unique .planner-calculator-column {
                width: 100% !important;
            }
            
            .planner-section-unique .planner-calculator-column {
                margin-top: 2rem !important;
            }
        }
        
        /* Additional calculator overrides with stronger selectors */
        .savings-calculator .calculator-note {
            background-color: rgba(74, 108, 247, 0.1) !important;
            border-radius: 8px !important;
            padding: 0.75rem 1rem !important;
            margin: 0.5rem 0 1.5rem 0 !important;
            font-size: 0.85rem !important;
        }
        
        /* Styling for the moved calculation basis note */
        .savings-calculator .calculation-basis {
            background-color: rgba(74, 108, 247, 0.08) !important;
            border-radius: 8px !important;
            padding: 0.5rem 1rem !important;
            margin: 0.5rem 0 1.25rem 0 !important;
            font-size: 0.8rem !important;
            border-left: 3px solid rgba(74, 108, 247, 0.3) !important;
        }
        
        .savings-calculator .calculator-note p {
            color: #555 !important;
            margin: 0 !important;
            line-height: 1.3 !important;
        }
        
        .savings-calculator .calculator-note i {
            color: #4a6cf7 !important;
            margin-right: 0.5rem !important;
        }
        
        /* Headings and text styling */
        .planner-section-unique h2 {
            font-size: 2rem !important;
            font-weight: 700 !important;
            color: #333 !important;
            margin: 0 0 0.5rem 0 !important;
            line-height: 1.2 !important;
        }
        
        .planner-section-unique > .planner-container-unique > .planner-content > .planner-info-column > p {
            font-size: 1.1rem !important;
            color: #555 !important;
            margin: 0 0 1.5rem 0 !important;
            line-height: 1.5 !important;
        }
        
        /* Feature comparison styling */
        .feature-comparison {
            margin-bottom: 2.5rem !important;
        }
        
        .feature-comparison h2 {
            font-size: 2rem !important;
            font-weight: 700 !important;
            color: #333 !important;
            margin: 0 0 1.5rem 0 !important;
        }
        
        .feature-list-group {
            display: flex !important;
            flex-direction: column !important;
            gap: 1.5rem !important;
        }
        
        .feature-list-group h3 {
            font-size: 1.25rem !important;
            font-weight: 600 !important;
            color: #333 !important;
            margin: 0 0 0.5rem 0 !important;
            padding-bottom: 0.5rem !important;
            border-bottom: 2px solid #4a6cf7 !important;
        }
        
        .benefits-list {
            list-style: none !important;
            padding: 0 !important;
            margin: 0 !important;
        }
        
        .benefits-list li {
            position: relative !important;
            padding: 0.5rem 0 0.5rem 1.5rem !important;
            font-size: 1rem !important;
            color: #444 !important;
            border-bottom: 1px solid #f0f0f0 !important;
        }
        
        .benefits-list li:before {
            content: "•" !important;
            color: #4a6cf7 !important;
            position: absolute !important;
            left: 0.5rem !important;
            font-size: 1.2rem !important;
        }
        
        @media (max-width: 768px) {
            .feature-comparison h2 {
                font-size: 1.75rem !important;
            }
            
            .feature-list-group h3 {
                font-size: 1.15rem !important;
            }
            
            .benefits-list li {
                font-size: 0.95rem !important;
            }
        }
    </style>
    
    <!-- Mobile menu styling -->
    <style>
        /* Mobile menu button */
        .mobile-menu-btn {
            display: none;
            flex-direction: column;
            justify-content: space-between;
            width: 30px;
            height: 21px;
            background: transparent;
            border: none;
            cursor: pointer;
            padding: 0;
            z-index: 10;
        }
        
        .mobile-menu-btn span {
            display: block;
            width: 100%;
            height: 3px;
            background-color: #333;
            border-radius: 3px;
            transition: all 0.3s ease;
        }
        
        /* Mobile menu transformations when open */
        .mobile-menu-open .mobile-menu-btn span:nth-child(1) {
            transform: translateY(9px) rotate(45deg);
        }
        
        .mobile-menu-open .mobile-menu-btn span:nth-child(2) {
            opacity: 0;
        }
        
        .mobile-menu-open .mobile-menu-btn span:nth-child(3) {
            transform: translateY(-9px) rotate(-45deg);
        }
        
        /* Mobile menu specific styles */
        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
            }
            
            .nav-links {
                position: fixed;
                top: 70px;
                right: -100%;
                width: 80%;
                max-width: 300px;
                height: calc(100vh - 70px);
                background-color: #fff;
                flex-direction: column;
                align-items: flex-start;
                padding: 20px;
                box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
                transition: right 0.3s ease;
                z-index: 100;
            }
            
            .mobile-menu-open .nav-links {
                right: 0;
            }
            
            .nav-links a {
                margin: 10px 0;
                width: 100%;
            }
            
            .nav-links a.btn {
                margin-top: 10px;
                width: 100%;
                text-align: center;
            }
        }
    </style>
    
    <script src="scripts.js" defer></script>
    <!-- Meta tags, CSS, etc. would go here -->
</head>
<body>
    <!-- Navigation -->
    <header>
    <nav>
        <div class="logo">
            <a href="homepage.html">
                <img src="wwms3-600x600.jpeg" alt="Transflow Logo" class="logo-image">
            </a>
        </div>
        <div class="nav-links">
            <a href="#service-options">Service Options</a>
            <a href="pricing.html">Pricing</a>
            <a href="#affiliates">For Affiliates</a>
            <a href="#contact">Contact</a>
            <a href="/login" class="btn btn-secondary">Login</a>
            <a href="/signup" class="btn btn-primary">Get Started</a>
        </div>
        <button class="mobile-menu-btn">
            <span></span>
            <span></span>
            <span></span>
        </button>
    </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>Transportation Management, Your Way</h1>
                <h2>From shared SaaS to white-label solutions - TransFlow adapts to your business model</h2>
                <p>Three-tier architecture supporting everything from corporate travel to multi-brand networks</p>
                <div class="cta-buttons">
                    <a href="/demo" class="btn btn-primary">Schedule Demo</a>
                    <a href="/signup" class="btn btn-secondary">Start Free Trial</a>
                </div>
            </div>
            <div class="hero-image">
                <img src="images/App-Demo/Gods-View.jpg" alt="Transflow Management Dashboard">
            </div>
        </div>
    </section>

    <!-- Key Benefits -->
    <section class="benefits">
        <div class="container">
            <div class="benefit-card">
                <div class="icon"><i class="fas fa-check-circle"></i></div>
                <h3>Access Vetted Providers</h3>
                <p>Connect with our growing network of professional transportation providers</p>
            </div>
            <div class="benefit-card">
                <div class="icon"><i class="fas fa-calendar-check"></i></div>
                <h3>Streamline Bookings</h3>
                <p>Manage all transportation in one platform with real-time updates</p>
            </div>
            <div class="benefit-card">
                <div class="icon"><i class="fas fa-dollar-sign"></i></div>
                <h3>Save on Costs</h3>
                <p>Competitive rates and transparent pricing with no hidden fees</p>
            </div>
            <div class="benefit-card">
                <div class="icon"><i class="fas fa-sliders-h"></i></div>
                <h3>Stay in Control</h3>
                <p>Maintain oversight while automating repetitive tasks</p>
            </div>
        </div>
    </section>

    <!-- Platform Features -->
    <section class="platform-feature">
        <div class="container">
            <div class="feature-content">
                <h2>Effortless Quote Management</h2>
                <p>Handle transportation quotes with ease and efficiency:</p>
                <ul>
                    <li>Compare multiple vendor quotes side by side</li>
                    <li>View detailed service and pricing breakdowns</li>
                    <li>Track approval workflows with complete audit trails</li>
                    <li>Manage VIP transportation requests with priority handling</li>
                </ul>
            </div>
            <div class="feature-image">
                <img src="images/App-Demo/Quote-Accept-Reject.jpg" alt="Quote Management Interface" class="screenshot">
                <p class="image-caption">Accept or reject transportation quotes with a single click</p>
            </div>
        </div>
    </section>

    <section class="platform-feature alternate">
        <div class="container">
            <div class="feature-image">
                <img src="images/App-Demo/Send-Quote-to-Providers.jpg" alt="Send Quote to Providers Interface" class="screenshot">
                <p class="image-caption">Send quote requests to multiple transportation providers simultaneously</p>
            </div>
            <div class="feature-content">
                <h2>Smart Provider Selection</h2>
                <p>Streamline your provider selection process:</p>
                <ul>
                    <li>Send requests to multiple vetted providers simultaneously</li>
                    <li>Filter providers by location, vehicle type, and availability</li>
                    <li>Compare response times and on-time performance</li>
                    <li>Make informed decisions based on provider ratings and history</li>
                </ul>
            </div>
        </div>
    </section>

    <section class="platform-feature">
        <div class="container">
            <div class="feature-content full-width">
                <h2>Real-Time Trip Tracking</h2>
                <p>Monitor your fleet with precision:</p>
                <ul>
                    <li>Track vehicle locations in real-time</li>
                    <li>Receive instant ETA updates and delay notifications</li>
                    <li>Monitor traffic conditions and route changes</li>
                    <li>Access driver and passenger information instantly</li>
                </ul>
            </div>
            <div class="feature-image contained-width">
                <img src="images/App-Demo/Trip-Tracking.jpg" alt="Live Trip Tracking Interface" class="screenshot">
                <p class="image-caption">Monitor vehicle locations, ETAs, and passenger status in real-time</p>
            </div>
        </div>
    </section>

    <section class="platform-feature alternate">
        <div class="container">
            <div class="feature-image">
                <img src="images/App-Demo/Gods-View.jpg" alt="Trip Overview Dashboard" class="screenshot">
                <p class="image-caption">Get a comprehensive view of all active trips across your organization</p>
            </div>
            <div class="feature-content">
                <h2>Complete Trip Overview</h2>
                <p>Monitor all your event transportation in real-time:</p>
                <ul>
                    <li>Track all passenger movements on a single dashboard</li>
                    <li>Monitor trip status and ETA updates</li>
                    <li>Get instant alerts for any schedule changes</li>
                    <li>Access passenger manifests and trip details</li>
                </ul>
            </div>
        </div>
    </section>

    <section class="platform-feature">
        <div class="container">
            <div class="feature-content">
                <h2>Personalized Dashboard Experience</h2>
                <p>Stay organized with your customized transportation hub:</p>
                <ul>
                    <li>View all upcoming trips and recent quotes at a glance</li>
                    <li>Track active transportation requests and their status</li>
                    <li>Access quick actions for booking and event creation</li>
                    <li>Monitor time-sensitive transportation needs</li>
                </ul>
            </div>
            <div class="feature-image">
                <img src="images/App-Demo/Customer-Dashboard.jpg" alt="Customer Dashboard Interface" class="screenshot">
                <p class="image-caption">Keep track of all your transportation activities from a centralized dashboard</p>
            </div>
        </div>
    </section>

    <section class="platform-feature alternate">
        <div class="container">
            <div class="feature-image">
                <img src="images/App-Demo/Create-Event-Form.jpg" alt="Event Creation Interface" class="screenshot">
                <p class="image-caption">Create and manage events with a user-friendly interface</p>
            </div>
            <div class="feature-content">
                <h2>Streamlined Event Creation</h2>
                <p>Create and manage events with ease:</p>
                <ul>
                    <li>Intuitive form for quick event setup</li>
                    <li>Add and manage passenger lists efficiently</li>
                    <li>Assign event coordinators and staff</li>
                    <li>Upload supporting documents and schedules</li>
                </ul>
            </div>
        </div>
    </section>

    <!-- Service Options Section -->
    <section class="service-options" id="service-options">
        <div class="container">
            <h2>Choose How You Manage Transportation</h2>
            <p>Transflow offers flexible options to match your needs and resources</p>
            
            <div class="options-grid">
                <div class="option-card">
                    <div class="option-header">
                        <h3>Self-Managed Platform</h3>
                        <p class="tagline">You're in control</p>
                    </div>
                    <div class="option-image">
                        <img src="images/Transport-Saas-solution.jpg" alt="Transflow Dashboard Interface">
                    </div>
                    <div class="option-body">
                        <p>Use our powerful platform to manage your own transportation needs</p>
                        <ul>
                            <li>Access our network of vetted providers</li>
                            <li>Manage bookings yourself</li>
                            <li>Pay only for the bookings you need</li>
                            <li>Subscription-based pricing</li>
                            <li>Complete visibility and control</li>
                        </ul>
                        <a href="pricing.html" class="btn btn-primary">View Platform Plans</a>
                    </div>
                </div>
                
                <div class="option-card featured">
                    <div class="option-header">
                        <h3>Managed Service</h3>
                        <p class="tagline">We handle everything</p>
                    </div>
                    <div class="option-image">
                        <img src="images/hybrid-solution-manager.jpg" alt="Transflow Managed Services Interface">
                    </div>
                    <div class="option-body">
                        <p>Let our experts manage your transportation needs end-to-end</p>
                        <ul>
                            <li>Dedicated transportation coordinators</li>
                            <li>We handle all bookings and management</li>
                            <li>Premium service with 24/7 support</li>
                            <li>20% service fee on managed bookings</li>
                            <li>Focus on your event while we handle logistics</li>
                        </ul>
                        <a href="/managed-service" class="btn btn-secondary">Learn About Managed Service</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials">
        <div class="container">
            <h2>Real Results from Real Clients</h2>
            <p>See how Transflow has transformed transportation management for businesses across different industries</p>
            
            <div class="testimonial-slider">
                <div class="testimonial-track">
                    <!-- Corporate Event Planner -->
                    <div class="testimonial-card">
                        <div class="testimonial-header">
                            <div class="testimonial-image">
                                <img src="https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=987&q=80" alt="Sarah Johnson">
                            </div>
                            <div class="testimonial-meta">
                                <div class="testimonial-name">Sarah Johnson</div>
                                <div class="testimonial-role">Senior Event Manager</div>
                                <div class="testimonial-company">Global Events Inc.</div>
                            </div>
                        </div>
                        <div class="testimonial-content">
                            <div class="pain-point">Challenge: Managing transportation for 500+ attendees across multiple venues</div>
                            <div class="testimonial-quote">Transflow transformed our event logistics. We reduced coordination time by 70% and saved 20% on transportation costs. The real-time tracking feature was a game-changer for our team.</div>
                            <div class="testimonial-result">
                                <i class="fas fa-chart-line"></i>
                                Managed 50+ events seamlessly in the first year
                            </div>
                        </div>
                    </div>

                    <!-- Travel Agency -->
                    <div class="testimonial-card">
                        <div class="testimonial-header">
                            <div class="testimonial-image">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=987&q=80" alt="Michael Chen">
                            </div>
                            <div class="testimonial-meta">
                                <div class="testimonial-name">Michael Chen</div>
                                <div class="testimonial-role">Operations Director</div>
                                <div class="testimonial-company">Elite Travel Solutions</div>
                            </div>
                        </div>
                        <div class="testimonial-content">
                            <div class="pain-point">Challenge: Time-consuming quote comparisons and manual bookings</div>
                            <div class="testimonial-quote">The automated quote comparison saved us countless hours. We're now handling 3x more bookings with the same team size, and our clients love the transparent pricing.</div>
                            <div class="testimonial-result">
                                <i class="fas fa-dollar-sign"></i>
                                15% average cost savings on bookings
                            </div>
                        </div>
                    </div>

                    <!-- Corporate Travel Manager -->
                    <div class="testimonial-card">
                        <div class="testimonial-header">
                            <div class="testimonial-image">
                                <img src="https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=987&q=80" alt="Emma Taylor">
                            </div>
                            <div class="testimonial-meta">
                                <div class="testimonial-name">Emma Taylor</div>
                                <div class="testimonial-role">Travel Program Manager</div>
                                <div class="testimonial-company">Tech Innovations Ltd</div>
                            </div>
                        </div>
                        <div class="testimonial-content">
                            <div class="pain-point">Challenge: Lack of visibility into transportation spend and compliance</div>
                            <div class="testimonial-quote">The reporting features helped us identify cost-saving opportunities we never knew existed. Our compliance rate increased to 95%, and we've reduced our carbon footprint significantly.</div>
                            <div class="testimonial-result">
                                <i class="fas fa-check-circle"></i>
                                Achieved 30% reduction in transport costs
                            </div>
                        </div>
                    </div>

                    <!-- Hotel Concierge -->
                    <div class="testimonial-card">
                        <div class="testimonial-header">
                            <div class="testimonial-image">
                                <img src="https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=987&q=80" alt="James Martinez">
                            </div>
                            <div class="testimonial-meta">
                                <div class="testimonial-name">James Martinez</div>
                                <div class="testimonial-role">Head Concierge</div>
                                <div class="testimonial-company">Luxury Resort & Spa</div>
                            </div>
                        </div>
                        <div class="testimonial-content">
                            <div class="pain-point">Challenge: Managing VIP guest transportation requests efficiently</div>
                            <div class="testimonial-quote">The instant booking and real-time tracking have elevated our guest experience. We can now handle last-minute changes effortlessly and provide accurate ETAs to our guests.</div>
                            <div class="testimonial-result">
                                <i class="fas fa-star"></i>
                                Guest satisfaction increased by 40%
                            </div>
                        </div>
                    </div>
                </div>
                <div class="slider-controls"></div>
            </div>
        </div>
    </section>

    <!-- For Travel Planners Section -->
    <section class="for-planners planner-section-unique">
        <div class="container planner-container-unique">
            <div class="planner-content two-column-layout">
                <!-- Left Column: Title and Features -->
                <div class="planner-info-column">
                    <h2>For Travel Planners & Event Managers</h2>
                    <p>Transflow gives you the tools to manage transportation for events of any size:</p>
                    
                    <div class="planner-features-list">
                        <div class="planner-feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="feature-text">
                                <h3>Compare & Save</h3>
                                <p>Compare quotes from multiple providers and save up to 20% on transportation costs</p>
                            </div>
                        </div>
                        
                        <div class="planner-feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="feature-text">
                                <h3>Real-time Management</h3>
                                <p>Book and manage transportation in real-time with instant confirmations</p>
                            </div>
                        </div>
                    
                        <div class="planner-feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="feature-text">
                                <h3>Live Tracking</h3>
                                <p>Track vehicles and monitor schedules with real-time GPS updates</p>
                            </div>
                        </div>
                        
                        <div class="planner-feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="feature-text">
                                <h3>Detailed Analytics</h3>
                                <p>Access comprehensive reporting and analytics for better decision making</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Right Column: Calculator -->
                <div class="planner-calculator-column">
                    <!-- Calculator Section -->
                    <div class="savings-calculator">
                        <h3>Calculate Your Potential Savings</h3>
                        <p class="calculator-description">See how much time and money you could save with Transflow</p>
                        
                        <!-- Time savings explanation moved here -->
                        <div class="calculator-note calculation-basis">
                            <p><i class="fas fa-info-circle"></i> Time savings based on an average of 1.5 hours spent per trip on searching providers, negotiations, and bookings.</p>
                        </div>
                        
                        <div class="calculator-inputs">
                            <div class="calculator-input">
                                <label>MONTHLY TRIPS</label>
                                <div class="range-slider">
                                    <input type="range" min="10" max="100" value="50" id="tripCount">
                                    <div class="range-value" id="tripCountValue">50</div>
                                </div>
                            </div>
                            
                            <div class="calculator-input">
                                <label>AVERAGE TRIP COST ($)</label>
                                <div class="range-slider">
                                    <input type="range" min="50" max="500" value="200" id="avgTripCost">
                                    <div class="range-value" id="avgTripCostValue">$200</div>
                                </div>
                            </div>
                        </div>

                        <div class="savings-result">
                            <div class="savings-amount">$1,500</div>
                            <div class="savings-period">Estimated Monthly Savings</div>
                            
                            <div class="savings-details">
                                <div class="savings-metric">
                                    <div class="metric-value" id="timeMetric">50h</div>
                                    <div class="metric-label">Time Saved Monthly</div>
                                </div>
                                <div class="savings-metric">
                                    <div class="metric-value" id="costMetric">$1,500</div>
                                    <div class="metric-label">Cost Reduction</div>
                                </div>
                                <div class="savings-metric">
                                    <div class="metric-value" id="efficiencyMetric">67%</div>
                                    <div class="metric-label">Efficiency Gain</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- For Affiliates -->
    <section class="for-affiliates" id="affiliates">
        <div class="container">
            <div class="image">
                <img src="images/airport-pickup-chauffeur.jpg" alt="Transportation provider with vehicle fleet">
            </div>
            <div class="text-content">
                <h2>For Transportation Providers</h2>
                <p>Join our network and grow your business with high-value enterprise clients:</p>
                <ul>
                    <li>Join for free, pay only $1 per confirmed booking</li>
                    <li>Set your own rates, availability, and service area</li>
                    <li>Manage your fleet and driver assignments</li>
                    <li>Access enterprise-level clients</li>
                    <li>Get paid quickly and securely</li>
                </ul>
                <a href="/affiliates" class="btn btn-primary">Become an Affiliate</a>
            </div>
        </div>
    </section>

    <!-- CTA -->
    <section class="cta">
        <div class="container">
            <h2>Ready to transform your transportation management?</h2>
            <p>Join thousands of event professionals using Transflow</p>
            <a href="/signup" class="btn btn-primary">Start Your Free Trial</a>
            <p class="small">No credit card required • 14-day free trial</p>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-section">
                    <div class="footer-logo">
                        <img src="wwms3-600x600.jpeg" alt="transflow Logo" class="footer-logo-image">
                    </div>
                    <p>Streamlining event transportation management for planners and providers worldwide.</p>
                    <div class="social-links">
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Platform</h4>
                    <ul>
                        <li><a href="#features">Features</a></li>
                        <li><a href="#service-options">Service Options</a></li>
                        <li><a href="#pricing">Pricing</a></li>
                        <li><a href="/security">Security</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>For Businesses</h4>
                    <ul>
                        <li><a href="/event-planners">Event Planners</a></li>
                        <li><a href="/affiliates">Transportation Providers</a></li>
                        <li><a href="/enterprise">Enterprise</a></li>
                        <li><a href="/case-studies">Case Studies</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Company</h4>
                    <ul>
                        <li><a href="/about">About Us</a></li>
                        <li><a href="/careers">Careers</a></li>
                        <li><a href="/blog">Blog</a></li>
                        <li><a href="/contact">Contact</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="copyright">© 2023 transflow. All rights reserved.</div>
                <div class="legal-links">
                    <a href="/terms">Terms of Service</a>
                    <a href="/privacy">Privacy Policy</a>
                    <a href="/cookies">Cookie Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Simple Image Modal -->
    <div id="imageModal" class="modal-overlay" style="display: none; position: fixed; z-index: 9999; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.9); padding: 40px; box-sizing: border-box;">
        <button id="closeModal" style="position: absolute; top: 10px; right: 20px; font-size: 30px; color: white; background: none; border: none; cursor: pointer; z-index: 10000;">&times;</button>
        <div style="position: relative; max-width: 80%; max-height: 80vh; margin: auto;">
            <img id="modalImage" style="display: block; max-width: 100%; max-height: 80vh; margin: 0 auto; object-fit: contain; border-radius: 4px; box-shadow: 0 2px 10px rgba(0,0,0,0.3);" src="" alt="Image preview">
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu functionality
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
            const nav = document.querySelector('nav');
            
            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Toggle mobile menu open class
                    nav.classList.toggle('mobile-menu-open');
                    
                    // Update aria attributes
                    const isOpen = nav.classList.contains('mobile-menu-open');
                    mobileMenuBtn.setAttribute('aria-expanded', isOpen);
                    
                    console.log("Mobile menu button clicked, nav class:", nav.className);
                });
                
                // Close mobile menu when clicking outside
                document.addEventListener('click', function(event) {
                    if (nav.classList.contains('mobile-menu-open') && 
                        !event.target.closest('nav') && 
                        !event.target.closest('.mobile-menu-btn')) {
                        nav.classList.remove('mobile-menu-open');
                        mobileMenuBtn.setAttribute('aria-expanded', 'false');
                    }
                });
            }
            
            // Get modal elements
            var modal = document.getElementById('imageModal');
            var modalImg = document.getElementById('modalImage');
            var closeBtn = document.getElementById('closeModal');
            
            // Find all images that should trigger modal
            var images = document.querySelectorAll('.feature-image img');
            
            // Add click handlers to images
            images.forEach(function(img) {
                img.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Set image source
                    modalImg.src = this.src;
                    
                    // Show modal when explicitly clicked
                    requestAnimationFrame(function() {
                        modal.style.display = 'flex';
                        document.body.style.overflow = 'hidden';
                    });
                });
            });
            
            // Close button
            closeBtn.addEventListener('click', function() {
                modal.style.display = 'none';
                
                // Clear image source after a short delay
                setTimeout(function() {
                    modalImg.src = '';
                }, 100);
                
                // Enable scrolling
                document.body.style.overflow = '';
            });
            
            // Close when clicking outside
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.style.display = 'none';
                    
                    // Clear image source after a short delay
                    setTimeout(function() {
                        modalImg.src = '';
                    }, 100);
                    
                    // Enable scrolling
                    document.body.style.overflow = '';
                }
            });
            
            // Close with ESC key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && modal.style.display === 'flex') {
                    modal.style.display = 'none';
                    
                    // Clear image source after a short delay
                    setTimeout(function() {
                        modalImg.src = '';
                    }, 100);
                    
                    // Enable scrolling
                    document.body.style.overflow = '';
                }
            });
        });

        // Force calculator initialization after DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                if (typeof initSavingsCalculator === 'function') {
                    console.log("Re-initializing calculator to ensure results are displayed");
                    initSavingsCalculator();
                    
                    // Force update the metrics
                    const calculator = document.querySelector('.savings-calculator');
                    if (calculator) {
                        const timeMetric = calculator.querySelector('#timeMetric');
                        const costMetric = calculator.querySelector('#costMetric');
                        const efficiencyMetric = calculator.querySelector('#efficiencyMetric');
                        
                        if (timeMetric && costMetric && efficiencyMetric) {
                            // Default values for 50 trips at $200 per trip
                            timeMetric.textContent = '50h';
                            costMetric.textContent = '$1,500';
                            efficiencyMetric.textContent = '67%';
                        }
                    }
                }
            }, 500); // Wait for 500ms to ensure everything is loaded
        });
    </script>

    <script src="scripts.js"></script>
</body>
</html>
