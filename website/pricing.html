<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>transflow Pricing | Simple, Transparent Pricing Plans</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <style>
        .logo-image {
            width: 150px !important; /* 75% of original 200px size */
            height: auto !important;
        }
        
        .footer-logo-image {
            width: 112.5px !important; /* 75% of original 150px size */
            height: auto !important;
        }
        
        @media (max-width: 768px) {
            .logo-image {
                width: 150px !important; /* 75% of original 200px size on mobile */
                height: auto !important;
            }
            
            .footer-logo-image {
                width: 112.5px !important; /* 75% of original 150px size on mobile */
                height: auto !important;
            }
        }
        
        /* Remove desktop logo size restrictions */
        .logo-image, .footer-logo-image {
            height: auto !important;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <header>
        <nav>
            <div class="logo">
                <a href="homepage.html">
                    <img src="wwms3-600x600.jpeg" alt="transflow Logo" class="logo-image">
                </a>
            </div>
            <div class="nav-links">
                <a href="homepage.html#service-options">Service Options</a>
                <a href="pricing.html">Pricing</a>
                <a href="homepage.html#affiliates">For Affiliates</a>
                <a href="homepage.html#contact">Contact</a>
                <a href="/login" class="btn btn-secondary">Login</a>
                <a href="/signup" class="btn btn-primary">Get Started</a>
            </div>
            <button class="mobile-menu-btn">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </nav>
    </header>

    <!-- Value Proposition Section -->
    <section class="value-proposition">
        <div class="container">
            <h2>Why Choose transflow?</h2>
            <p class="section-subtitle">Your All-in-One Transportation Management Solution</p>
            
            <div class="value-grid">
                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Rigorous Provider Vetting</h3>
                    <p>We continuously source and thoroughly evaluate transportation providers, assessing their fleet quality, safety records, insurance coverage, and service standards to ensure only the best join our network.</p>
                </div>

                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-file-shield"></i>
                    </div>
                    <h3>Security & Compliance</h3>
                    <p>Streamlined document verification for high-security events and VIP transport, including background checks, security clearances, and compliance with international safety standards.</p>
                </div>

                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3>Premium VIP Services</h3>
                    <p>Access to exclusive VIP services including fast-track processing, luxury fleet options, dedicated concierge support, and specialized security arrangements for high-profile clients.</p>
                </div>

                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-location-dot"></i>
                    </div>
                    <h3>Real-Time Tracking</h3>
                    <p>Advanced passenger tracking system with live updates, ETAs, and route optimization, ensuring seamless coordination and peace of mind for event organizers and clients.</p>
                </div>

                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h3>Rate Negotiation</h3>
                    <p>Our team actively negotiates competitive rates with providers, leveraging our network's volume to secure preferential pricing and exclusive benefits for our clients.</p>
                </div>

                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-users-gear"></i>
                    </div>
                    <h3>Event Coordination</h3>
                    <p>Professional on-site coordinators available for large events, managing real-time logistics, handling last-minute changes, and ensuring flawless transportation execution.</p>
                </div>

                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Smart Comparison Tools</h3>
                    <p>Advanced platform features for comparing rates, service levels, and provider performance metrics, helping you make informed decisions for every booking.</p>
                </div>

                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3>24/7 Support Access</h3>
                    <p>Round-the-clock support team available to handle urgent requests, resolve issues, and provide immediate assistance for time-critical transportation needs.</p>
                </div>
            </div>
        </div>
    </section>

    <style>
        .value-proposition {
            padding: 5rem 2rem;
            background-color: #f8f9fa;
        }

        .value-proposition h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            text-align: center;
            margin-bottom: 1rem;
        }

        .value-proposition .section-subtitle {
            font-size: 1.2rem;
            color: #666;
            text-align: center;
            max-width: 800px;
            margin: 0 auto 3rem;
        }

        .value-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .value-card {
            background: #ffffff;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .value-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .value-icon {
            width: 50px;
            height: 50px;
            background: rgba(74, 108, 247, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
        }

        .value-icon i {
            font-size: 1.5rem;
            color: #4a6cf7;
        }

        .value-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
        }

        .value-card p {
            font-size: 1rem;
            color: #666;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .value-proposition {
                padding: 3rem 1.5rem;
            }

            .value-proposition h2 {
                font-size: 2rem;
            }

            .value-proposition .section-subtitle {
                font-size: 1.1rem;
            }

            .value-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .value-card {
                padding: 1.5rem;
            }
        }
    </style>

    <!-- Pricing Hero -->
    <section class="pricing-hero">
        <div class="container">
            <h1>Flexible Solutions for Every Business Model</h1>
            <p>From shared SaaS to white-label solutions - choose the architecture that fits your needs</p>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing" id="pricing">
        <div class="container">
            <div class="pricing-grid">
                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3>Shared SaaS</h3>
                        <div class="price">$199<span>/month</span></div>
                        <p>Traditional SaaS with TransFlow branding and shared affiliate network</p>
                    </div>
                    <div class="pricing-features">
                        <ul>
                            <li>TransFlow branded platform</li>
                            <li>Up to 5 team members</li>
                            <li>Full affiliate network access</li>
                            <li>Transparent affiliate pricing</li>
                            <li>Standard reporting & analytics</li>
                            <li>Document exchange for security vetting</li>
                            <li>VIP services booking</li>
                            <li>Email support</li>
                        </ul>
                    </div>
                    <div class="pricing-action">
                        <a href="/signup?plan=shared" class="btn btn-primary">Start Free Trial</a>
                    </div>
                </div>
                
                <div class="pricing-card featured">
                    <div class="pricing-header">
                        <span class="featured-tag">Most Popular</span>
                        <h3>Multi-Brand Network</h3>
                        <div class="price">$999<span>/month</span></div>
                        <p>Operate multiple transportation brands with isolated customer bases</p>
                    </div>
                    <div class="pricing-features">
                        <ul>
                            <li>Everything in Shared SaaS, plus:</li>
                            <li>Multiple brand management (LIMO123, etc.)</li>
                            <li>Isolated customer databases per brand</li>
                            <li>Affiliate network participation choice</li>
                            <li>Customer migration & upsell tools</li>
                            <li>Cross-brand analytics</li>
                            <li>Custom commission models</li>
                            <li>Priority support</li>
                        </ul>
                    </div>
                    <div class="pricing-action">
                        <a href="/signup?plan=multi-brand" class="btn btn-primary">Start Free Trial</a>
                    </div>
                </div>
                
                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3>White-Label Solution</h3>
                        <div class="price">Custom<span>/pricing</span></div>
                        <p>Fully branded transportation platform for hotels, corporations, and enterprises</p>
                    </div>
                    <div class="pricing-features">
                        <ul>
                            <li>Everything in Multi-Brand, plus:</li>
                            <li>Complete custom branding</li>
                            <li>Custom domains (yourcompany.com)</li>
                            <li>Full data isolation</li>
                            <li>Custom workflows & integrations</li>
                            <li>Dedicated account manager</li>
                            <li>24/7 priority support</li>
                            <li>Custom development available</li>
                        </ul>
                    </div>
                    <div class="pricing-action">
                        <a href="/contact?plan=white-label" class="btn btn-primary">Contact Sales</a>
                    </div>
                </div>
                
                <div class="pricing-card calculator-card">
                    <!-- Savings Calculator -->
                    <div class="savings-calculator">
                        <h3>Calculate Your Potential Savings</h3>
                        <p class="calculator-description">See how much time and money you could save with transflow</p>
                        
                        <div class="calculator-note">
                            <p><i class="fas fa-info-circle"></i> Time savings based on an average of 1.5 hours spent per trip on searching providers, negotiations, and bookings.</p>
                        </div>
                        
                        <div class="calculator-inputs">
                            <div class="calculator-input">
                                <label>MONTHLY TRIPS</label>
                                <div class="range-slider">
                                    <input type="range" min="10" max="100" value="50" id="tripCount">
                                    <div class="range-value" id="tripCountValue">50</div>
                                </div>
                            </div>
                            
                            <div class="calculator-input">
                                <label>AVERAGE TRIP COST ($)</label>
                                <div class="range-slider">
                                    <input type="range" min="50" max="500" value="200" id="avgTripCost">
                                    <div class="range-value" id="avgTripCostValue">$200</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="savings-result">
                            <div class="savings-amount">$1,500</div>
                            <div class="savings-period">Estimated Monthly Savings</div>
                            
                            <div class="savings-details">
                                <div class="savings-metric">
                                    <div class="metric-value" id="timeMetric">50h</div>
                                    <div class="metric-label">Time Saved Monthly</div>
                                </div>
                                <div class="savings-metric">
                                    <div class="metric-value" id="costMetric">$1,500</div>
                                    <div class="metric-label">Cost Reduction</div>
                                </div>
                                <div class="savings-metric">
                                    <div class="metric-value" id="efficiencyMetric">67%</div>
                                    <div class="metric-label">Efficiency Gain</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Architecture Explanation Section -->
    <section class="architecture-explanation">
        <div class="container">
            <h2>Three-Tier Architecture Explained</h2>
            <p class="section-subtitle">Choose the level of isolation and branding that fits your business model</p>

            <div class="architecture-grid">
                <div class="architecture-card">
                    <div class="architecture-icon">
                        <i class="fas fa-share-alt"></i>
                    </div>
                    <h3>Level 1: Shared SaaS</h3>
                    <p><strong>Traditional SaaS Model</strong></p>
                    <ul>
                        <li>TransFlow branding throughout</li>
                        <li>Shared affiliate network</li>
                        <li>Transparent affiliate pricing</li>
                        <li>Organizational switching for management</li>
                        <li>Perfect for: Corporate travel departments</li>
                    </ul>
                </div>

                <div class="architecture-card">
                    <div class="architecture-icon">
                        <i class="fas fa-network-wired"></i>
                    </div>
                    <h3>Level 2: Multi-Brand Networks</h3>
                    <p><strong>Segregated Customer Bases</strong></p>
                    <ul>
                        <li>Multiple brands (LIMO123, DallasLimo)</li>
                        <li>Isolated customer databases</li>
                        <li>Affiliate network participation choice</li>
                        <li>Customer migration & upsell pathways</li>
                        <li>Perfect for: Transportation network operators</li>
                    </ul>
                </div>

                <div class="architecture-card">
                    <div class="architecture-icon">
                        <i class="fas fa-paint-brush"></i>
                    </div>
                    <h3>Level 3: White-Label</h3>
                    <p><strong>Complete Branding Customization</strong></p>
                    <ul>
                        <li>Custom domains and branding</li>
                        <li>Full data isolation</li>
                        <li>Tailored workflows</li>
                        <li>No TransFlow branding visible</li>
                        <li>Perfect for: Hotels, corporations, enterprises</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <style>
        .architecture-explanation {
            padding: 5rem 2rem;
            background-color: #f8f9fa;
        }

        .architecture-explanation h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            text-align: center;
            margin-bottom: 1rem;
        }

        .architecture-explanation .section-subtitle {
            font-size: 1.2rem;
            color: #666;
            text-align: center;
            max-width: 800px;
            margin: 0 auto 3rem;
        }

        .architecture-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .architecture-card {
            background: #ffffff;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-align: center;
        }

        .architecture-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .architecture-icon {
            width: 60px;
            height: 60px;
            background: rgba(74, 108, 247, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
        }

        .architecture-icon i {
            font-size: 1.8rem;
            color: #4a6cf7;
        }

        .architecture-card h3 {
            font-size: 1.4rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .architecture-card p {
            font-size: 1rem;
            color: #4a6cf7;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .architecture-card ul {
            list-style: none;
            padding: 0;
            text-align: left;
        }

        .architecture-card li {
            padding: 0.5rem 0;
            color: #666;
            position: relative;
            padding-left: 1.5rem;
        }

        .architecture-card li:before {
            content: "•";
            color: #4a6cf7;
            position: absolute;
            left: 0;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .architecture-explanation {
                padding: 3rem 1.5rem;
            }

            .architecture-explanation h2 {
                font-size: 2rem;
            }

            .architecture-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .architecture-card {
                padding: 1.5rem;
            }
        }
    </style>

    <!-- New Comprehensive Feature Comparison -->
    <section class="comprehensive-features">
        <div class="container">
            <h2>All Plan Features</h2>
            <p class="section-description">Compare all features across our different pricing tiers</p>

            <div class="feature-comparison-card">
                <div class="feature-comparison-header">
                    <h3>Platform Access</h3>
                </div>
                <div class="feature-comparison-grid">
                    <div class="feature-row header-row">
                        <div class="feature-name"></div>
                        <div class="plan-header essential-header">Essential</div>
                        <div class="plan-header professional-header">Professional</div>
                        <div class="plan-header business-header">Business</div>
                        <div class="plan-header enterprise-header">Enterprise</div>
                    </div>
                    <div class="feature-row">
                        <div class="feature-name">Team members</div>
                        <div class="feature-essential">2 users</div>
                        <div class="feature-professional">5 users</div>
                        <div class="feature-business">10 users</div>
                        <div class="feature-enterprise"><i class="fas fa-check"></i> Unlimited team members</div>
                    </div>
                    <div class="feature-row">
                        <div class="feature-name">Booking management</div>
                        <div class="feature-essential">Basic</div>
                        <div class="feature-professional">Advanced</div>
                        <div class="feature-business">Advanced</div>
                        <div class="feature-enterprise"><i class="fas fa-check"></i> Enterprise-grade</div>
                    </div>
                    <div class="feature-row">
                        <div class="feature-name">Reporting & analytics</div>
                        <div class="feature-essential">Standard</div>
                        <div class="feature-professional">Advanced</div>
                        <div class="feature-business">Custom dashboard</div>
                        <div class="feature-enterprise"><i class="fas fa-check"></i> Custom reporting suite</div>
                    </div>
                    <div class="feature-row">
                        <div class="feature-name">Document exchange</div>
                        <div class="feature-essential"><i class="fas fa-check"></i></div>
                        <div class="feature-professional"><i class="fas fa-check"></i></div>
                        <div class="feature-business"><i class="fas fa-check"></i></div>
                        <div class="feature-enterprise"><i class="fas fa-check"></i> Advanced security</div>
                    </div>
                </div>
            </div>

            <div class="feature-comparison-card">
                <div class="feature-comparison-header">
                    <h3>Network Benefits</h3>
                </div>
                <div class="feature-comparison-grid">
                    <div class="feature-row header-row">
                        <div class="feature-name"></div>
                        <div class="plan-header essential-header">Essential</div>
                        <div class="plan-header professional-header">Professional</div>
                        <div class="plan-header business-header">Business</div>
                        <div class="plan-header enterprise-header">Enterprise</div>
                    </div>
                    <div class="feature-row">
                        <div class="feature-name">Affiliate network access</div>
                        <div class="feature-essential"><i class="fas fa-check"></i></div>
                        <div class="feature-professional"><i class="fas fa-check"></i></div>
                        <div class="feature-business"><i class="fas fa-check"></i></div>
                        <div class="feature-enterprise"><i class="fas fa-check"></i> Global fleet management</div>
                    </div>
                    <div class="feature-row">
                        <div class="feature-name">Negotiated rates</div>
                        <div class="feature-essential">Standard</div>
                        <div class="feature-professional">Enhanced</div>
                        <div class="feature-business">Premium</div>
                        <div class="feature-enterprise"><i class="fas fa-check"></i> Custom rates</div>
                    </div>
                    <div class="feature-row">
                        <div class="feature-name">VIP services booking</div>
                        <div class="feature-essential"><i class="fas fa-check"></i></div>
                        <div class="feature-professional"><i class="fas fa-check"></i></div>
                        <div class="feature-business">Priority</div>
                        <div class="feature-enterprise"><i class="fas fa-check"></i> Full white-label solution</div>
                    </div>
                    <div class="feature-row">
                        <div class="feature-name">Volume incentives</div>
                        <div class="feature-essential">—</div>
                        <div class="feature-professional">Basic</div>
                        <div class="feature-business"><i class="fas fa-check"></i></div>
                        <div class="feature-enterprise"><i class="fas fa-check"></i> Event-specific solutions</div>
                    </div>
                </div>
            </div>

            <div class="feature-comparison-card">
                <div class="feature-comparison-header">
                    <h3>Support & Integration</h3>
                </div>
                <div class="feature-comparison-grid">
                    <div class="feature-row header-row">
                        <div class="feature-name"></div>
                        <div class="plan-header essential-header">Essential</div>
                        <div class="plan-header professional-header">Professional</div>
                        <div class="plan-header business-header">Business</div>
                        <div class="plan-header enterprise-header">Enterprise</div>
                    </div>
                    <div class="feature-row">
                        <div class="feature-name">Support level</div>
                        <div class="feature-essential">Email</div>
                        <div class="feature-professional">Priority</div>
                        <div class="feature-business">24/7 + Account Manager</div>
                        <div class="feature-enterprise"><i class="fas fa-check"></i> Dedicated support team</div>
                    </div>
                    <div class="feature-row">
                        <div class="feature-name">API access</div>
                        <div class="feature-essential">—</div>
                        <div class="feature-professional">Basic</div>
                        <div class="feature-business">Advanced</div>
                        <div class="feature-enterprise"><i class="fas fa-check"></i> Custom integrations</div>
                    </div>
                    <div class="feature-row">
                        <div class="feature-name">White-label options</div>
                        <div class="feature-essential">—</div>
                        <div class="feature-professional">—</div>
                        <div class="feature-business">Basic</div>
                        <div class="feature-enterprise"><i class="fas fa-check"></i> Full white-label solution</div>
                    </div>
                    <div class="feature-row">
                        <div class="feature-name">Custom development</div>
                        <div class="feature-essential">—</div>
                        <div class="feature-professional">—</div>
                        <div class="feature-business">Limited</div>
                        <div class="feature-enterprise"><i class="fas fa-check"></i> Custom development</div>
                    </div>
                </div>
            </div>

            <div class="feature-comparison-card">
                <div class="feature-comparison-header">
                    <h3>Security & Compliance</h3>
                </div>
                <div class="feature-comparison-grid">
                    <div class="feature-row header-row">
                        <div class="feature-name"></div>
                        <div class="plan-header essential-header">Essential</div>
                        <div class="plan-header professional-header">Professional</div>
                        <div class="plan-header business-header">Business</div>
                        <div class="plan-header enterprise-header">Enterprise</div>
                    </div>
                    <div class="feature-row">
                        <div class="feature-name">Data encryption</div>
                        <div class="feature-essential"><i class="fas fa-check"></i></div>
                        <div class="feature-professional"><i class="fas fa-check"></i></div>
                        <div class="feature-business"><i class="fas fa-check"></i></div>
                        <div class="feature-enterprise"><i class="fas fa-check"></i> Advanced security features</div>
                    </div>
                    <div class="feature-row">
                        <div class="feature-name">SLA guarantees</div>
                        <div class="feature-essential">—</div>
                        <div class="feature-professional">—</div>
                        <div class="feature-business">Basic</div>
                        <div class="feature-enterprise"><i class="fas fa-check"></i> SLA guarantees</div>
                    </div>
                    <div class="feature-row">
                        <div class="feature-name">Deployment options</div>
                        <div class="feature-essential">Cloud</div>
                        <div class="feature-professional">Cloud</div>
                        <div class="feature-business">Cloud</div>
                        <div class="feature-enterprise"><i class="fas fa-check"></i> On-premise deployment option</div>
                    </div>
                </div>
            </div>

            <div class="cta-container">
                <a href="/contact" class="btn btn-primary">Contact Sales for Custom Pricing</a>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq">
        <div class="container">
            <h2>Frequently Asked Questions</h2>
            <div class="faq-grid">
                <!-- Add FAQ items here -->
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What is the transflow?</h3>
                        <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                    </div>
                    <div class="faq-answer">
                        <p>transflow is a transportation management system that connects event planners and travel managers with vetted transportation providers. Our platform streamlines the process of requesting quotes, booking services, and managing transportation for events of any size.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>How does pricing work?</h3>
                        <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                    </div>
                    <div class="faq-answer">
                        <p>We offer subscription-based pricing with three main tiers: Essential ($199/month), Professional ($499/month), and Business ($999/month). Each tier includes different features and user limits. For larger organizations, we also offer custom Enterprise pricing. All plans include a free 14-day trial, no credit card required.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What's the difference between Self-Managed and Managed Service?</h3>
                        <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                    </div>
                    <div class="faq-answer">
                        <p>With Self-Managed, you use our platform to handle your transportation needs yourself, paying only a subscription fee. With Managed Service, our team handles everything for you, from bookings to logistics coordination, charging a 20% service fee on managed bookings. You can choose the option that best fits your resources and needs.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>How do I join as a transportation provider?</h3>
                        <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                    </div>
                    <div class="faq-answer">
                        <p>Transportation providers can join our affiliate network for free and pay only $1 per confirmed booking. You set your own rates, availability, and service area while gaining access to enterprise-level clients. Visit our Affiliates page to learn more and sign up.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>How much time and money can I save with transflow?</h3>
                        <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                    </div>
                    <div class="faq-answer">
                        <p>Our clients typically save up to 20% on transportation costs and reduce coordination time by up to 70%. The exact savings depend on your current volume and processes. Contact our sales team for a personalized assessment of potential savings for your business.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>Is there a contract or commitment required?</h3>
                        <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                    </div>
                    <div class="faq-answer">
                        <p>No long-term contracts are required. All our plans are billed monthly, and you can upgrade, downgrade, or cancel at any time. We're confident in our platform's value and don't believe in locking customers into contracts.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What type of support is included?</h3>
                        <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                    </div>
                    <div class="faq-answer">
                        <p>Support levels vary by plan. Essential includes email support, Professional adds priority support, Business includes 24/7 priority support and a dedicated account manager, and Enterprise comes with a dedicated support team and SLA guarantees.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>Can I white-label the platform for my business?</h3>
                        <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                    </div>
                    <div class="faq-answer">
                        <p>Yes, white-label options are available on our Business and Enterprise plans. This allows you to customize the platform with your branding, providing a seamless experience for your team and clients.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <!-- Copy footer from homepage -->
    </footer>
</body>

<!-- Add styling for FAQ section -->
<style>
    /* FAQ Section Styling */
    .faq {
        padding: 4rem 2rem;
        background-color: #f8f9fa;
    }
    
    .faq h2 {
        font-size: 2.5rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 2.5rem;
        text-align: center;
    }
    
    .faq-grid {
        max-width: 900px;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .faq-item {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }
    
    .faq-question {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.25rem 1.5rem;
        cursor: pointer;
        position: relative;
    }
    
    .faq-question h3 {
        font-size: 1.15rem;
        font-weight: 600;
        color: #333;
        margin: 0;
    }
    
    .faq-toggle {
        color: #4a6cf7;
        font-size: 1rem;
        transition: transform 0.3s ease;
    }
    
    .faq-answer {
        display: none;
        padding: 0 1.5rem 1.5rem;
    }
    
    .faq-answer p {
        margin: 0 0 1.5rem;
        padding-top: 0;
        color: #555;
        line-height: 1.6;
        font-size: 1rem;
    }
    
    /* Active state for FAQ items */
    .faq-item.active .faq-toggle {
        transform: rotate(45deg);
    }
    
    .faq-item.active .faq-answer {
        display: block;
    }
    
    @media (max-width: 768px) {
        .faq {
            padding: 3rem 1.5rem;
        }
        
        .faq h2 {
            font-size: 2rem;
            margin-bottom: 2rem;
        }
        
        .faq-question {
            padding: 1rem 1.25rem;
        }
        
        .faq-question h3 {
            font-size: 1rem;
        }
        
        .faq-answer {
            padding: 0 1.25rem;
        }
        
        .faq-answer p {
            font-size: 0.95rem;
        }
    }
</style>

<!-- Replace the enhanced FAQ functionality script with a more direct approach -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.clear();
        console.log('DOM loaded, initializing functionality');
        
        // Fix for FAQ accordion
        var faqQuestions = document.querySelectorAll('.faq-question');
        
        faqQuestions.forEach(function(question) {
            question.addEventListener('click', function() {
                console.log('FAQ question clicked');
                
                // Get the parent item
                var faqItem = this.parentNode;
                
                // Get all FAQ items
                var allItems = document.querySelectorAll('.faq-item');
                
                // Close all other items
                allItems.forEach(function(item) {
                    if (item !== faqItem && item.classList.contains('active')) {
                        item.classList.remove('active');
                    }
                });
                
                // Toggle the clicked item
                faqItem.classList.toggle('active');
            });
        });
        
        // Fix for mobile menu
        var mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        var nav = document.querySelector('nav');
        
        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', function(e) {
                console.log('Mobile menu button clicked');
                e.preventDefault();
                nav.classList.toggle('mobile-menu-open');
            });
            
            // Close when clicking outside
            document.addEventListener('click', function(e) {
                if (nav.classList.contains('mobile-menu-open') && 
                    !e.target.closest('nav') && 
                    !e.target.closest('.mobile-menu-btn')) {
                    nav.classList.remove('mobile-menu-open');
                }
            });
        }
        
        // Initialize calculator
        initSavingsCalculator();
    });
</script>

<!-- Add CSS for reducing logo size on mobile -->
<style>
    @media (max-width: 768px) {
        .logo-image {
            width: 75%; /* Reduce by 25% */
            height: auto;
        }
        
        .logo {
            display: flex;
            align-items: center;
        }
    }
</style>

<!-- Comprehensive Feature Comparison Styling -->
<style>
    /* Comprehensive Feature Comparison Styling */
    .comprehensive-features {
        padding: 5rem 2rem;
        background-color: #ffffff;
    }

    .comprehensive-features h2 {
        font-size: 2.5rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 1rem;
        text-align: center;
    }

    .comprehensive-features .section-description {
        text-align: center;
        color: #666;
        font-size: 1.1rem;
        max-width: 800px;
        margin: 0 auto 3rem auto;
    }

    .feature-comparison-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 2rem;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .feature-comparison-header {
        padding: 1.5rem 2rem;
        background-color: #4a6cf7;
    }

    .feature-comparison-header h3 {
        color: white;
        font-size: 1.4rem;
        font-weight: 600;
        margin: 0;
    }

    .feature-comparison-grid {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr 1fr 2fr;
        width: 100%;
        border-collapse: collapse;
    }

    .feature-row {
        display: contents;
    }

    .feature-row > div {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        display: flex;
        align-items: center;
    }

    .feature-row:last-child > div {
        border-bottom: none;
    }

    .feature-name {
        font-weight: 600;
        color: #444;
        background-color: rgba(0, 0, 0, 0.02);
    }

    .feature-essential, .feature-professional, .feature-business {
        color: #666;
        text-align: center;
        justify-content: center;
    }

    .feature-enterprise {
        background-color: #f1f5fa;
        color: #444;
        font-weight: 500;
    }

    .feature-enterprise i {
        color: #4a6cf7;
        margin-right: 8px;
    }

    .feature-row > div:not(.feature-name) i.fas.fa-check {
        color: #4a6cf7;
    }

    .header-row > div {
        font-weight: 600;
        border-bottom: 2px solid rgba(0, 0, 0, 0.08);
        padding-top: 1.2rem;
        padding-bottom: 1.2rem;
    }

    .plan-header {
        color: #333;
        text-align: center;
        justify-content: center;
    }
    
    .enterprise-header {
        background-color: #f1f5fa;
        color: #333;
        font-weight: 700;
    }

    .cta-container {
        text-align: center;
        margin-top: 3rem;
    }

    .cta-container .btn {
        padding: 1rem 2rem;
        font-size: 1.1rem;
    }

    @media (max-width: 992px) {
        .feature-comparison-grid {
            grid-template-columns: 1.5fr 0.8fr 0.8fr 0.8fr 1.5fr;
        }
        
        .feature-row > div {
            padding: 0.8rem 1rem;
            font-size: 0.9rem;
        }
        
        .feature-comparison-header h3 {
            font-size: 1.2rem;
        }
    }

    @media (max-width: 768px) {
        .comprehensive-features {
            padding: 3rem 1rem;
        }
        
        .comprehensive-features h2 {
            font-size: 1.8rem;
        }
        
        .feature-enterprise i {
            margin-right: 4px;
        }
        
        .feature-row > div {
            padding: 0.7rem 0.5rem;
            font-size: 0.8rem;
        }
    }

    @media (max-width: 576px) {
        .feature-comparison-grid {
            display: block !important;
        }
        
        .feature-row {
            display: block !important;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .feature-row > div {
            display: block !important;
            border-bottom: none !important;
            padding: 0.5rem 1rem !important;
            text-align: left !important;
            justify-content: flex-start !important;
        }
        
        /* Feature name formatting for mobile */
        .feature-name {
            font-weight: bold;
            font-size: 1rem;
            background-color: rgba(0, 0, 0, 0.05);
            padding: 0.75rem 1rem !important;
            margin-bottom: 0.5rem;
            border-radius: 4px;
            text-align: left !important;
        }
        
        /* Reset the before pseudo-elements */
        .feature-essential::before, .feature-professional::before, 
        .feature-business::before, .feature-enterprise::before {
            content: none !important;
        }
        
        /* Format plan values with plan names */
        .feature-essential, .feature-professional, 
        .feature-business, .feature-enterprise {
            position: relative;
            padding-left: 0 !important;
            border-bottom: none;
            display: flex !important;
            align-items: flex-start !important;
            justify-content: flex-start !important;
            text-align: left !important;
            padding: 0.5rem 1rem !important;
        }
        
        .feature-row:not(.header-row) .feature-essential::before {
            content: "Essential: " !important;
            font-weight: 600;
            color: #444;
            margin-right: 0.5rem;
            display: inline-block;
            min-width: 80px;
        }
        
        .feature-row:not(.header-row) .feature-professional::before {
            content: "Pro: " !important;
            font-weight: 600;
            color: #444;
            margin-right: 0.5rem;
            display: inline-block;
            min-width: 80px;
        }
        
        .feature-row:not(.header-row) .feature-business::before {
            content: "Business: " !important;
            font-weight: 600;
            color: #444;
            margin-right: 0.5rem;
            display: inline-block;
            min-width: 80px;
        }
        
        .feature-row:not(.header-row) .feature-enterprise::before {
            content: "Enterprise: " !important;
            font-weight: 600;
            color: #444;
            margin-right: 0.5rem;
            display: inline-block;
            min-width: 80px;
        }
        
        /* Hide header row */
        .header-row {
            display: none !important;
        }
        
        /* Remove border from the last row in each card */
        .feature-comparison-card .feature-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        /* Adjust spacing for checkmarks */
        .feature-row > div i.fas.fa-check {
            margin-left: 0 !important;
        }
    }
</style>

<!-- Calculator Styles -->
<style>
    /* Savings Calculator Styling */
    .calculator-card {
        background-color: #f8f9fa;
        display: flex;
        flex-direction: column;
    }
    
    .savings-calculator {
        padding: 1.5rem;
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    
    .savings-calculator h3 {
        font-size: 1.5rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 0.5rem;
        text-align: center;
    }
    
    .calculator-description {
        text-align: center;
        color: #666;
        font-size: 0.95rem;
        margin-bottom: 1.5rem;
    }
    
    .calculator-note {
        background-color: rgba(74, 108, 247, 0.05);
        border-radius: 6px;
        padding: 0.8rem;
        margin-bottom: 1.5rem;
        font-size: 0.85rem;
        color: #555;
    }
    
    .calculator-note i {
        color: #4a6cf7;
        margin-right: 5px;
    }
    
    .calculator-inputs {
        margin-bottom: 1.5rem;
    }
    
    .calculator-input {
        margin-bottom: 1.5rem;
    }
    
    .calculator-input:last-child {
        margin-bottom: 0;
    }
    
    .calculator-input label {
        display: block;
        font-size: 0.8rem;
        font-weight: 600;
        color: #444;
        margin-bottom: 0.8rem;
        letter-spacing: 0.5px;
    }
    
    .range-slider {
        position: relative;
        margin-top: 1rem;
    }
    
    .range-slider input[type="range"] {
        width: 100%;
        -webkit-appearance: none;
        appearance: none;
        height: 8px;
        border-radius: 4px;
        background: linear-gradient(to right, #4a6cf7, #6d89f7);
        outline: none;
    }
    
    .range-slider input[type="range"]::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #ffffff;
        cursor: pointer;
        border: 2px solid #4a6cf7;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }
    
    .range-slider input[type="range"]::-moz-range-thumb {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #ffffff;
        cursor: pointer;
        border: 2px solid #4a6cf7;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }
    
    .range-value {
        position: absolute;
        top: -25px;
        right: 0;
        font-weight: 600;
        color: #4a6cf7;
        font-size: 1rem;
    }
    
    .savings-result {
        margin-top: auto;
        background-color: #ffffff;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        text-align: center;
    }
    
    @media (min-width: 769px) {
        .savings-result {
            max-width: 600px;
            margin: auto auto;
        }
        
        .savings-details {
            display: flex;
            justify-content: center;
            margin-top: 1rem;
            gap: 2rem;
        }
        
        .savings-metric {
            flex: initial;
            padding: 0.5rem;
            min-width: 100px;
        }
    }
    
    .savings-amount {
        font-size: 2.5rem;
        font-weight: 700;
        color: #4a6cf7;
    }
    
    .savings-period {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 1.5rem;
    }
    
    .savings-details {
        display: flex;
        justify-content: space-between;
        margin-top: 1rem;
    }
    
    .savings-metric {
        flex: 1;
        padding: 0.5rem;
    }
    
    .metric-value {
        font-size: 1.2rem;
        font-weight: 600;
        color: #333;
    }
    
    .metric-label {
        font-size: 0.8rem;
        color: #666;
        margin-top: 0.3rem;
    }
    
    @media (max-width: 992px) {
        .savings-details {
            flex-direction: column;
            gap: 1rem;
        }
    }
</style>

<script>
    // Initialize Savings Calculator
    function initSavingsCalculator() {
        // Get calculator elements
        const tripCountSlider = document.getElementById('tripCount');
        const tripCountValue = document.getElementById('tripCountValue');
        const avgTripCostSlider = document.getElementById('avgTripCost');
        const avgTripCostValue = document.getElementById('avgTripCostValue');
        
        // Get metric elements
        const savingsAmount = document.querySelector('.savings-amount');
        const timeMetric = document.getElementById('timeMetric');
        const costMetric = document.getElementById('costMetric');
        const efficiencyMetric = document.getElementById('efficiencyMetric');
        
        // Update display functions
        function updateTripCount() {
            tripCountValue.textContent = tripCountSlider.value;
            calculateSavings();
        }
        
        function updateAvgTripCost() {
            avgTripCostValue.textContent = `$${avgTripCostSlider.value}`;
            calculateSavings();
        }
        
        // Main calculation
        function calculateSavings() {
            const tripCount = parseInt(tripCountSlider.value);
            const avgCost = parseInt(avgTripCostSlider.value);
            
            // Calculate metrics
            const timeHours = Math.round(tripCount * 1.5); // 1.5 hours saved per trip
            const costSavings = Math.round(tripCount * avgCost * 0.15); // 15% savings
            const efficiency = Math.round((timeHours / (tripCount * 2.25)) * 100); // Efficiency gain percentage
            
            // Update metrics display
            timeMetric.textContent = `${timeHours}h`;
            costMetric.textContent = `$${costSavings.toLocaleString()}`;
            efficiencyMetric.textContent = `${efficiency}%`;
            savingsAmount.textContent = `$${costSavings.toLocaleString()}`;
        }
        
        // Add event listeners
        if (tripCountSlider) {
            tripCountSlider.addEventListener('input', updateTripCount);
            avgTripCostSlider.addEventListener('input', updateAvgTripCost);
            
            // Initial calculation
            calculateSavings();
        }
    }
</script>

<!-- Add additional styling for pricing page -->
<style>
    /* Add spacing between hero and pricing section */
    .pricing-hero {
        padding: 4rem 2rem 2rem;
        margin-bottom: 3rem;
    }
    
    .pricing-hero h1 {
        font-size: 3.2rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-align: center;
    }
    
    .pricing-hero p {
        font-size: 1.2rem;
        color: #555;
        text-align: center;
        max-width: 700px;
        margin: 0 auto;
    }
    
    /* Fix calculator card to take full width on desktop */
    .calculator-card {
        grid-column: 1 / -1;
        width: 100%;
    }
    
    /* Fix for FAQ accordion */
    .faq-item.active .faq-answer {
        max-height: 1000px; /* Increased max-height to ensure content fits */
        padding-top: 0.5rem;
        padding-bottom: 1rem;
    }
    
    @media (max-width: 768px) {
        .pricing-hero {
            padding: 3rem 1.5rem 1.5rem;
            margin-bottom: 2rem;
        }
        
        .pricing-hero h1 {
            font-size: 2.5rem;
        }
        
        .pricing-hero p {
            font-size: 1.1rem;
        }
    }
</style>
</html> 