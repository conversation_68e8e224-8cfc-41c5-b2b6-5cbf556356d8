import { getSupabaseClient } from '@/lib/supabase'
import { getConnectionManager } from '@/lib/websocket/connection-manager'
import { getWebSocketAuth } from '@/lib/websocket/auth'

export interface QuoteStatusUpdate {
  id: string
  quote_id: string
  old_status: string
  new_status: string
  updated_by: string
  updated_at: string
  metadata?: Record<string, any>
  user_roles?: string[]
}

export interface QuoteStatusNotification {
  type: 'status_change' | 'offer_received' | 'offer_accepted' | 'offer_rejected'
  quote_id: string
  message: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  recipient_roles: string[]
  data?: Record<string, any>
}

class QuoteStatusBroadcaster {
  private connectionManager = getConnectionManager()
  private webSocketAuth = getWebSocketAuth()
  private activeSubscriptions = new Map<string, any>()

  /**
   * Broadcast quote status change to all relevant users
   */
  async broadcastStatusChange(update: QuoteStatusUpdate): Promise<void> {
    try {
      console.log('[QuoteStatusBroadcaster] Broadcasting status change:', update)

      // Validate authentication
      if (!this.webSocketAuth.isAuthenticated()) {
        console.error('[QuoteStatusBroadcaster] User not authenticated')
        return
      }

      // Create notification based on status change
      const notification = this.createStatusNotification(update)

      // Broadcast to general quote updates channel
      await this.broadcastToChannel('quote_status_updates', {
        type: 'status_change',
        data: update,
        notification
      })

      // Broadcast to specific quote channel
      await this.broadcastToChannel(`quote_${update.quote_id}`, {
        type: 'status_change',
        data: update,
        notification
      })

      // Broadcast to user-specific channels based on roles
      await this.broadcastToRoleChannels(update, notification)

      console.log('[QuoteStatusBroadcaster] Status change broadcast completed')
    } catch (error) {
      console.error('[QuoteStatusBroadcaster] Error broadcasting status change:', error)
    }
  }

  /**
   * Broadcast quote offer updates
   */
  async broadcastOfferUpdate(quoteId: string, offerData: any): Promise<void> {
    try {
      console.log('[QuoteStatusBroadcaster] Broadcasting offer update:', { quoteId, offerData })

      const notification: QuoteStatusNotification = {
        type: 'offer_received',
        quote_id: quoteId,
        message: `New offer received for quote ${quoteId}`,
        priority: 'high',
        recipient_roles: ['event_manager', 'customer'],
        data: offerData
      }

      // Broadcast to quote-specific channels
      await this.broadcastToChannel(`quote_offers_${quoteId}`, {
        type: 'offer_update',
        data: offerData,
        notification
      })

      await this.broadcastToChannel(`quote_${quoteId}`, {
        type: 'offer_update',
        data: offerData,
        notification
      })

    } catch (error) {
      console.error('[QuoteStatusBroadcaster] Error broadcasting offer update:', error)
    }
  }

  /**
   * Subscribe to quote status updates for a specific quote
   */
  subscribeToQuoteStatus(
    quoteId: string, 
    callback: (update: QuoteStatusUpdate) => void
  ): (() => void) | null {
    if (!this.connectionManager.isConnected()) {
      console.warn('[QuoteStatusBroadcaster] Cannot subscribe: not connected')
      return null
    }

    const channelName = `quote_status_${quoteId}`
    const handlers = [
      {
        event: 'status_change',
        callback: (payload: any) => {
          console.log('[QuoteStatusBroadcaster] Received status update:', payload)
          if (payload.data) {
            callback(payload.data)
          }
        }
      }
    ]

    const subscription = this.connectionManager.subscribe(channelName, handlers)
    
    if (subscription) {
      this.activeSubscriptions.set(channelName, subscription)
      
      return () => {
        subscription.unsubscribe()
        this.activeSubscriptions.delete(channelName)
      }
    }

    return null
  }

  /**
   * Subscribe to all quote status updates
   */
  subscribeToAllQuoteUpdates(
    callback: (update: QuoteStatusUpdate) => void
  ): (() => void) | null {
    if (!this.connectionManager.isConnected()) {
      console.warn('[QuoteStatusBroadcaster] Cannot subscribe: not connected')
      return null
    }

    const channelName = 'quote_status_updates'
    const handlers = [
      {
        event: 'status_change',
        callback: (payload: any) => {
          console.log('[QuoteStatusBroadcaster] Received global status update:', payload)
          if (payload.data) {
            callback(payload.data)
          }
        }
      }
    ]

    const subscription = this.connectionManager.subscribe(channelName, handlers)
    
    if (subscription) {
      this.activeSubscriptions.set(channelName, subscription)
      
      return () => {
        subscription.unsubscribe()
        this.activeSubscriptions.delete(channelName)
      }
    }

    return null
  }

  /**
   * Create a notification based on status change
   */
  private createStatusNotification(update: QuoteStatusUpdate): QuoteStatusNotification {
    const statusMessages: Record<string, { message: string; priority: QuoteStatusNotification['priority'] }> = {
      'pending': { message: 'Quote is pending review', priority: 'medium' },
      'rate_requested': { message: 'Rate has been requested from affiliates', priority: 'medium' },
      'quote_ready': { message: 'Quote is ready for review', priority: 'high' },
      'accepted': { message: 'Quote has been accepted', priority: 'high' },
      'rejected': { message: 'Quote has been rejected', priority: 'medium' },
      'completed': { message: 'Quote has been completed', priority: 'low' },
      'cancelled': { message: 'Quote has been cancelled', priority: 'medium' }
    }

    const statusInfo = statusMessages[update.new_status] || { 
      message: `Quote status changed to ${update.new_status}`, 
      priority: 'medium' as const 
    }

    return {
      type: 'status_change',
      quote_id: update.quote_id,
      message: statusInfo.message,
      priority: statusInfo.priority,
      recipient_roles: ['event_manager', 'customer', 'affiliate'],
      data: update
    }
  }

  /**
   * Broadcast to a specific channel
   */
  private async broadcastToChannel(channelName: string, payload: any): Promise<void> {
    try {
      const supabase = getSupabaseClient()
      if (!supabase) {
        console.error('[QuoteStatusBroadcaster] Supabase client not available')
        return
      }

      // Use Supabase's broadcast functionality
      const channel = supabase.channel(channelName)
      await channel.send({
        type: 'broadcast',
        event: payload.type,
        payload
      })

    } catch (error) {
      console.error(`[QuoteStatusBroadcaster] Error broadcasting to channel ${channelName}:`, error)
    }
  }

  /**
   * Broadcast to role-specific channels
   */
  private async broadcastToRoleChannels(
    update: QuoteStatusUpdate, 
    notification: QuoteStatusNotification
  ): Promise<void> {
    const userRoles = this.webSocketAuth.getUserRoles()
    
    for (const role of notification.recipient_roles) {
      if (userRoles.includes(role)) {
        await this.broadcastToChannel(`role_${role}_quotes`, {
          type: 'status_change',
          data: update,
          notification
        })
      }
    }
  }

  /**
   * Clean up all subscriptions
   */
  cleanup(): void {
    this.activeSubscriptions.forEach(subscription => {
      subscription.unsubscribe()
    })
    this.activeSubscriptions.clear()
  }
}

// Singleton instance
let quoteStatusBroadcaster: QuoteStatusBroadcaster | null = null

export function getQuoteStatusBroadcaster(): QuoteStatusBroadcaster {
  if (!quoteStatusBroadcaster) {
    quoteStatusBroadcaster = new QuoteStatusBroadcaster()
  }
  return quoteStatusBroadcaster
}

/**
 * Utility function to trigger a quote status change
 */
export async function triggerQuoteStatusChange(
  quoteId: string,
  newStatus: string,
  metadata?: Record<string, any>
): Promise<void> {
  const broadcaster = getQuoteStatusBroadcaster()
  const webSocketAuth = getWebSocketAuth()

  const update: QuoteStatusUpdate = {
    id: `${quoteId}_${Date.now()}`,
    quote_id: quoteId,
    old_status: 'unknown', // This should be fetched from current quote
    new_status: newStatus,
    updated_by: webSocketAuth.getUserId() || 'system',
    updated_at: new Date().toISOString(),
    metadata,
    user_roles: webSocketAuth.getUserRoles()
  }

  await broadcaster.broadcastStatusChange(update)
}

export { QuoteStatusBroadcaster }
