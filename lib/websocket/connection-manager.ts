import { RealtimeChannel, RealtimeClient } from '@supabase/supabase-js'
import { getSupabaseClient } from '@/lib/supabase'

export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error' | 'reconnecting'

export interface ConnectionState {
  status: ConnectionStatus
  error?: string
  lastConnected?: Date
  reconnectAttempts: number
  maxReconnectAttempts: number
}

export interface WebSocketEventHandler {
  event: string
  callback: (payload: any) => void
}

export interface ChannelSubscription {
  channel: RealtimeChannel
  handlers: WebSocketEventHandler[]
  unsubscribe: () => void
}

class ConnectionManager {
  private client: RealtimeClient | null = null
  private channels: Map<string, ChannelSubscription> = new Map()
  private connectionState: ConnectionState = {
    status: 'disconnected',
    reconnectAttempts: 0,
    maxReconnectAttempts: 5
  }
  private listeners: Set<(state: ConnectionState) => void> = new Set()
  private reconnectTimer: NodeJS.Timeout | null = null
  private heartbeatTimer: NodeJS.Timeout | null = null

  constructor() {
    this.initializeConnection()
  }

  private initializeConnection() {
    const supabase = getSupabaseClient()
    if (!supabase) {
      console.error('[ConnectionManager] Supabase client not available')
      this.updateConnectionState({ status: 'error', error: 'Supabase client not available' })
      return
    }

    this.client = supabase.realtime
    this.setupConnectionListeners()
  }

  private setupConnectionListeners() {
    if (!this.client) return

    // Supabase RealtimeClient uses different event handling
    // We'll monitor connection state through channel subscriptions
    this.updateConnectionState({ status: 'connecting' })

    // Create a test channel to monitor connection status
    const statusChannel = this.client.channel('connection-status-test')

    statusChannel.subscribe((status) => {
      console.log('[ConnectionManager] Connection status:', status)

      switch (status) {
        case 'SUBSCRIBED':
          console.log('[ConnectionManager] WebSocket connection established')
          this.updateConnectionState({
            status: 'connected',
            lastConnected: new Date(),
            reconnectAttempts: 0,
            error: undefined
          })
          this.startHeartbeat()
          break
        case 'CHANNEL_ERROR':
        case 'TIMED_OUT':
          console.error('[ConnectionManager] WebSocket connection error:', status)
          this.updateConnectionState({
            status: 'error',
            error: `Connection error: ${status}`
          })
          this.stopHeartbeat()
          this.scheduleReconnect()
          break
        case 'CLOSED':
          console.log('[ConnectionManager] WebSocket connection closed')
          this.updateConnectionState({ status: 'disconnected' })
          this.stopHeartbeat()
          this.scheduleReconnect()
          break
      }
    })
  }

  private updateConnectionState(updates: Partial<ConnectionState>) {
    this.connectionState = { ...this.connectionState, ...updates }
    this.notifyListeners()
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.connectionState))
  }

  private scheduleReconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }

    if (this.connectionState.reconnectAttempts >= this.connectionState.maxReconnectAttempts) {
      console.error('[ConnectionManager] Max reconnection attempts reached')
      this.updateConnectionState({ status: 'error', error: 'Max reconnection attempts reached' })
      return
    }

    const delay = Math.min(1000 * Math.pow(2, this.connectionState.reconnectAttempts), 30000)
    console.log(`[ConnectionManager] Scheduling reconnect in ${delay}ms (attempt ${this.connectionState.reconnectAttempts + 1})`)
    
    this.updateConnectionState({ 
      status: 'reconnecting',
      reconnectAttempts: this.connectionState.reconnectAttempts + 1
    })

    this.reconnectTimer = setTimeout(() => {
      this.connect()
    }, delay)
  }

  private startHeartbeat() {
    this.stopHeartbeat()
    this.heartbeatTimer = setInterval(() => {
      if (this.client && this.connectionState.status === 'connected') {
        // Supabase handles heartbeat automatically
        // We'll just log that we're monitoring the connection
        console.log('[ConnectionManager] Connection heartbeat check - status:', this.connectionState.status)
      }
    }, 30000) // 30 seconds
  }

  private stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  public connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.client) {
        this.initializeConnection()
      }

      if (this.connectionState.status === 'connected') {
        resolve()
        return
      }

      this.updateConnectionState({ status: 'connecting' })

      const timeout = setTimeout(() => {
        reject(new Error('Connection timeout'))
      }, 10000)

      const onConnect = (state: ConnectionState) => {
        if (state.status === 'connected') {
          clearTimeout(timeout)
          this.removeListener(onConnect)
          resolve()
        } else if (state.status === 'error') {
          clearTimeout(timeout)
          this.removeListener(onConnect)
          reject(new Error(state.error || 'Connection failed'))
        }
      }

      this.addListener(onConnect)

      // Supabase RealtimeClient connects automatically when channels are subscribed
      // The connection status will be updated through the status channel
      console.log('[ConnectionManager] Connection will be established through channel subscriptions')
    })
  }

  public disconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    this.stopHeartbeat()

    // Unsubscribe from all channels
    this.channels.forEach(subscription => {
      subscription.unsubscribe()
    })
    this.channels.clear()

    // Supabase RealtimeClient doesn't have a direct disconnect method
    // Disconnection happens when all channels are unsubscribed
    console.log('[ConnectionManager] Disconnected - all channels unsubscribed')

    this.updateConnectionState({ status: 'disconnected' })
  }

  public getConnectionState(): ConnectionState {
    return { ...this.connectionState }
  }

  public addListener(listener: (state: ConnectionState) => void) {
    this.listeners.add(listener)
  }

  public removeListener(listener: (state: ConnectionState) => void) {
    this.listeners.delete(listener)
  }

  public subscribe(
    channelName: string,
    handlers: WebSocketEventHandler[]
  ): ChannelSubscription | null {
    const supabase = getSupabaseClient()
    if (!supabase) {
      console.error('[ConnectionManager] Cannot subscribe: Supabase client not available')
      return null
    }

    // Remove existing subscription if it exists
    if (this.channels.has(channelName)) {
      this.unsubscribe(channelName)
    }

    const channel = supabase.channel(channelName)

    // Add all handlers to the channel
    handlers.forEach(({ event, callback }) => {
      if (event === 'postgres_changes') {
        // Handle postgres_changes events specially
        channel.on('postgres_changes', callback)
      } else {
        channel.on(event as any, callback)
      }
    })

    const subscription: ChannelSubscription = {
      channel,
      handlers,
      unsubscribe: () => {
        channel.unsubscribe()
        this.channels.delete(channelName)
      }
    }

    // Subscribe to the channel
    channel.subscribe((status) => {
      console.log(`[ConnectionManager] Channel ${channelName} subscription status:`, status)
    })

    this.channels.set(channelName, subscription)
    return subscription
  }

  public unsubscribe(channelName: string) {
    const subscription = this.channels.get(channelName)
    if (subscription) {
      subscription.unsubscribe()
    }
  }

  public isConnected(): boolean {
    return this.connectionState.status === 'connected'
  }
}

// Singleton instance
let connectionManager: ConnectionManager | null = null

export function getConnectionManager(): ConnectionManager {
  if (!connectionManager) {
    connectionManager = new ConnectionManager()
  }
  return connectionManager
}

export { ConnectionManager }
