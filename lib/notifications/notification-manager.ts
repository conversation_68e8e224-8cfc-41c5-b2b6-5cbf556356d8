import { getPushNotificationService, NotificationPayload } from './push-service'
import { getQuoteStatusBroadcaster, QuoteStatusUpdate } from '@/lib/api/quote-status-broadcast'
import { getWebSocketAuth } from '@/lib/websocket/auth'
import { toast } from 'sonner'

export interface NotificationTemplate {
  title: string
  body: string
  icon?: string
  actions?: Array<{ action: string; title: string }>
}

export interface NotificationHistory {
  id: string
  timestamp: Date
  payload: NotificationPayload
  status: 'sent' | 'clicked' | 'dismissed'
  read: boolean
}

class NotificationManager {
  private pushService = getPushNotificationService()
  private quoteStatusBroadcaster = getQuoteStatusBroadcaster()
  private webSocketAuth = getWebSocketAuth()
  private notificationHistory: NotificationHistory[] = []
  private listeners: Set<(notification: NotificationHistory) => void> = new Set()

  constructor() {
    this.initializeManager()
  }

  /**
   * Initialize the notification manager
   */
  private async initializeManager(): Promise<void> {
    try {
      console.log('[NotificationManager] Initializing notification manager')
      
      // Load notification history
      await this.loadNotificationHistory()
      
      // Set up real-time quote status notifications
      this.setupQuoteStatusNotifications()
      
      // Set up service worker message handling
      this.setupServiceWorkerMessages()
      
      console.log('[NotificationManager] Notification manager initialized')
    } catch (error) {
      console.error('[NotificationManager] Error initializing manager:', error)
    }
  }

  /**
   * Send a notification with automatic template selection
   */
  async sendNotification(
    type: NotificationPayload['type'],
    data: Record<string, any>,
    options: Partial<NotificationPayload> = {}
  ): Promise<void> {
    try {
      const template = this.getNotificationTemplate(type, data)
      
      const payload: NotificationPayload = {
        title: template.title,
        body: template.body,
        type,
        priority: options.priority || 'medium',
        icon: template.icon || options.icon,
        actions: template.actions || options.actions,
        data,
        tag: options.tag || `${type}-${data.id || Date.now()}`,
        ...options
      }

      // Send push notification
      await this.pushService.sendLocalNotification(payload)
      
      // Add to history
      this.addToHistory(payload, 'sent')
      
      // Show toast notification as fallback
      this.showToastNotification(payload)
      
      console.log('[NotificationManager] Notification sent successfully:', payload)
    } catch (error) {
      console.error('[NotificationManager] Error sending notification:', error)
    }
  }

  /**
   * Send quote status notification
   */
  async sendQuoteStatusNotification(update: QuoteStatusUpdate): Promise<void> {
    const data = {
      quote_id: update.quote_id,
      old_status: update.old_status,
      new_status: update.new_status,
      event_id: update.metadata?.event_id,
      updated_by: update.updated_by
    }

    await this.sendNotification('quote_status', data, {
      priority: this.getQuoteStatusPriority(update.new_status),
      requireInteraction: update.new_status === 'accepted' || update.new_status === 'rejected'
    })
  }

  /**
   * Send trip update notification
   */
  async sendTripUpdateNotification(tripData: any): Promise<void> {
    const data = {
      trip_id: tripData.id,
      status: tripData.status,
      driver_name: tripData.driver?.name,
      pickup_time: tripData.pickup_time,
      event_id: tripData.event_id
    }

    await this.sendNotification('trip_update', data, {
      priority: this.getTripUpdatePriority(tripData.status),
      requireInteraction: tripData.status === 'driver_arrived' || tripData.status === 'completed'
    })
  }

  /**
   * Send offer received notification
   */
  async sendOfferNotification(offerData: any): Promise<void> {
    const data = {
      quote_id: offerData.quote_id,
      affiliate_name: offerData.affiliate_name,
      price: offerData.price,
      vehicle_type: offerData.vehicle_type,
      event_id: offerData.event_id
    }

    await this.sendNotification('offer_received', data, {
      priority: 'high',
      requireInteraction: true
    })
  }

  /**
   * Get notification history
   */
  getNotificationHistory(): NotificationHistory[] {
    return [...this.notificationHistory].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
  }

  /**
   * Mark notification as read
   */
  markAsRead(notificationId: string): void {
    const notification = this.notificationHistory.find(n => n.id === notificationId)
    if (notification) {
      notification.read = true
      this.saveNotificationHistory()
      this.notifyListeners(notification)
    }
  }

  /**
   * Mark all notifications as read
   */
  markAllAsRead(): void {
    this.notificationHistory.forEach(notification => {
      notification.read = true
    })
    this.saveNotificationHistory()
  }

  /**
   * Clear notification history
   */
  clearHistory(): void {
    this.notificationHistory = []
    this.saveNotificationHistory()
  }

  /**
   * Get unread notification count
   */
  getUnreadCount(): number {
    return this.notificationHistory.filter(n => !n.read).length
  }

  /**
   * Add listener for notification updates
   */
  addListener(listener: (notification: NotificationHistory) => void): void {
    this.listeners.add(listener)
  }

  /**
   * Remove listener
   */
  removeListener(listener: (notification: NotificationHistory) => void): void {
    this.listeners.delete(listener)
  }

  /**
   * Request notification permission
   */
  async requestPermission(): Promise<NotificationPermission> {
    return this.pushService.requestPermission()
  }

  /**
   * Get notification preferences
   */
  getPreferences() {
    return this.pushService.getPreferences()
  }

  /**
   * Update notification preferences
   */
  async updatePreferences(preferences: any): Promise<void> {
    return this.pushService.updatePreferences(preferences)
  }

  /**
   * Get notification template based on type and data
   */
  private getNotificationTemplate(type: NotificationPayload['type'], data: any): NotificationTemplate {
    switch (type) {
      case 'quote_status':
        return this.getQuoteStatusTemplate(data)
      case 'trip_update':
        return this.getTripUpdateTemplate(data)
      case 'offer_received':
        return this.getOfferReceivedTemplate(data)
      case 'system_alert':
        return this.getSystemAlertTemplate(data)
      default:
        return {
          title: 'Transflow Notification',
          body: 'You have a new notification',
          icon: '/favicon.ico'
        }
    }
  }

  /**
   * Get quote status notification template
   */
  private getQuoteStatusTemplate(data: any): NotificationTemplate {
    const statusMessages: Record<string, { title: string; body: string }> = {
      'pending': {
        title: 'Quote Pending Review',
        body: `Quote ${data.quote_id} is awaiting review`
      },
      'rate_requested': {
        title: 'Rate Requested',
        body: `Requesting rates from affiliates for quote ${data.quote_id}`
      },
      'quote_ready': {
        title: 'Quote Ready for Review',
        body: `Quote ${data.quote_id} is ready for your review`
      },
      'accepted': {
        title: '🎉 Quote Accepted!',
        body: `Quote ${data.quote_id} has been accepted`
      },
      'rejected': {
        title: 'Quote Rejected',
        body: `Quote ${data.quote_id} has been rejected`
      },
      'completed': {
        title: 'Quote Completed',
        body: `Quote ${data.quote_id} has been completed successfully`
      },
      'cancelled': {
        title: 'Quote Cancelled',
        body: `Quote ${data.quote_id} has been cancelled`
      }
    }

    const template = statusMessages[data.new_status] || statusMessages['pending']
    
    return {
      ...template,
      icon: '/favicon.ico',
      actions: [
        { action: 'view_quote', title: 'View Quote' },
        { action: 'dismiss', title: 'Dismiss' }
      ]
    }
  }

  /**
   * Get trip update notification template
   */
  private getTripUpdateTemplate(data: any): NotificationTemplate {
    const statusMessages: Record<string, { title: string; body: string }> = {
      'driver_assigned': {
        title: 'Driver Assigned',
        body: `${data.driver_name} has been assigned to your trip`
      },
      'driver_en_route': {
        title: 'Driver En Route',
        body: `${data.driver_name} is on the way to pick you up`
      },
      'driver_arrived': {
        title: 'Driver Arrived',
        body: `${data.driver_name} has arrived at the pickup location`
      },
      'trip_started': {
        title: 'Trip Started',
        body: 'Your trip has started'
      },
      'trip_completed': {
        title: 'Trip Completed',
        body: 'Your trip has been completed successfully'
      }
    }

    const template = statusMessages[data.status] || {
      title: 'Trip Update',
      body: `Your trip status has been updated to ${data.status}`
    }

    return {
      ...template,
      icon: '/favicon.ico',
      actions: [
        { action: 'view_trip', title: 'View Trip' },
        { action: 'track_trip', title: 'Track' }
      ]
    }
  }

  /**
   * Get offer received notification template
   */
  private getOfferReceivedTemplate(data: any): NotificationTemplate {
    return {
      title: 'New Offer Received',
      body: `${data.affiliate_name} sent an offer for $${data.price} (${data.vehicle_type})`,
      icon: '/favicon.ico',
      actions: [
        { action: 'view_quote', title: 'View Offer' },
        { action: 'dismiss', title: 'Dismiss' }
      ]
    }
  }

  /**
   * Get system alert notification template
   */
  private getSystemAlertTemplate(data: any): NotificationTemplate {
    return {
      title: data.title || 'System Alert',
      body: data.message || 'You have a new system notification',
      icon: '/favicon.ico'
    }
  }

  /**
   * Get priority level for quote status
   */
  private getQuoteStatusPriority(status: string): NotificationPayload['priority'] {
    const priorityMap: Record<string, NotificationPayload['priority']> = {
      'accepted': 'high',
      'rejected': 'high',
      'quote_ready': 'high',
      'rate_requested': 'medium',
      'pending': 'medium',
      'completed': 'low',
      'cancelled': 'medium'
    }
    return priorityMap[status] || 'medium'
  }

  /**
   * Get priority level for trip updates
   */
  private getTripUpdatePriority(status: string): NotificationPayload['priority'] {
    const priorityMap: Record<string, NotificationPayload['priority']> = {
      'driver_arrived': 'urgent',
      'driver_en_route': 'high',
      'driver_assigned': 'medium',
      'trip_started': 'high',
      'trip_completed': 'medium'
    }
    return priorityMap[status] || 'medium'
  }

  /**
   * Set up quote status notifications
   */
  private setupQuoteStatusNotifications(): void {
    this.quoteStatusBroadcaster.subscribeToAllQuoteUpdates((update) => {
      this.sendQuoteStatusNotification(update)
    })
  }

  /**
   * Set up service worker message handling
   */
  private setupServiceWorkerMessages(): void {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', (event) => {
        const { type, data } = event.data

        if (type === 'NOTIFICATION_EVENT') {
          this.handleNotificationEvent(data.event, data.data, data.action)
        }
      })
    }
  }

  /**
   * Handle notification events from service worker
   */
  private handleNotificationEvent(event: string, data: any, action?: string): void {
    const notification = this.notificationHistory.find(n => 
      n.payload.tag === data.tag || n.payload.data?.quote_id === data.quote_id
    )

    if (notification) {
      if (event === 'clicked') {
        notification.status = 'clicked'
        notification.read = true
      } else if (event === 'dismissed') {
        notification.status = 'dismissed'
      }

      this.saveNotificationHistory()
      this.notifyListeners(notification)
    }
  }

  /**
   * Add notification to history
   */
  private addToHistory(payload: NotificationPayload, status: 'sent' | 'clicked' | 'dismissed'): void {
    const notification: NotificationHistory = {
      id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      payload,
      status,
      read: false
    }

    this.notificationHistory.unshift(notification)
    
    // Keep only last 100 notifications
    if (this.notificationHistory.length > 100) {
      this.notificationHistory = this.notificationHistory.slice(0, 100)
    }

    this.saveNotificationHistory()
    this.notifyListeners(notification)
  }

  /**
   * Show toast notification as fallback
   */
  private showToastNotification(payload: NotificationPayload): void {
    const toastOptions = {
      description: payload.body,
      duration: payload.priority === 'urgent' ? 10000 : 5000
    }

    switch (payload.priority) {
      case 'urgent':
      case 'high':
        toast.error(payload.title, toastOptions)
        break
      case 'medium':
        toast.info(payload.title, toastOptions)
        break
      case 'low':
        toast.success(payload.title, toastOptions)
        break
    }
  }

  /**
   * Notify all listeners
   */
  private notifyListeners(notification: NotificationHistory): void {
    this.listeners.forEach(listener => listener(notification))
  }

  /**
   * Load notification history from storage
   */
  private async loadNotificationHistory(): Promise<void> {
    try {
      const stored = localStorage.getItem('transflow-notification-history')
      if (stored) {
        const parsed = JSON.parse(stored)
        this.notificationHistory = parsed.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        }))
      }
    } catch (error) {
      console.error('[NotificationManager] Error loading history:', error)
    }
  }

  /**
   * Save notification history to storage
   */
  private saveNotificationHistory(): void {
    try {
      localStorage.setItem('transflow-notification-history', JSON.stringify(this.notificationHistory))
    } catch (error) {
      console.error('[NotificationManager] Error saving history:', error)
    }
  }
}

// Singleton instance
let notificationManager: NotificationManager | null = null

export function getNotificationManager(): NotificationManager {
  if (!notificationManager) {
    notificationManager = new NotificationManager()
  }
  return notificationManager
}

export { NotificationManager }
