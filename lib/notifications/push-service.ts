import { getSupabaseClient } from '@/lib/supabase'
import { getWebSocketAuth } from '@/lib/websocket/auth'

export interface NotificationPayload {
  title: string
  body: string
  type: 'quote_status' | 'trip_update' | 'offer_received' | 'system_alert'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  data?: Record<string, any>
  icon?: string
  image?: string
  actions?: NotificationAction[]
  tag?: string
  requireInteraction?: boolean
  silent?: boolean
}

export interface NotificationAction {
  action: string
  title: string
  icon?: string
}

export interface PushSubscription {
  endpoint: string
  keys: {
    p256dh: string
    auth: string
  }
}

export interface NotificationPreferences {
  enabled: boolean
  quote_updates: boolean
  trip_updates: boolean
  offer_notifications: boolean
  system_alerts: boolean
  quiet_hours: {
    enabled: boolean
    start: string // HH:MM format
    end: string   // HH:MM format
  }
  sound_enabled: boolean
  vibration_enabled: boolean
}

class PushNotificationService {
  private registration: ServiceWorkerRegistration | null = null
  private subscription: PushSubscription | null = null
  private preferences: NotificationPreferences = {
    enabled: true,
    quote_updates: true,
    trip_updates: true,
    offer_notifications: true,
    system_alerts: true,
    quiet_hours: {
      enabled: false,
      start: '22:00',
      end: '08:00'
    },
    sound_enabled: true,
    vibration_enabled: true
  }

  constructor() {
    this.initializeService()
  }

  /**
   * Initialize the push notification service
   */
  async initializeService(): Promise<void> {
    try {
      if (!('serviceWorker' in navigator)) {
        console.warn('[PushService] Service Worker not supported')
        return
      }

      if (!('PushManager' in window)) {
        console.warn('[PushService] Push messaging not supported')
        return
      }

      // Register service worker
      this.registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      })

      console.log('[PushService] Service Worker registered successfully')

      // Wait for service worker to be ready
      await navigator.serviceWorker.ready

      // Load user preferences
      await this.loadPreferences()

      // Set up message listener
      this.setupMessageListener()

      console.log('[PushService] Push notification service initialized')
    } catch (error) {
      console.error('[PushService] Error initializing service:', error)
    }
  }

  /**
   * Request notification permission from user
   */
  async requestPermission(): Promise<NotificationPermission> {
    try {
      if (!('Notification' in window)) {
        throw new Error('Notifications not supported')
      }

      let permission = Notification.permission

      if (permission === 'default') {
        permission = await Notification.requestPermission()
      }

      console.log('[PushService] Notification permission:', permission)

      if (permission === 'granted') {
        await this.subscribeToPush()
      }

      return permission
    } catch (error) {
      console.error('[PushService] Error requesting permission:', error)
      return 'denied'
    }
  }

  /**
   * Subscribe to push notifications
   */
  async subscribeToPush(): Promise<PushSubscription | null> {
    try {
      if (!this.registration) {
        throw new Error('Service Worker not registered')
      }

      // Check if already subscribed
      const existingSubscription = await this.registration.pushManager.getSubscription()
      if (existingSubscription) {
        this.subscription = existingSubscription
        await this.savePushSubscription(existingSubscription)
        return existingSubscription
      }

      // Create new subscription
      const subscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(
          process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY || ''
        )
      })

      this.subscription = subscription
      await this.savePushSubscription(subscription)

      console.log('[PushService] Push subscription created successfully')
      return subscription
    } catch (error) {
      console.error('[PushService] Error subscribing to push:', error)
      return null
    }
  }

  /**
   * Unsubscribe from push notifications
   */
  async unsubscribeFromPush(): Promise<boolean> {
    try {
      if (!this.registration) {
        return false
      }

      const subscription = await this.registration.pushManager.getSubscription()
      if (subscription) {
        await subscription.unsubscribe()
        await this.removePushSubscription()
        this.subscription = null
        console.log('[PushService] Unsubscribed from push notifications')
        return true
      }

      return false
    } catch (error) {
      console.error('[PushService] Error unsubscribing from push:', error)
      return false
    }
  }

  /**
   * Send a local notification (for testing or immediate feedback)
   */
  async sendLocalNotification(payload: NotificationPayload): Promise<void> {
    try {
      if (Notification.permission !== 'granted') {
        console.warn('[PushService] Notification permission not granted')
        return
      }

      // Check if notification should be sent based on preferences
      if (!this.shouldSendNotification(payload)) {
        console.log('[PushService] Notification blocked by user preferences')
        return
      }

      if (this.registration) {
        // Use service worker to show notification
        await this.registration.showNotification(payload.title, {
          body: payload.body,
          icon: payload.icon || '/favicon.ico',
          badge: '/favicon.ico',
          tag: payload.tag || `local-${Date.now()}`,
          data: payload.data,
          requireInteraction: payload.requireInteraction || false,
          silent: payload.silent || false,
          actions: payload.actions || []
        })
      } else {
        // Fallback to browser notification
        new Notification(payload.title, {
          body: payload.body,
          icon: payload.icon || '/favicon.ico',
          tag: payload.tag || `local-${Date.now()}`,
          data: payload.data,
          requireInteraction: payload.requireInteraction || false,
          silent: payload.silent || false
        })
      }

      console.log('[PushService] Local notification sent successfully')
    } catch (error) {
      console.error('[PushService] Error sending local notification:', error)
    }
  }

  /**
   * Update notification preferences
   */
  async updatePreferences(newPreferences: Partial<NotificationPreferences>): Promise<void> {
    try {
      this.preferences = { ...this.preferences, ...newPreferences }
      await this.savePreferences()
      console.log('[PushService] Preferences updated successfully')
    } catch (error) {
      console.error('[PushService] Error updating preferences:', error)
    }
  }

  /**
   * Get current notification preferences
   */
  getPreferences(): NotificationPreferences {
    return { ...this.preferences }
  }

  /**
   * Check if service is supported and ready
   */
  isSupported(): boolean {
    return 'serviceWorker' in navigator && 'PushManager' in window && 'Notification' in window
  }

  /**
   * Get current notification permission status
   */
  getPermissionStatus(): NotificationPermission {
    return Notification.permission
  }

  /**
   * Get current push subscription
   */
  getCurrentSubscription(): PushSubscription | null {
    return this.subscription
  }

  /**
   * Check if notifications should be sent based on preferences
   */
  private shouldSendNotification(payload: NotificationPayload): boolean {
    if (!this.preferences.enabled) {
      return false
    }

    // Check type-specific preferences
    switch (payload.type) {
      case 'quote_status':
        if (!this.preferences.quote_updates) return false
        break
      case 'trip_update':
        if (!this.preferences.trip_updates) return false
        break
      case 'offer_received':
        if (!this.preferences.offer_notifications) return false
        break
      case 'system_alert':
        if (!this.preferences.system_alerts) return false
        break
    }

    // Check quiet hours
    if (this.preferences.quiet_hours.enabled) {
      const now = new Date()
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
      
      const { start, end } = this.preferences.quiet_hours
      
      if (start <= end) {
        // Same day quiet hours (e.g., 22:00 to 23:59)
        if (currentTime >= start && currentTime <= end) {
          return payload.priority === 'urgent'
        }
      } else {
        // Overnight quiet hours (e.g., 22:00 to 08:00)
        if (currentTime >= start || currentTime <= end) {
          return payload.priority === 'urgent'
        }
      }
    }

    return true
  }

  /**
   * Save push subscription to backend
   */
  private async savePushSubscription(subscription: PushSubscription): Promise<void> {
    try {
      const supabase = getSupabaseClient()
      const webSocketAuth = getWebSocketAuth()

      if (!supabase || !webSocketAuth.isAuthenticated()) {
        console.warn('[PushService] Cannot save subscription: not authenticated')
        return
      }

      const { error } = await supabase
        .from('push_subscriptions')
        .upsert({
          user_id: webSocketAuth.getUserId(),
          endpoint: subscription.endpoint,
          p256dh_key: subscription.keys.p256dh,
          auth_key: subscription.keys.auth,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (error) {
        console.error('[PushService] Error saving subscription:', error)
      } else {
        console.log('[PushService] Push subscription saved successfully')
      }
    } catch (error) {
      console.error('[PushService] Error saving push subscription:', error)
    }
  }

  /**
   * Remove push subscription from backend
   */
  private async removePushSubscription(): Promise<void> {
    try {
      const supabase = getSupabaseClient()
      const webSocketAuth = getWebSocketAuth()

      if (!supabase || !webSocketAuth.isAuthenticated()) {
        return
      }

      const { error } = await supabase
        .from('push_subscriptions')
        .delete()
        .eq('user_id', webSocketAuth.getUserId())

      if (error) {
        console.error('[PushService] Error removing subscription:', error)
      } else {
        console.log('[PushService] Push subscription removed successfully')
      }
    } catch (error) {
      console.error('[PushService] Error removing push subscription:', error)
    }
  }

  /**
   * Load user preferences from storage
   */
  private async loadPreferences(): Promise<void> {
    try {
      const stored = localStorage.getItem('transflow-notification-preferences')
      if (stored) {
        this.preferences = { ...this.preferences, ...JSON.parse(stored) }
      }
    } catch (error) {
      console.error('[PushService] Error loading preferences:', error)
    }
  }

  /**
   * Save user preferences to storage
   */
  private async savePreferences(): Promise<void> {
    try {
      localStorage.setItem('transflow-notification-preferences', JSON.stringify(this.preferences))
    } catch (error) {
      console.error('[PushService] Error saving preferences:', error)
    }
  }

  /**
   * Set up message listener for service worker communication
   */
  private setupMessageListener(): void {
    navigator.serviceWorker.addEventListener('message', (event) => {
      const { type, data } = event.data

      switch (type) {
        case 'NOTIFICATION_CLICKED':
          console.log('[PushService] Notification clicked:', data)
          // Handle notification click events
          break
        case 'NOTIFICATION_EVENT':
          console.log('[PushService] Notification event:', data)
          // Track notification events
          break
        default:
          console.log('[PushService] Unknown message from SW:', event.data)
      }
    })
  }

  /**
   * Convert VAPID key to Uint8Array
   */
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4)
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/')

    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    return outputArray
  }
}

// Singleton instance
let pushNotificationService: PushNotificationService | null = null

export function getPushNotificationService(): PushNotificationService {
  if (!pushNotificationService) {
    pushNotificationService = new PushNotificationService()
  }
  return pushNotificationService
}

export { PushNotificationService }
