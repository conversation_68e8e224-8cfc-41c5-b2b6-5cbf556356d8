import { useState, useEffect, useCallback } from 'react'
import { 
  getNotificationManager, 
  NotificationHistory 
} from '@/lib/notifications/notification-manager'
import { 
  getPushNotificationService, 
  NotificationPreferences 
} from '@/lib/notifications/push-service'

export interface UseNotificationsOptions {
  autoRequestPermission?: boolean
  enablePushNotifications?: boolean
}

export interface UseNotificationsReturn {
  // Permission state
  permission: NotificationPermission
  isSupported: boolean
  
  // Notification history
  notifications: NotificationHistory[]
  unreadCount: number
  
  // Preferences
  preferences: NotificationPreferences
  
  // Actions
  requestPermission: () => Promise<NotificationPermission>
  sendTestNotification: () => Promise<void>
  markAsRead: (id: string) => void
  markAllAsRead: () => void
  clearHistory: () => void
  updatePreferences: (prefs: Partial<NotificationPreferences>) => Promise<void>
  
  // Push subscription
  subscribeToPush: () => Promise<boolean>
  unsubscribeFromPush: () => Promise<boolean>
  isSubscribedToPush: boolean
}

/**
 * Hook for managing notifications and push subscriptions
 */
export function useNotifications(options: UseNotificationsOptions = {}): UseNotificationsReturn {
  const {
    autoRequestPermission = false,
    enablePushNotifications = true
  } = options

  const [permission, setPermission] = useState<NotificationPermission>('default')
  const [notifications, setNotifications] = useState<NotificationHistory[]>([])
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    enabled: true,
    quote_updates: true,
    trip_updates: true,
    offer_notifications: true,
    system_alerts: true,
    quiet_hours: {
      enabled: false,
      start: '22:00',
      end: '08:00'
    },
    sound_enabled: true,
    vibration_enabled: true
  })
  const [isSubscribedToPush, setIsSubscribedToPush] = useState(false)

  const notificationManager = getNotificationManager()
  const pushService = getPushNotificationService()

  // Initialize notifications
  useEffect(() => {
    const initializeNotifications = async () => {
      // Check if notifications are supported
      if (!pushService.isSupported()) {
        console.warn('[useNotifications] Notifications not supported')
        return
      }

      // Get current permission status
      const currentPermission = pushService.getPermissionStatus()
      setPermission(currentPermission)

      // Load preferences
      const currentPreferences = pushService.getPreferences()
      setPreferences(currentPreferences)

      // Load notification history
      const history = notificationManager.getNotificationHistory()
      setNotifications(history)

      // Check push subscription status
      const subscription = pushService.getCurrentSubscription()
      setIsSubscribedToPush(!!subscription)

      // Auto-request permission if enabled and permission is default
      if (autoRequestPermission && currentPermission === 'default') {
        await requestPermission()
      }

      console.log('[useNotifications] Notifications initialized')
    }

    initializeNotifications()
  }, [autoRequestPermission])

  // Set up notification listener
  useEffect(() => {
    const handleNotificationUpdate = (notification: NotificationHistory) => {
      setNotifications(prev => {
        const updated = [...prev]
        const existingIndex = updated.findIndex(n => n.id === notification.id)
        
        if (existingIndex >= 0) {
          updated[existingIndex] = notification
        } else {
          updated.unshift(notification)
        }
        
        return updated
      })
    }

    notificationManager.addListener(handleNotificationUpdate)

    return () => {
      notificationManager.removeListener(handleNotificationUpdate)
    }
  }, [notificationManager])

  // Request notification permission
  const requestPermission = useCallback(async (): Promise<NotificationPermission> => {
    try {
      const newPermission = await notificationManager.requestPermission()
      setPermission(newPermission)
      
      if (newPermission === 'granted' && enablePushNotifications) {
        await subscribeToPush()
      }
      
      return newPermission
    } catch (error) {
      console.error('[useNotifications] Error requesting permission:', error)
      return 'denied'
    }
  }, [enablePushNotifications])

  // Subscribe to push notifications
  const subscribeToPush = useCallback(async (): Promise<boolean> => {
    try {
      if (permission !== 'granted') {
        console.warn('[useNotifications] Cannot subscribe: permission not granted')
        return false
      }

      const subscription = await pushService.subscribeToPush()
      const success = !!subscription
      setIsSubscribedToPush(success)
      
      if (success) {
        console.log('[useNotifications] Successfully subscribed to push notifications')
      }
      
      return success
    } catch (error) {
      console.error('[useNotifications] Error subscribing to push:', error)
      return false
    }
  }, [permission, pushService])

  // Unsubscribe from push notifications
  const unsubscribeFromPush = useCallback(async (): Promise<boolean> => {
    try {
      const success = await pushService.unsubscribeFromPush()
      setIsSubscribedToPush(!success)
      
      if (success) {
        console.log('[useNotifications] Successfully unsubscribed from push notifications')
      }
      
      return success
    } catch (error) {
      console.error('[useNotifications] Error unsubscribing from push:', error)
      return false
    }
  }, [pushService])

  // Send test notification
  const sendTestNotification = useCallback(async (): Promise<void> => {
    try {
      await notificationManager.sendNotification('system_alert', {
        title: 'Test Notification',
        message: 'This is a test notification from Transflow'
      }, {
        priority: 'medium',
        requireInteraction: false
      })
      
      console.log('[useNotifications] Test notification sent')
    } catch (error) {
      console.error('[useNotifications] Error sending test notification:', error)
    }
  }, [notificationManager])

  // Mark notification as read
  const markAsRead = useCallback((id: string) => {
    notificationManager.markAsRead(id)
  }, [notificationManager])

  // Mark all notifications as read
  const markAllAsRead = useCallback(() => {
    notificationManager.markAllAsRead()
    setNotifications(prev => prev.map(n => ({ ...n, read: true })))
  }, [notificationManager])

  // Clear notification history
  const clearHistory = useCallback(() => {
    notificationManager.clearHistory()
    setNotifications([])
  }, [notificationManager])

  // Update preferences
  const updatePreferences = useCallback(async (newPreferences: Partial<NotificationPreferences>) => {
    try {
      await notificationManager.updatePreferences(newPreferences)
      const updatedPreferences = pushService.getPreferences()
      setPreferences(updatedPreferences)
      
      console.log('[useNotifications] Preferences updated successfully')
    } catch (error) {
      console.error('[useNotifications] Error updating preferences:', error)
    }
  }, [notificationManager, pushService])

  // Calculate unread count
  const unreadCount = notifications.filter(n => !n.read).length

  return {
    // Permission state
    permission,
    isSupported: pushService.isSupported(),
    
    // Notification history
    notifications,
    unreadCount,
    
    // Preferences
    preferences,
    
    // Actions
    requestPermission,
    sendTestNotification,
    markAsRead,
    markAllAsRead,
    clearHistory,
    updatePreferences,
    
    // Push subscription
    subscribeToPush,
    unsubscribeFromPush,
    isSubscribedToPush
  }
}

/**
 * Hook for notification preferences only
 */
export function useNotificationPreferences() {
  const {
    preferences,
    updatePreferences,
    permission,
    requestPermission,
    subscribeToPush,
    unsubscribeFromPush,
    isSubscribedToPush
  } = useNotifications()

  return {
    preferences,
    updatePreferences,
    permission,
    requestPermission,
    subscribeToPush,
    unsubscribeFromPush,
    isSubscribedToPush
  }
}

/**
 * Hook for notification history only
 */
export function useNotificationHistory() {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    clearHistory
  } = useNotifications()

  return {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    clearHistory
  }
}
