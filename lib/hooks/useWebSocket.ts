import { useEffect, useState, useCallback, useRef } from 'react'
import { 
  getConnectionManager, 
  ConnectionState, 
  WebSocketEventHandler,
  ChannelSubscription 
} from '@/lib/websocket/connection-manager'
import { 
  getWebSocketAuth, 
  WebSocketAuthState,
  hasWebSocketPermission,
  validateSubscriptionPermissions,
  createAuthenticatedChannelName
} from '@/lib/websocket/auth'

export interface UseWebSocketOptions {
  autoConnect?: boolean
  requireAuth?: boolean
  permissions?: string[]
}

export interface WebSocketHookState {
  connectionState: ConnectionState
  authState: WebSocketAuthState
  isReady: boolean
  error?: string
}

export interface WebSocketSubscription {
  channelName: string
  handlers: WebSocketEventHandler[]
  subscription?: ChannelSubscription
}

/**
 * Enhanced WebSocket hook with authentication and connection management
 */
export function useWebSocket(options: UseWebSocketOptions = {}) {
  const {
    autoConnect = true,
    requireAuth = true,
    permissions = []
  } = options

  const [state, setState] = useState<WebSocketHookState>({
    connectionState: { status: 'disconnected', reconnectAttempts: 0, maxReconnectAttempts: 5 },
    authState: { isAuthenticated: false },
    isReady: false
  })

  const subscriptionsRef = useRef<Map<string, WebSocketSubscription>>(new Map())
  const connectionManagerRef = useRef(getConnectionManager())
  const webSocketAuthRef = useRef(getWebSocketAuth())

  // Update state when connection or auth changes
  const updateState = useCallback(() => {
    const connectionState = connectionManagerRef.current.getConnectionState()
    const authState = webSocketAuthRef.current.getAuthState()

    const isReady = requireAuth
      ? authState.isAuthenticated && connectionState.status === 'connected'
      : connectionState.status === 'connected'

    // Check permissions if specified
    let hasRequiredPermissions = true
    if (permissions.length > 0 && authState.isAuthenticated) {
      hasRequiredPermissions = permissions.every(permission =>
        hasWebSocketPermission(permission, authState.roles)
      )
    }

    setState(prevState => {
      // Only update if state actually changed
      const newState = {
        connectionState,
        authState,
        isReady: isReady && hasRequiredPermissions,
        error: !hasRequiredPermissions ? 'Insufficient permissions' : undefined
      }

      // Deep comparison to prevent unnecessary updates
      if (
        prevState.connectionState.status === newState.connectionState.status &&
        prevState.authState.isAuthenticated === newState.authState.isAuthenticated &&
        prevState.isReady === newState.isReady &&
        prevState.error === newState.error
      ) {
        return prevState // No change, return previous state
      }

      return newState
    })
  }, [requireAuth, permissions])

  // Set up listeners
  useEffect(() => {
    const connectionManager = connectionManagerRef.current
    const webSocketAuth = webSocketAuthRef.current

    connectionManager.addListener(updateState)
    webSocketAuth.addListener(updateState)

    // Initial state update
    updateState()

    return () => {
      connectionManager.removeListener(updateState)
      webSocketAuth.removeListener(updateState)
    }
  }, [updateState])

  // Auto-connect if enabled
  useEffect(() => {
    if (autoConnect && state.authState.isAuthenticated) {
      const connectionManager = connectionManagerRef.current
      if (!connectionManager.isConnected()) {
        connectionManager.connect().catch(error => {
          console.error('[useWebSocket] Auto-connect failed:', error)
        })
      }
    }
  }, [autoConnect, state.authState.isAuthenticated])

  // Connect function
  const connect = useCallback(async (): Promise<void> => {
    if (requireAuth && !state.authState.isAuthenticated) {
      throw new Error('Authentication required for WebSocket connection')
    }

    return connectionManagerRef.current.connect()
  }, [requireAuth, state.authState.isAuthenticated])

  // Disconnect function
  const disconnect = useCallback(() => {
    connectionManagerRef.current.disconnect()
  }, [])

  // Subscribe to a channel
  const subscribe = useCallback((
    channelName: string,
    handlers: WebSocketEventHandler[],
    options: { authenticated?: boolean } = {}
  ): (() => void) | null => {
    const { authenticated = false } = options

    // Check if ready
    if (!state.isReady) {
      console.warn('[useWebSocket] Cannot subscribe: WebSocket not ready')
      return null
    }

    // Validate permissions
    if (!validateSubscriptionPermissions(channelName, state.authState.roles)) {
      console.error('[useWebSocket] Insufficient permissions for channel:', channelName)
      return null
    }

    // Create authenticated channel name if needed
    const finalChannelName = authenticated 
      ? createAuthenticatedChannelName(channelName, state.authState.userId)
      : channelName

    // Remove existing subscription if it exists
    const existingSubscription = subscriptionsRef.current.get(finalChannelName)
    if (existingSubscription?.subscription) {
      existingSubscription.subscription.unsubscribe()
    }

    // Create new subscription
    const subscription = connectionManagerRef.current.subscribe(finalChannelName, handlers)
    
    if (subscription) {
      subscriptionsRef.current.set(finalChannelName, {
        channelName: finalChannelName,
        handlers,
        subscription
      })

      // Return unsubscribe function
      return () => {
        subscription.unsubscribe()
        subscriptionsRef.current.delete(finalChannelName)
      }
    }

    return null
  }, [state.isReady, state.authState.roles, state.authState.userId])

  // Unsubscribe from a channel
  const unsubscribe = useCallback((channelName: string) => {
    const subscription = subscriptionsRef.current.get(channelName)
    if (subscription?.subscription) {
      subscription.subscription.unsubscribe()
      subscriptionsRef.current.delete(channelName)
    }
  }, [])

  // Unsubscribe from all channels
  const unsubscribeAll = useCallback(() => {
    subscriptionsRef.current.forEach(subscription => {
      if (subscription.subscription) {
        subscription.subscription.unsubscribe()
      }
    })
    subscriptionsRef.current.clear()
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      unsubscribeAll()
    }
  }, [unsubscribeAll])

  return {
    // State
    connectionState: state.connectionState,
    authState: state.authState,
    isReady: state.isReady,
    isConnected: state.connectionState.status === 'connected',
    isAuthenticated: state.authState.isAuthenticated,
    error: state.error,

    // Actions
    connect,
    disconnect,
    subscribe,
    unsubscribe,
    unsubscribeAll,

    // Utilities
    hasPermission: (permission: string) => 
      hasWebSocketPermission(permission, state.authState.roles),
    getUserId: () => state.authState.userId,
    getUserRoles: () => state.authState.roles || []
  }
}

/**
 * Hook for subscribing to quote updates
 */
export function useQuoteUpdates(quoteId?: string) {
  const webSocket = useWebSocket({
    requireAuth: true,
    permissions: ['quote_updates']
  })

  const [quoteData, setQuoteData] = useState<any>(null)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)

  const subscribeToQuote = useCallback((id: string, callback?: (data: any) => void) => {
    if (!webSocket.isReady) return null

    const handlers: WebSocketEventHandler[] = [
      {
        event: 'postgres_changes',
        callback: (payload) => {
          console.log('[useQuoteUpdates] Quote update received:', payload)
          setQuoteData(payload.new)
          setLastUpdate(new Date())
          callback?.(payload.new)
        }
      }
    ]

    return webSocket.subscribe(`quotes:${id}`, handlers, { authenticated: true })
  }, [webSocket])

  useEffect(() => {
    if (quoteId && webSocket.isReady) {
      return subscribeToQuote(quoteId)
    }
  }, [quoteId, webSocket.isReady])

  return {
    ...webSocket,
    quoteData,
    lastUpdate,
    subscribeToQuote
  }
}

/**
 * Hook for subscribing to quote offers
 */
export function useQuoteOffers(quoteId?: string) {
  const webSocket = useWebSocket({
    requireAuth: true,
    permissions: ['quote_offers']
  })

  const [offers, setOffers] = useState<any[]>([])

  useEffect(() => {
    if (!quoteId || !webSocket.isReady) return

    const handlers: WebSocketEventHandler[] = [
      {
        event: 'postgres_changes',
        callback: (payload) => {
          console.log('[useQuoteOffers] Offer update received:', payload)
          if (payload.eventType === 'INSERT') {
            setOffers(prev => [...prev, payload.new])
          } else if (payload.eventType === 'UPDATE') {
            setOffers(prev => prev.map(offer => 
              offer.id === payload.new.id ? payload.new : offer
            ))
          }
        }
      }
    ]

    return webSocket.subscribe(`quote_offers:${quoteId}`, handlers)
  }, [quoteId, webSocket.isReady, webSocket])

  return {
    ...webSocket,
    offers,
    clearOffers: () => setOffers([])
  }
}
