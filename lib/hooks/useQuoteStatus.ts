import { useState, useEffect, useCallback, useRef } from 'react'
import { useWebSocket } from './useWebSocket'
import { 
  getQuoteStatusBroadcaster, 
  QuoteStatusUpdate, 
  QuoteStatusNotification,
  triggerQuoteStatusChange
} from '@/lib/api/quote-status-broadcast'
import { toast } from 'sonner'

export interface QuoteStatus {
  id: string
  status: string
  updated_at: string
  updated_by: string
  metadata?: Record<string, any>
}

export interface UseQuoteStatusOptions {
  quoteId?: string
  autoSubscribe?: boolean
  showNotifications?: boolean
  onStatusChange?: (update: QuoteStatusUpdate) => void
}

export interface UseQuoteStatusReturn {
  // State
  currentStatus: string | null
  statusHistory: QuoteStatusUpdate[]
  isLoading: boolean
  error: string | null
  lastUpdate: Date | null

  // Actions
  updateStatus: (newStatus: string, metadata?: Record<string, any>) => Promise<void>
  subscribeToQuote: (quoteId: string) => (() => void) | null
  unsubscribeFromQuote: () => void
  clearHistory: () => void

  // WebSocket state
  isConnected: boolean
  connectionState: any
}

/**
 * Hook for managing real-time quote status updates
 */
export function useQuoteStatus(options: UseQuoteStatusOptions = {}): UseQuoteStatusReturn {
  const {
    quoteId,
    autoSubscribe = true,
    showNotifications = true,
    onStatusChange
  } = options

  const webSocket = useWebSocket({
    requireAuth: true,
    permissions: ['quote_updates']
  })

  const [currentStatus, setCurrentStatus] = useState<string | null>(null)
  const [statusHistory, setStatusHistory] = useState<QuoteStatusUpdate[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)

  const broadcasterRef = useRef(getQuoteStatusBroadcaster())
  const unsubscribeRef = useRef<(() => void) | null>(null)

  // Handle status updates - use useRef to avoid dependency issues
  const onStatusChangeRef = useRef(onStatusChange)
  const showNotificationsRef = useRef(showNotifications)

  // Update refs when props change
  useEffect(() => {
    onStatusChangeRef.current = onStatusChange
    showNotificationsRef.current = showNotifications
  }, [onStatusChange, showNotifications])

  // Handle status updates
  const handleStatusUpdate = useCallback((update: QuoteStatusUpdate) => {
    console.log('[useQuoteStatus] Received status update:', update)

    setCurrentStatus(update.new_status)
    setStatusHistory(prev => [update, ...prev.slice(0, 49)]) // Keep last 50 updates
    setLastUpdate(new Date())
    setError(null)

    // Show notification if enabled
    if (showNotificationsRef.current) {
      const statusMessages: Record<string, string> = {
        'pending': '⏳ Quote is pending review',
        'rate_requested': '📋 Rate requested from affiliates',
        'quote_ready': '✅ Quote is ready for review',
        'accepted': '🎉 Quote has been accepted',
        'rejected': '❌ Quote has been rejected',
        'completed': '✅ Quote completed successfully',
        'cancelled': '🚫 Quote has been cancelled'
      }

      const message = statusMessages[update.new_status] || `Status changed to ${update.new_status}`

      toast.success(message, {
        description: `Quote ${update.quote_id}`,
        duration: 4000
      })
    }

    // Call custom callback if provided
    onStatusChangeRef.current?.(update)
  }, [])

  // Subscribe to quote status updates
  const subscribeToQuote = useCallback((targetQuoteId: string): (() => void) | null => {
    if (!webSocket.isReady) {
      console.warn('[useQuoteStatus] Cannot subscribe: WebSocket not ready')
      return null
    }

    // Unsubscribe from previous subscription
    if (unsubscribeRef.current) {
      unsubscribeRef.current()
    }

    console.log('[useQuoteStatus] Subscribing to quote:', targetQuoteId)

    const unsubscribe = broadcasterRef.current.subscribeToQuoteStatus(
      targetQuoteId,
      handleStatusUpdate
    )

    unsubscribeRef.current = unsubscribe
    return unsubscribe
  }, [webSocket.isReady])

  // Unsubscribe from current quote
  const unsubscribeFromQuote = useCallback(() => {
    if (unsubscribeRef.current) {
      unsubscribeRef.current()
      unsubscribeRef.current = null
    }
  }, [])

  // Update quote status
  const updateStatus = useCallback(async (
    newStatus: string, 
    metadata?: Record<string, any>
  ): Promise<void> => {
    if (!quoteId) {
      throw new Error('No quote ID provided')
    }

    if (!webSocket.isAuthenticated) {
      throw new Error('User not authenticated')
    }

    setIsLoading(true)
    setError(null)

    try {
      console.log('[useQuoteStatus] Updating status:', { quoteId, newStatus, metadata })
      
      // Optimistic update
      const optimisticUpdate: QuoteStatusUpdate = {
        id: `${quoteId}_${Date.now()}`,
        quote_id: quoteId,
        old_status: currentStatus || 'unknown',
        new_status: newStatus,
        updated_by: webSocket.getUserId() || 'current_user',
        updated_at: new Date().toISOString(),
        metadata,
        user_roles: webSocket.getUserRoles()
      }

      // Apply optimistic update
      handleStatusUpdate(optimisticUpdate)

      // Trigger the actual status change
      await triggerQuoteStatusChange(quoteId, newStatus, metadata)

      console.log('[useQuoteStatus] Status update completed')
    } catch (err) {
      console.error('[useQuoteStatus] Error updating status:', err)
      setError(err instanceof Error ? err.message : 'Failed to update status')
      
      // Revert optimistic update on error
      if (currentStatus) {
        setCurrentStatus(currentStatus)
      }
      
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [quoteId, currentStatus, webSocket, handleStatusUpdate])

  // Clear status history
  const clearHistory = useCallback(() => {
    setStatusHistory([])
    setLastUpdate(null)
  }, [])

  // Auto-subscribe effect
  useEffect(() => {
    if (autoSubscribe && quoteId && webSocket.isReady) {
      console.log('[useQuoteStatus] Auto-subscribing to quote:', quoteId)
      const unsubscribe = subscribeToQuote(quoteId)

      return () => {
        if (unsubscribe) {
          unsubscribe()
        }
      }
    }
  }, [autoSubscribe, quoteId, webSocket.isReady])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
        unsubscribeRef.current = null
      }
    }
  }, [])

  return {
    // State
    currentStatus,
    statusHistory,
    isLoading,
    error,
    lastUpdate,

    // Actions
    updateStatus,
    subscribeToQuote,
    unsubscribeFromQuote,
    clearHistory,

    // WebSocket state
    isConnected: webSocket.isConnected,
    connectionState: webSocket.connectionState
  }
}

/**
 * Hook for subscribing to all quote status updates (for dashboards)
 */
export function useAllQuoteStatus(options: { 
  showNotifications?: boolean
  onStatusChange?: (update: QuoteStatusUpdate) => void
} = {}) {
  const { showNotifications = false, onStatusChange } = options
  
  const webSocket = useWebSocket({
    requireAuth: true,
    permissions: ['quote_updates']
  })

  const [allUpdates, setAllUpdates] = useState<QuoteStatusUpdate[]>([])
  const [lastGlobalUpdate, setLastGlobalUpdate] = useState<Date | null>(null)

  const broadcasterRef = useRef(getQuoteStatusBroadcaster())
  const unsubscribeRef = useRef<(() => void) | null>(null)

  // Handle global status updates
  // Use refs to avoid dependency issues
  const onStatusChangeRef = useRef(onStatusChange)
  const showNotificationsRef = useRef(showNotifications)

  useEffect(() => {
    onStatusChangeRef.current = onStatusChange
    showNotificationsRef.current = showNotifications
  }, [onStatusChange, showNotifications])

  const handleGlobalStatusUpdate = useCallback((update: QuoteStatusUpdate) => {
    console.log('[useAllQuoteStatus] Received global status update:', update)

    setAllUpdates(prev => [update, ...prev.slice(0, 99)]) // Keep last 100 updates
    setLastGlobalUpdate(new Date())

    // Show notification if enabled
    if (showNotificationsRef.current) {
      toast.info(`Quote ${update.quote_id} status: ${update.new_status}`, {
        duration: 3000
      })
    }

    // Call custom callback if provided
    onStatusChangeRef.current?.(update)
  }, [])

  // Subscribe to all quote updates
  useEffect(() => {
    if (webSocket.isReady) {
      console.log('[useAllQuoteStatus] Subscribing to all quote updates')

      const unsubscribe = broadcasterRef.current.subscribeToAllQuoteUpdates(
        handleGlobalStatusUpdate
      )

      unsubscribeRef.current = unsubscribe

      return () => {
        if (unsubscribe) {
          unsubscribe()
        }
      }
    }
  }, [webSocket.isReady])

  return {
    allUpdates,
    lastGlobalUpdate,
    isConnected: webSocket.isConnected,
    clearUpdates: () => setAllUpdates([])
  }
}
