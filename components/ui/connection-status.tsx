"use client"

import { useState, useEffect } from 'react'
import { Badge } from '@/app/components/ui/badge'
import { Button } from '@/app/components/ui/button'
import { 
  <PERSON>lt<PERSON>, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from '@/app/components/ui/tooltip'
import { 
  Wifi, 
  WifiOff, 
  Loader2, 
  AlertTriangle, 
  RefreshCw,
  CheckCircle
} from 'lucide-react'
import { useWebSocket } from '@/lib/hooks/useWebSocket'
import { cn } from '@/lib/utils'

interface ConnectionStatusProps {
  className?: string
  showText?: boolean
  showRetryButton?: boolean
  size?: 'sm' | 'md' | 'lg'
}

export function ConnectionStatus({ 
  className, 
  showText = false, 
  showRetryButton = false,
  size = 'md'
}: ConnectionStatusProps) {
  const { connectionState, authState, isReady, connect } = useWebSocket({
    autoConnect: true,
    requireAuth: true
  })

  const [isRetrying, setIsRetrying] = useState(false)

  const handleRetry = async () => {
    setIsRetrying(true)
    try {
      await connect()
    } catch (error) {
      console.error('Failed to reconnect:', error)
    } finally {
      setIsRetrying(false)
    }
  }

  const getStatusInfo = () => {
    if (!authState.isAuthenticated) {
      return {
        status: 'unauthenticated',
        icon: WifiOff,
        color: 'bg-gray-500',
        text: 'Not authenticated',
        description: 'User authentication required for real-time updates'
      }
    }

    switch (connectionState.status) {
      case 'connected':
        return {
          status: 'connected',
          icon: CheckCircle,
          color: 'bg-green-500',
          text: 'Connected',
          description: 'Real-time updates active'
        }
      case 'connecting':
        return {
          status: 'connecting',
          icon: Loader2,
          color: 'bg-blue-500',
          text: 'Connecting...',
          description: 'Establishing connection...'
        }
      case 'reconnecting':
        return {
          status: 'reconnecting',
          icon: RefreshCw,
          color: 'bg-yellow-500',
          text: `Reconnecting... (${connectionState.reconnectAttempts}/${connectionState.maxReconnectAttempts})`,
          description: 'Attempting to restore connection'
        }
      case 'error':
        return {
          status: 'error',
          icon: AlertTriangle,
          color: 'bg-red-500',
          text: 'Connection error',
          description: connectionState.error || 'Connection failed'
        }
      case 'disconnected':
      default:
        return {
          status: 'disconnected',
          icon: WifiOff,
          color: 'bg-gray-500',
          text: 'Disconnected',
          description: 'Real-time updates unavailable'
        }
    }
  }

  const statusInfo = getStatusInfo()
  const IconComponent = statusInfo.icon

  const iconSize = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  }[size]

  const badgeSize = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-2.5 py-1',
    lg: 'text-base px-3 py-1.5'
  }[size]

  if (!showText) {
    // Icon-only version
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className={cn('flex items-center', className)}>
              <div className={cn(
                'rounded-full p-1.5 flex items-center justify-center',
                statusInfo.color
              )}>
                <IconComponent 
                  className={cn(
                    iconSize, 
                    'text-white',
                    (statusInfo.status === 'connecting' || statusInfo.status === 'reconnecting') && 'animate-spin'
                  )} 
                />
              </div>
              {showRetryButton && statusInfo.status === 'error' && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRetry}
                  disabled={isRetrying}
                  className="ml-2 h-6 px-2"
                >
                  {isRetrying ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <RefreshCw className="h-3 w-3" />
                  )}
                </Button>
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-center">
              <div className="font-medium">{statusInfo.text}</div>
              <div className="text-xs text-muted-foreground mt-1">
                {statusInfo.description}
              </div>
              {connectionState.lastConnected && (
                <div className="text-xs text-muted-foreground mt-1">
                  Last connected: {connectionState.lastConnected.toLocaleTimeString()}
                </div>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  // Badge version with text
  return (
    <div className={cn('flex items-center gap-2', className)}>
      <Badge 
        variant={statusInfo.status === 'connected' ? 'default' : 'secondary'}
        className={cn(
          'flex items-center gap-1.5',
          badgeSize,
          statusInfo.status === 'connected' && 'bg-green-100 text-green-800 border-green-200',
          statusInfo.status === 'error' && 'bg-red-100 text-red-800 border-red-200',
          statusInfo.status === 'reconnecting' && 'bg-yellow-100 text-yellow-800 border-yellow-200'
        )}
      >
        <IconComponent 
          className={cn(
            iconSize,
            (statusInfo.status === 'connecting' || statusInfo.status === 'reconnecting') && 'animate-spin'
          )} 
        />
        {statusInfo.text}
      </Badge>
      
      {showRetryButton && statusInfo.status === 'error' && (
        <Button
          variant="outline"
          size="sm"
          onClick={handleRetry}
          disabled={isRetrying}
          className="h-7 px-2"
        >
          {isRetrying ? (
            <>
              <Loader2 className="h-3 w-3 animate-spin mr-1" />
              Retrying...
            </>
          ) : (
            <>
              <RefreshCw className="h-3 w-3 mr-1" />
              Retry
            </>
          )}
        </Button>
      )}
    </div>
  )
}

/**
 * Compact connection status for headers/navigation
 */
export function CompactConnectionStatus({ className }: { className?: string }) {
  return (
    <ConnectionStatus 
      className={className}
      showText={false}
      showRetryButton={false}
      size="sm"
    />
  )
}

/**
 * Detailed connection status for dashboards
 */
export function DetailedConnectionStatus({ className }: { className?: string }) {
  return (
    <ConnectionStatus 
      className={className}
      showText={true}
      showRetryButton={true}
      size="md"
    />
  )
}
