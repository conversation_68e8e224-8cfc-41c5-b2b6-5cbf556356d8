"use client"

import { useState, useEffect } from 'react'
import { Badge } from '@/app/components/ui/badge'
import { Button } from '@/app/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from '@/app/components/ui/tooltip'
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Loader2,
  RefreshCw,
  Bell,
  BellOff,
  History,
  Activity
} from 'lucide-react'
import { useQuoteStatus } from '@/lib/hooks/useQuoteStatus'
import { cn } from '@/lib/utils'
import { formatDistanceToNow } from 'date-fns'

interface RealTimeStatusProps {
  quoteId: string
  className?: string
  showHistory?: boolean
  showNotifications?: boolean
  size?: 'sm' | 'md' | 'lg'
}

export function RealTimeStatus({ 
  quoteId, 
  className, 
  showHistory = false,
  showNotifications = true,
  size = 'md'
}: RealTimeStatusProps) {
  const {
    currentStatus,
    statusHistory,
    isLoading,
    error,
    lastUpdate,
    updateStatus,
    isConnected
  } = useQuoteStatus({
    quoteId,
    autoSubscribe: true,
    showNotifications
  })

  const [notificationsEnabled, setNotificationsEnabled] = useState(showNotifications)

  const getStatusInfo = (status: string | null) => {
    const statusMap: Record<string, { 
      icon: any; 
      color: string; 
      bgColor: string; 
      label: string;
      description: string;
    }> = {
      'pending': {
        icon: Clock,
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-100 border-yellow-200',
        label: 'Pending',
        description: 'Quote is awaiting review'
      },
      'rate_requested': {
        icon: Loader2,
        color: 'text-blue-600',
        bgColor: 'bg-blue-100 border-blue-200',
        label: 'Rate Requested',
        description: 'Requesting rates from affiliates'
      },
      'quote_ready': {
        icon: AlertCircle,
        color: 'text-orange-600',
        bgColor: 'bg-orange-100 border-orange-200',
        label: 'Ready for Review',
        description: 'Quote is ready for customer review'
      },
      'accepted': {
        icon: CheckCircle,
        color: 'text-green-600',
        bgColor: 'bg-green-100 border-green-200',
        label: 'Accepted',
        description: 'Quote has been accepted'
      },
      'rejected': {
        icon: XCircle,
        color: 'text-red-600',
        bgColor: 'bg-red-100 border-red-200',
        label: 'Rejected',
        description: 'Quote has been rejected'
      },
      'completed': {
        icon: CheckCircle,
        color: 'text-green-700',
        bgColor: 'bg-green-200 border-green-300',
        label: 'Completed',
        description: 'Quote has been completed'
      },
      'cancelled': {
        icon: XCircle,
        color: 'text-gray-600',
        bgColor: 'bg-gray-100 border-gray-200',
        label: 'Cancelled',
        description: 'Quote has been cancelled'
      }
    }

    return statusMap[status || 'pending'] || statusMap['pending']
  }

  const statusInfo = getStatusInfo(currentStatus)
  const IconComponent = statusInfo.icon

  const iconSize = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  }[size]

  const badgeSize = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-2.5 py-1',
    lg: 'text-base px-3 py-1.5'
  }[size]

  if (error) {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        <Badge variant="destructive" className={badgeSize}>
          <XCircle className={cn(iconSize, 'mr-1')} />
          Error
        </Badge>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="ghost" size="sm" onClick={() => window.location.reload()}>
              <RefreshCw className="h-3 w-3" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Retry connection</p>
          </TooltipContent>
        </Tooltip>
      </div>
    )
  }

  return (
    <div className={cn('space-y-2', className)}>
      {/* Status Badge */}
      <div className="flex items-center gap-2">
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge
              variant="outline"
              className={cn(
                'flex items-center gap-1.5',
                badgeSize,
                statusInfo.bgColor,
                statusInfo.color
              )}
            >
              <IconComponent
                className={cn(
                  iconSize,
                  currentStatus === 'rate_requested' && 'animate-spin'
                )}
              />
              {statusInfo.label}
              {!isConnected && (
                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse ml-1" />
              )}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-center">
              <div className="font-medium">{statusInfo.description}</div>
              {lastUpdate && (
                <div className="text-xs text-muted-foreground mt-1">
                  Updated {formatDistanceToNow(lastUpdate, { addSuffix: true })}
                </div>
              )}
              {!isConnected && (
                <div className="text-xs text-red-500 mt-1">
                  Real-time updates disconnected
                </div>
              )}
            </div>
          </TooltipContent>
        </Tooltip>

        {/* Notification Toggle */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setNotificationsEnabled(!notificationsEnabled)}
              className="h-6 w-6 p-0"
            >
              {notificationsEnabled ? (
                <Bell className="h-3 w-3" />
              ) : (
                <BellOff className="h-3 w-3 text-muted-foreground" />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{notificationsEnabled ? 'Disable' : 'Enable'} notifications</p>
          </TooltipContent>
        </Tooltip>

        {/* Loading Indicator */}
        {isLoading && (
          <Loader2 className="h-3 w-3 animate-spin text-muted-foreground" />
        )}
      </div>

      {/* Status History */}
      {showHistory && statusHistory.length > 0 && (
        <Card className="mt-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <History className="h-4 w-4" />
              Status History
            </CardTitle>
            <CardDescription className="text-xs">
              Recent status changes for this quote
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {statusHistory.slice(0, 5).map((update, index) => {
                const updateStatusInfo = getStatusInfo(update.new_status)
                const UpdateIcon = updateStatusInfo.icon
                
                return (
                  <div 
                    key={update.id} 
                    className="flex items-center gap-2 text-xs p-2 rounded border"
                  >
                    <UpdateIcon className={cn('h-3 w-3', updateStatusInfo.color)} />
                    <span className="font-medium">{updateStatusInfo.label}</span>
                    <span className="text-muted-foreground ml-auto">
                      {formatDistanceToNow(new Date(update.updated_at), { addSuffix: true })}
                    </span>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

/**
 * Compact status indicator for lists and tables
 */
export function CompactRealTimeStatus({ 
  quoteId, 
  className 
}: { 
  quoteId: string
  className?: string 
}) {
  return (
    <RealTimeStatus 
      quoteId={quoteId}
      className={className}
      showHistory={false}
      showNotifications={false}
      size="sm"
    />
  )
}

/**
 * Detailed status card for quote details pages
 */
export function DetailedRealTimeStatus({ 
  quoteId, 
  className 
}: { 
  quoteId: string
  className?: string 
}) {
  return (
    <RealTimeStatus 
      quoteId={quoteId}
      className={className}
      showHistory={true}
      showNotifications={true}
      size="lg"
    />
  )
}

/**
 * Status timeline component for tracking quote progress
 */
export function QuoteStatusTimeline({ quoteId }: { quoteId: string }) {
  const { statusHistory, currentStatus } = useQuoteStatus({
    quoteId,
    autoSubscribe: true,
    showNotifications: false
  })

  const allStatuses = ['pending', 'rate_requested', 'quote_ready', 'accepted', 'completed']
  
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Activity className="h-4 w-4" />
        <h3 className="font-medium">Quote Progress</h3>
      </div>
      
      <div className="space-y-2">
        {allStatuses.map((status, index) => {
          const statusInfo = getStatusInfo(status)
          const IconComponent = statusInfo.icon
          const isCompleted = statusHistory.some(h => h.new_status === status)
          const isCurrent = currentStatus === status
          
          return (
            <div 
              key={status}
              className={cn(
                'flex items-center gap-3 p-2 rounded',
                isCurrent && 'bg-blue-50 border border-blue-200',
                isCompleted && !isCurrent && 'opacity-60'
              )}
            >
              <div className={cn(
                'w-6 h-6 rounded-full flex items-center justify-center',
                isCompleted || isCurrent ? statusInfo.bgColor : 'bg-gray-100'
              )}>
                <IconComponent className={cn(
                  'h-3 w-3',
                  isCompleted || isCurrent ? statusInfo.color : 'text-gray-400'
                )} />
              </div>
              
              <div className="flex-1">
                <div className={cn(
                  'font-medium text-sm',
                  isCompleted || isCurrent ? statusInfo.color : 'text-gray-400'
                )}>
                  {statusInfo.label}
                </div>
                <div className="text-xs text-muted-foreground">
                  {statusInfo.description}
                </div>
              </div>
              
              {isCurrent && (
                <Badge variant="default" className="text-xs">
                  Current
                </Badge>
              )}
            </div>
          )
        })}
      </div>
    </div>
  )
}
