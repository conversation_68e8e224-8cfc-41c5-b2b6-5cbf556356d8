"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Button } from '@/app/components/ui/button'
import { Switch } from '@/app/components/ui/switch'
import { Label } from '@/app/components/ui/label'
import { Input } from '@/app/components/ui/input'
import { Separator } from '@/app/components/ui/separator'
import { Badge } from '@/app/components/ui/badge'
import { 
  Bell, 
  BellOff, 
  Volume2, 
  VolumeX, 
  Smartphone, 
  Clock,
  CheckCircle,
  AlertCircle,
  Settings
} from 'lucide-react'
import { useNotificationPreferences } from '@/lib/hooks/useNotifications'
import { toast } from 'sonner'

interface NotificationPreferencesProps {
  className?: string
}

export function NotificationPreferences({ className }: NotificationPreferencesProps) {
  const {
    preferences,
    updatePreferences,
    permission,
    requestPermission,
    subscribeToPush,
    unsubscribeFromPush,
    isSubscribedToPush
  } = useNotificationPreferences()

  const [isLoading, setIsLoading] = useState(false)

  const handlePermissionRequest = async () => {
    setIsLoading(true)
    try {
      const newPermission = await requestPermission()
      if (newPermission === 'granted') {
        toast.success('Notification permission granted!')
      } else {
        toast.error('Notification permission denied')
      }
    } catch (error) {
      toast.error('Failed to request notification permission')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePushSubscription = async () => {
    setIsLoading(true)
    try {
      if (isSubscribedToPush) {
        const success = await unsubscribeFromPush()
        if (success) {
          toast.success('Unsubscribed from push notifications')
        } else {
          toast.error('Failed to unsubscribe from push notifications')
        }
      } else {
        const success = await subscribeToPush()
        if (success) {
          toast.success('Subscribed to push notifications!')
        } else {
          toast.error('Failed to subscribe to push notifications')
        }
      }
    } catch (error) {
      toast.error('Failed to update push subscription')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePreferenceChange = async (key: string, value: any) => {
    try {
      await updatePreferences({ [key]: value })
      toast.success('Preferences updated successfully')
    } catch (error) {
      toast.error('Failed to update preferences')
    }
  }

  const handleQuietHoursChange = async (field: 'start' | 'end', value: string) => {
    try {
      await updatePreferences({
        quiet_hours: {
          ...preferences.quiet_hours,
          [field]: value
        }
      })
      toast.success('Quiet hours updated')
    } catch (error) {
      toast.error('Failed to update quiet hours')
    }
  }

  const getPermissionStatus = () => {
    switch (permission) {
      case 'granted':
        return { icon: CheckCircle, color: 'text-green-600', text: 'Granted' }
      case 'denied':
        return { icon: AlertCircle, color: 'text-red-600', text: 'Denied' }
      default:
        return { icon: Clock, color: 'text-yellow-600', text: 'Not Requested' }
    }
  }

  const permissionStatus = getPermissionStatus()
  const PermissionIcon = permissionStatus.icon

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Notification Preferences
          </CardTitle>
          <CardDescription>
            Customize how and when you receive notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Permission Status */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="text-base font-medium">Browser Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Allow Transflow to send browser notifications
                </p>
              </div>
              <div className="flex items-center gap-2">
                <PermissionIcon className={`h-4 w-4 ${permissionStatus.color}`} />
                <Badge variant={permission === 'granted' ? 'default' : 'secondary'}>
                  {permissionStatus.text}
                </Badge>
                {permission !== 'granted' && (
                  <Button
                    onClick={handlePermissionRequest}
                    disabled={isLoading}
                    size="sm"
                  >
                    Enable
                  </Button>
                )}
              </div>
            </div>

            {/* Push Notifications */}
            {permission === 'granted' && (
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-base font-medium">Push Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications even when the app is closed
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={isSubscribedToPush ? 'default' : 'secondary'}>
                    {isSubscribedToPush ? 'Enabled' : 'Disabled'}
                  </Badge>
                  <Button
                    onClick={handlePushSubscription}
                    disabled={isLoading}
                    size="sm"
                    variant={isSubscribedToPush ? 'outline' : 'default'}
                  >
                    {isSubscribedToPush ? 'Disable' : 'Enable'}
                  </Button>
                </div>
              </div>
            )}
          </div>

          <Separator />

          {/* General Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">General Settings</h3>
            
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="notifications-enabled">Enable Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Master switch for all notifications
                </p>
              </div>
              <Switch
                id="notifications-enabled"
                checked={preferences.enabled}
                onCheckedChange={(checked) => handlePreferenceChange('enabled', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="sound-enabled">Sound</Label>
                <p className="text-sm text-muted-foreground">
                  Play sound with notifications
                </p>
              </div>
              <div className="flex items-center gap-2">
                {preferences.sound_enabled ? (
                  <Volume2 className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <VolumeX className="h-4 w-4 text-muted-foreground" />
                )}
                <Switch
                  id="sound-enabled"
                  checked={preferences.sound_enabled}
                  onCheckedChange={(checked) => handlePreferenceChange('sound_enabled', checked)}
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="vibration-enabled">Vibration</Label>
                <p className="text-sm text-muted-foreground">
                  Vibrate device for notifications (mobile)
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Smartphone className="h-4 w-4 text-muted-foreground" />
                <Switch
                  id="vibration-enabled"
                  checked={preferences.vibration_enabled}
                  onCheckedChange={(checked) => handlePreferenceChange('vibration_enabled', checked)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Notification Types */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Notification Types</h3>
            
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="quote-updates">Quote Updates</Label>
                <p className="text-sm text-muted-foreground">
                  Status changes, approvals, and rejections
                </p>
              </div>
              <Switch
                id="quote-updates"
                checked={preferences.quote_updates}
                onCheckedChange={(checked) => handlePreferenceChange('quote_updates', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="trip-updates">Trip Updates</Label>
                <p className="text-sm text-muted-foreground">
                  Driver assignments, arrivals, and trip progress
                </p>
              </div>
              <Switch
                id="trip-updates"
                checked={preferences.trip_updates}
                onCheckedChange={(checked) => handlePreferenceChange('trip_updates', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="offer-notifications">Offer Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  New offers from transportation providers
                </p>
              </div>
              <Switch
                id="offer-notifications"
                checked={preferences.offer_notifications}
                onCheckedChange={(checked) => handlePreferenceChange('offer_notifications', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="system-alerts">System Alerts</Label>
                <p className="text-sm text-muted-foreground">
                  Important system messages and updates
                </p>
              </div>
              <Switch
                id="system-alerts"
                checked={preferences.system_alerts}
                onCheckedChange={(checked) => handlePreferenceChange('system_alerts', checked)}
              />
            </div>
          </div>

          <Separator />

          {/* Quiet Hours */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Quiet Hours</h3>
            
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="quiet-hours-enabled">Enable Quiet Hours</Label>
                <p className="text-sm text-muted-foreground">
                  Reduce notifications during specified hours (urgent notifications will still come through)
                </p>
              </div>
              <Switch
                id="quiet-hours-enabled"
                checked={preferences.quiet_hours.enabled}
                onCheckedChange={(checked) => 
                  handlePreferenceChange('quiet_hours', { ...preferences.quiet_hours, enabled: checked })
                }
              />
            </div>

            {preferences.quiet_hours.enabled && (
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="quiet-start">Start Time</Label>
                  <Input
                    id="quiet-start"
                    type="time"
                    value={preferences.quiet_hours.start}
                    onChange={(e) => handleQuietHoursChange('start', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="quiet-end">End Time</Label>
                  <Input
                    id="quiet-end"
                    type="time"
                    value={preferences.quiet_hours.end}
                    onChange={(e) => handleQuietHoursChange('end', e.target.value)}
                  />
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
