"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Button } from '@/app/components/ui/button'
import { Badge } from '@/app/components/ui/badge'
import { ScrollArea } from '@/app/components/ui/scroll-area'
import { Separator } from '@/app/components/ui/separator'
import {
  Bell,
  BellOff,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Trash2,
  Check,
  History,
  Filter
} from 'lucide-react'
import { useNotificationHistory } from '@/lib/hooks/useNotifications'
import { formatDistanceToNow } from 'date-fns'
import { cn } from '@/lib/utils'

interface NotificationHistoryProps {
  className?: string
  maxHeight?: string
}

export function NotificationHistory({ className, maxHeight = "400px" }: NotificationHistoryProps) {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    clearHistory
  } = useNotificationHistory()

  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all')

  const filteredNotifications = notifications.filter(notification => {
    switch (filter) {
      case 'unread':
        return !notification.read
      case 'read':
        return notification.read
      default:
        return true
    }
  })

  const getNotificationIcon = (type: string, status: string) => {
    if (status === 'clicked') {
      return CheckCircle
    } else if (status === 'dismissed') {
      return XCircle
    }

    switch (type) {
      case 'quote_status':
        return Bell
      case 'trip_update':
        return Clock
      case 'offer_received':
        return AlertCircle
      case 'system_alert':
        return Bell
      default:
        return Bell
    }
  }

  const getNotificationColor = (type: string, priority: string) => {
    if (priority === 'urgent') {
      return 'text-red-600'
    } else if (priority === 'high') {
      return 'text-orange-600'
    } else if (type === 'quote_status') {
      return 'text-blue-600'
    } else if (type === 'trip_update') {
      return 'text-green-600'
    }
    return 'text-gray-600'
  }

  const getPriorityBadge = (priority: string) => {
    const variants: Record<string, any> = {
      urgent: 'destructive',
      high: 'default',
      medium: 'secondary',
      low: 'outline'
    }
    return variants[priority] || 'secondary'
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                Notification History
                {unreadCount > 0 && (
                  <Badge variant="destructive" className="ml-2">
                    {unreadCount}
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                View and manage your notification history
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {/* Filter Buttons */}
              <div className="flex items-center gap-1">
                <Button
                  variant={filter === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilter('all')}
                >
                  All
                </Button>
                <Button
                  variant={filter === 'unread' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilter('unread')}
                >
                  Unread ({unreadCount})
                </Button>
                <Button
                  variant={filter === 'read' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilter('read')}
                >
                  Read
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              {unreadCount > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={markAllAsRead}
                  className="gap-2"
                >
                  <Check className="h-4 w-4" />
                  Mark All Read
                </Button>
              )}
              {notifications.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearHistory}
                  className="gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  Clear History
                </Button>
              )}
            </div>

            {/* Notifications List */}
            <ScrollArea style={{ height: maxHeight }}>
              <div className="space-y-3">
                {filteredNotifications.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <BellOff className="h-8 w-8 mx-auto mb-2" />
                    <p>
                      {filter === 'all' 
                        ? 'No notifications yet' 
                        : `No ${filter} notifications`
                      }
                    </p>
                  </div>
                ) : (
                  filteredNotifications.map((notification) => {
                    const IconComponent = getNotificationIcon(
                      notification.payload.type, 
                      notification.status
                    )
                    const iconColor = getNotificationColor(
                      notification.payload.type, 
                      notification.payload.priority
                    )

                    return (
                      <div
                        key={notification.id}
                        className={cn(
                          'flex items-start gap-3 p-3 rounded-lg border transition-colors',
                          !notification.read 
                            ? 'bg-blue-50 border-blue-200' 
                            : 'bg-gray-50 border-gray-200',
                          'hover:bg-gray-100 cursor-pointer'
                        )}
                        onClick={() => !notification.read && markAsRead(notification.id)}
                      >
                        {/* Icon */}
                        <div className={cn('mt-0.5', iconColor)}>
                          <IconComponent className="h-4 w-4" />
                        </div>

                        {/* Content */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-2">
                            <div className="flex-1">
                              <h4 className={cn(
                                'font-medium text-sm',
                                !notification.read && 'font-semibold'
                              )}>
                                {notification.payload.title}
                              </h4>
                              <p className="text-sm text-muted-foreground mt-1">
                                {notification.payload.body}
                              </p>
                            </div>
                            
                            {/* Badges */}
                            <div className="flex items-center gap-1 flex-shrink-0">
                              <Badge 
                                variant={getPriorityBadge(notification.payload.priority)}
                                className="text-xs"
                              >
                                {notification.payload.priority}
                              </Badge>
                              {!notification.read && (
                                <div className="w-2 h-2 bg-blue-600 rounded-full" />
                              )}
                            </div>
                          </div>

                          {/* Metadata */}
                          <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                            <span>
                              {formatDistanceToNow(notification.timestamp, { addSuffix: true })}
                            </span>
                            <span className="capitalize">
                              {notification.payload.type.replace('_', ' ')}
                            </span>
                            <span className="capitalize">
                              {notification.status}
                            </span>
                          </div>

                          {/* Additional Data */}
                          {notification.payload.data && (
                            <div className="mt-2 text-xs">
                              {notification.payload.data.quote_id && (
                                <span className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 rounded text-gray-600">
                                  Quote: {notification.payload.data.quote_id}
                                </span>
                              )}
                              {notification.payload.data.trip_id && (
                                <span className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 rounded text-gray-600 ml-1">
                                  Trip: {notification.payload.data.trip_id}
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  })
                )}
              </div>
            </ScrollArea>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Compact notification history for sidebars
 */
export function CompactNotificationHistory({ className }: { className?: string }) {
  return (
    <NotificationHistory 
      className={className}
      maxHeight="300px"
    />
  )
}

/**
 * Notification bell with unread count
 */
export function NotificationBell({ className }: { className?: string }) {
  const { unreadCount } = useNotificationHistory()

  return (
    <div className={cn('relative', className)}>
      <Bell className="h-5 w-5" />
      {unreadCount > 0 && (
        <Badge 
          variant="destructive" 
          className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs"
        >
          {unreadCount > 99 ? '99+' : unreadCount}
        </Badge>
      )}
    </div>
  )
}
