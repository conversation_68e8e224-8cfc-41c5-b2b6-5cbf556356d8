# Test Environment Configuration
NEXTAUTH_URL=http://localhost:3003
NEXTAUTH_SECRET=your_test_secret_key

# Test Database Configuration
DATABASE_URL="postgresql://test:test@localhost:5432/test_db"

# Test API Keys
NEXT_PUBLIC_SUPABASE_URL=your_test_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_test_supabase_anon_key

# Test User Credentials
TEST_ADMIN_EMAIL=<EMAIL>
TEST_ADMIN_PASSWORD=admin123
TEST_CUSTOMER_EMAIL=<EMAIL>
TEST_CUSTOMER_PASSWORD=customer123
TEST_AFFILIATE_EMAIL=<EMAIL>
TEST_AFFILIATE_PASSWORD=affiliate123
TEST_EVENT_MANAGER_EMAIL=<EMAIL>
TEST_EVENT_MANAGER_PASSWORD=eventmanager123

# Test Mode Flag
NODE_ENV=test
IS_TEST=true 


VITE_MAPBOX_ACCESS_TOKEN=pk.eyJ1Ijoid3dtcyIsImEiOiJjbHdrYWQ0eXAxNGM1MmptbTd4YXg2NGxqIn0.tAM-9pPFtZHoVAzuDuLkUg