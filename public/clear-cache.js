// Clear browser cache and fix infinite loops
(function() {
  console.log('Clearing browser cache and fixing infinite loops...');
  
  // Clear localStorage
  try {
    localStorage.clear();
    console.log('localStorage cleared');
  } catch (e) {
    console.warn('Could not clear localStorage:', e);
  }
  
  // Clear sessionStorage
  try {
    sessionStorage.clear();
    console.log('sessionStorage cleared');
  } catch (e) {
    console.warn('Could not clear sessionStorage:', e);
  }
  
  // Clear IndexedDB
  try {
    if ('indexedDB' in window) {
      indexedDB.databases().then(databases => {
        databases.forEach(db => {
          if (db.name) {
            indexedDB.deleteDatabase(db.name);
          }
        });
      });
      console.log('IndexedDB cleared');
    }
  } catch (e) {
    console.warn('Could not clear IndexedDB:', e);
  }
  
  // Clear service worker cache
  if ('serviceWorker' in navigator && 'caches' in window) {
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          return caches.delete(cacheName);
        })
      );
    }).then(() => {
      console.log('Service worker cache cleared');
    }).catch(e => {
      console.warn('Could not clear service worker cache:', e);
    });
  }
  
  console.log('Cache clearing complete. Reloading page...');
  
  // Reload the page after a short delay
  setTimeout(() => {
    window.location.reload(true);
  }, 1000);
})();
