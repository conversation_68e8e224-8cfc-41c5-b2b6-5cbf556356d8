// Service Worker for Transflow SaaS Platform
// Handles push notifications, caching, and offline functionality

const CACHE_NAME = 'transflow-v1'
const NOTIFICATION_CACHE = 'transflow-notifications-v1'

// Cache essential resources (only include resources that actually exist)
const STATIC_CACHE_URLS = [
  '/manifest.json'
]

// Install event - cache static resources
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker')
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('[SW] Caching static resources')
        return cache.addAll(STATIC_CACHE_URLS)
      })
      .then(() => {
        console.log('[SW] Service worker installed successfully')
        return self.skipWaiting()
      })
      .catch((error) => {
        console.error('[SW] Error during installation:', error)
      })
  )
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker')
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME && cacheName !== NOTIFICATION_CACHE) {
              console.log('[SW] Deleting old cache:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      })
      .then(() => {
        console.log('[SW] Service worker activated')
        return self.clients.claim()
      })
  )
})

// Push event - handle incoming push notifications
self.addEventListener('push', (event) => {
  console.log('[SW] Push event received:', event)
  
  if (!event.data) {
    console.log('[SW] Push event has no data')
    return
  }

  try {
    const data = event.data.json()
    console.log('[SW] Push notification data:', data)
    
    const options = createNotificationOptions(data)
    
    event.waitUntil(
      self.registration.showNotification(data.title || 'Transflow Notification', options)
        .then(() => {
          console.log('[SW] Notification displayed successfully')
          // Store notification in cache for history
          return cacheNotification(data)
        })
        .catch((error) => {
          console.error('[SW] Error showing notification:', error)
        })
    )
  } catch (error) {
    console.error('[SW] Error parsing push data:', error)
  }
})

// Notification click event - handle notification interactions
self.addEventListener('notificationclick', (event) => {
  console.log('[SW] Notification clicked:', event.notification)
  
  event.notification.close()
  
  const data = event.notification.data || {}
  const action = event.action
  
  event.waitUntil(
    handleNotificationClick(data, action)
  )
})

// Notification close event - track notification dismissals
self.addEventListener('notificationclose', (event) => {
  console.log('[SW] Notification closed:', event.notification)
  
  const data = event.notification.data || {}
  
  // Track notification dismissal
  event.waitUntil(
    trackNotificationEvent('dismissed', data)
  )
})

// Background sync event - handle offline actions
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync event:', event.tag)
  
  if (event.tag === 'notification-sync') {
    event.waitUntil(syncPendingNotifications())
  }
})

// Message event - handle messages from main thread
self.addEventListener('message', (event) => {
  console.log('[SW] Message received:', event.data)
  
  const { type, payload } = event.data
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting()
      break
    case 'GET_NOTIFICATION_PERMISSION':
      event.ports[0].postMessage({
        permission: Notification.permission
      })
      break
    case 'CLEAR_NOTIFICATIONS':
      clearAllNotifications()
      break
    default:
      console.log('[SW] Unknown message type:', type)
  }
})

// Helper function to create notification options
function createNotificationOptions(data) {
  const options = {
    body: data.body || data.message,
    icon: data.icon || '/favicon.ico',
    badge: data.badge || '/favicon.ico',
    image: data.image,
    tag: data.tag || `transflow-${Date.now()}`,
    renotify: data.renotify || false,
    requireInteraction: data.requireInteraction || false,
    silent: data.silent || false,
    timestamp: data.timestamp || Date.now(),
    data: data,
    actions: []
  }

  // Add actions based on notification type
  if (data.type === 'quote_status') {
    options.actions = [
      {
        action: 'view_quote',
        title: 'View Quote',
        icon: '/icons/view.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: '/icons/dismiss.png'
      }
    ]
  } else if (data.type === 'trip_update') {
    options.actions = [
      {
        action: 'view_trip',
        title: 'View Trip',
        icon: '/icons/trip.png'
      },
      {
        action: 'track_trip',
        title: 'Track',
        icon: '/icons/track.png'
      }
    ]
  }

  // Set priority-based styling
  if (data.priority === 'urgent') {
    options.requireInteraction = true
    options.vibrate = [200, 100, 200]
  } else if (data.priority === 'high') {
    options.vibrate = [100, 50, 100]
  }

  return options
}

// Handle notification click actions
async function handleNotificationClick(data, action) {
  console.log('[SW] Handling notification click:', { data, action })
  
  try {
    const clients = await self.clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    })
    
    let targetUrl = '/'
    
    // Determine target URL based on notification type and action
    if (action === 'view_quote' || data.type === 'quote_status') {
      targetUrl = data.quote_id 
        ? `/event-manager/events/${data.event_id || 'quotes'}#quote-${data.quote_id}`
        : '/event-manager/quotes'
    } else if (action === 'view_trip' || data.type === 'trip_update') {
      targetUrl = data.trip_id 
        ? `/event-manager/events/${data.event_id || 'trips'}#trip-${data.trip_id}`
        : '/event-manager/events'
    } else if (action === 'track_trip') {
      targetUrl = `/customer/trips/gods-view?trip=${data.trip_id}`
    }
    
    // Try to focus existing window or open new one
    const existingClient = clients.find(client => 
      client.url.includes(new URL(targetUrl, self.location.origin).pathname)
    )
    
    if (existingClient) {
      await existingClient.focus()
      existingClient.postMessage({
        type: 'NOTIFICATION_CLICKED',
        data,
        action
      })
    } else {
      await self.clients.openWindow(targetUrl)
    }
    
    // Track notification interaction
    await trackNotificationEvent('clicked', data, action)
    
  } catch (error) {
    console.error('[SW] Error handling notification click:', error)
  }
}

// Cache notification for history
async function cacheNotification(data) {
  try {
    const cache = await caches.open(NOTIFICATION_CACHE)
    const notificationRecord = {
      id: data.id || `notification-${Date.now()}`,
      timestamp: Date.now(),
      data
    }
    
    await cache.put(
      `/notifications/${notificationRecord.id}`,
      new Response(JSON.stringify(notificationRecord))
    )
    
    console.log('[SW] Notification cached successfully')
  } catch (error) {
    console.error('[SW] Error caching notification:', error)
  }
}

// Track notification events
async function trackNotificationEvent(event, data, action = null) {
  try {
    // Send tracking data to main thread
    const clients = await self.clients.matchAll()
    clients.forEach(client => {
      client.postMessage({
        type: 'NOTIFICATION_EVENT',
        event,
        data,
        action,
        timestamp: Date.now()
      })
    })
  } catch (error) {
    console.error('[SW] Error tracking notification event:', error)
  }
}

// Sync pending notifications when back online
async function syncPendingNotifications() {
  try {
    console.log('[SW] Syncing pending notifications')
    
    // Get pending notifications from cache
    const cache = await caches.open(NOTIFICATION_CACHE)
    const requests = await cache.keys()
    
    for (const request of requests) {
      if (request.url.includes('/pending/')) {
        const response = await cache.match(request)
        const notificationData = await response.json()
        
        // Try to send the notification
        await self.registration.showNotification(
          notificationData.title,
          createNotificationOptions(notificationData)
        )
        
        // Remove from pending cache
        await cache.delete(request)
      }
    }
    
    console.log('[SW] Notification sync completed')
  } catch (error) {
    console.error('[SW] Error syncing notifications:', error)
  }
}

// Clear all notifications
async function clearAllNotifications() {
  try {
    const notifications = await self.registration.getNotifications()
    notifications.forEach(notification => notification.close())
    
    // Clear notification cache
    await caches.delete(NOTIFICATION_CACHE)
    
    console.log('[SW] All notifications cleared')
  } catch (error) {
    console.error('[SW] Error clearing notifications:', error)
  }
}

console.log('[SW] Service worker script loaded')
