{"name": "transflow-saas", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test:login": "ts-node --transpile-only scripts/test-login.ts", "test:rls": "ts-node --transpile-only scripts/test-rls-policies.ts", "test:visibility": "ts-node --transpile-only scripts/test-data-visibility.ts", "create:test-users": "NODE_OPTIONS='--loader ts-node/esm' ts-node --transpile-only scripts/create-test-users.mts", "test:setup": "chmod +x scripts/test-setup.sh && ./scripts/test-setup.sh", "test:setup:cloud": "./scripts/setup-cloud-test.sh", "reorganize": "ts-node scripts/reorganize.ts", "reorganize:dry": "ts-node scripts/reorganize.ts --dry-run", "reorganize:verbose": "ts-node scripts/reorganize.ts --verbose", "reorganize:dry-verbose": "ts-node scripts/reorganize.ts --dry-run --verbose", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:chromium": "playwright test --project=chromium", "test:e2e:firefox": "playwright test --project=firefox", "test:e2e:webkit": "playwright test --project=webkit", "dev:bypass-auth": "BYPASS_AUTH=true next dev", "dev:bypass-auth:mac": "cross-env BYPASS_AUTH=true next dev", "seed:quotes": "node scripts/seed-quote-workflow.js", "test:auth": "NODE_OPTIONS='--loader ts-node/esm' ts-node --transpile-only scripts/test-auth.ts"}, "dependencies": {"@agentdeskai/browser-tools-mcp": "^1.2.0", "@agentdeskai/browser-tools-server": "^1.2.0", "@floating-ui/react": "^0.27.12", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@prisma/client": "5.9.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-hover-card": "^1.1.5", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@sentry/nextjs": "^9.10.1", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/postcss": "^4.0.3", "@tanstack/react-query": "^4.36.1", "@tanstack/react-table": "^8.20.6", "@trpc/client": "^10.45.0", "@trpc/react-query": "^10.45.0", "@trpc/server": "^10.45.0", "@types/jsonwebtoken": "^9.0.8", "@types/mapbox-gl": "^3.4.1", "@types/react-datepicker": "^6.2.0", "bcrypt": "^5.1.1", "chalk": "^4.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^2.30.0", "dotenv": "^16.5.0", "framer-motion": "^12.0.6", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "mapbox-gl": "^3.10.0", "next": "^14.2.23", "next-themes": "^0.4.4", "pg": "^8.15.6", "react": "^18", "react-datepicker": "^8.4.0", "react-day-picker": "^8.9.1", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-map-gl": "^7.1.9", "react-markdown": "^8.0.7", "recharts": "^2.15.3", "sonner": "^1.7.4", "superjson": "^1.13.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "xstate": "^4.38.3", "zod": "^3.24.1", "zustand": "^5.0.5"}, "devDependencies": {"@playwright/test": "^1.42.1", "@testing-library/react": "^16.2.0", "@types/bcrypt": "^5.0.2", "@types/glob": "^8.1.0", "@types/node": "^22.13.1", "@types/pg": "^8.11.11", "@types/react": "19.0.8", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "chalk": "^5.3.0", "glob": "^11.0.1", "postcss": "^8.5.1", "prisma": "^5.9.1", "raw-loader": "^4.0.2", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "5.7.3", "uuid": "^11.1.0"}, "ts-node": {"esm": true, "experimentalSpecifiers": true}, "description": "## Overview WWMS DIY is a comprehensive transportation management system that connects customers, affiliates, and event managers in a seamless platform for managing transportation services.", "directories": {"doc": "docs", "lib": "lib"}, "keywords": [], "author": "", "license": "ISC"}