-- Fix RLS policy for quote_affiliate_offers table
-- This allows event managers and clients to create quote offers

-- Drop existing restrictive policies
DROP POLICY IF EXISTS "Users can submit counter-offers for their company" ON public.quote_affiliate_offers;

-- Create new policy that allows authenticated users to create quote offers
CREATE POLICY "Authenticated users can create quote offers" 
ON public.quote_affiliate_offers
FOR INSERT
WITH CHECK (auth.uid() IS NOT NULL);

-- Also allow authenticated users to view offers for quotes they created
CREATE POLICY "Users can view offers for their quotes" 
ON public.quote_affiliate_offers
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM quotes 
    WHERE quotes.id = quote_affiliate_offers.quote_id 
    AND quotes.customer_id = auth.uid()
  )
);
